/**
 * 解压ps-uploader压缩包的JSX脚本
 * 在插件首次运行时自动解压ps-uploader.zip文件
 */

// 获取插件根目录
var pluginFolder = new Folder($.fileName).parent.parent;
var exeFolder = new Folder(pluginFolder + "/exe");

// 检查并解压ps-uploader压缩包
function unzipUploaderIfNeeded() {
    try {
        // 检查ps-uploader-mac.zip
        var macZipFile = new File(exeFolder + "/ps-uploader-mac.zip");
        var macFolder = new Folder(exeFolder + "/ps-uploader-mac");
        
        if (macZipFile.exists && !macFolder.exists) {
            $.writeln("发现ps-uploader-mac.zip，开始解压...");
            if (unzipFile(macZipFile, exeFolder)) {
                $.writeln("ps-uploader-mac.zip 解压成功");

                // 设置Mac版本的执行权限
                if ($.os.indexOf("Mac") !== -1) {
                    var uploaderFile = new File(exeFolder + "/ps-uploader-mac/ps-uploader");
                    if (uploaderFile.exists) {
                        var chmodCommand = 'chmod +x "' + uploaderFile.fsName + '"';
                        var result = app.system(chmodCommand);
                        if (result === 0) {
                            $.writeln("ps-uploader 执行权限设置成功");
                        } else {
                            $.writeln("ps-uploader 执行权限设置失败");
                        }
                    }
                }

                // 删除zip文件
                macZipFile.remove();
                $.writeln("已删除ps-uploader-mac.zip");
            } else {
                $.writeln("ps-uploader-mac.zip 解压失败");
            }
        }
        
        // 检查ps-uploader-win.zip
        var winZipFile = new File(exeFolder + "/ps-uploader-win.zip");
        var winFolder = new Folder(exeFolder + "/ps-uploader-win");
        
        if (winZipFile.exists && !winFolder.exists) {
            $.writeln("发现ps-uploader-win.zip，开始解压...");
            if (unzipFile(winZipFile, exeFolder)) {
                $.writeln("ps-uploader-win.zip 解压成功");
                // 删除zip文件
                winZipFile.remove();
                $.writeln("已删除ps-uploader-win.zip");
            } else {
                $.writeln("ps-uploader-win.zip 解压失败");
            }
        }
        
        return true;
    } catch (e) {
        $.writeln("解压ps-uploader时出错: " + e.toString());
        return false;
    }
}

// 解压zip文件的函数
function unzipFile(zipFile, targetFolder) {
    try {
        // 在Mac上使用unzip命令
        if ($.os.indexOf("Mac") !== -1) {
            var command = 'cd "' + targetFolder.fsName + '" && unzip -o "' + zipFile.fsName + '"';
            $.writeln("执行解压命令: " + command);
            var result = app.system(command);
            $.writeln("解压命令结果: " + result);
            return result === 0;
        }
        // 在Windows上使用PowerShell
        else if ($.os.indexOf("Windows") !== -1) {
            var command = 'powershell -command "Expand-Archive -Path \\"' + zipFile.fsName + '\\" -DestinationPath \\"' + targetFolder.fsName + '\\" -Force"';
            $.writeln("执行解压命令: " + command);
            var result = app.system(command);
            $.writeln("解压命令结果: " + result);
            return result === 0;
        }

        $.writeln("不支持的操作系统: " + $.os);
        return false;
    } catch (e) {
        $.writeln("解压文件时出错: " + e.toString());
        return false;
    }
}

// 执行解压
unzipUploaderIfNeeded();
