// 检查文件格式是否为有效的图像格式
function checkFileFormat(filePath) {
  try {
    var file = new File(filePath);
    if (!file.exists) {
      return "File does not exist";
    }
    
    // 尝试打开文件
    if (!file.open("r")) {
      return "Cannot open file for reading";
    }
    
    // 读取文件头部字节以识别文件格式
    var header = "";
    var bytes = [];
    for (var i = 0; i < 12 && !file.eof; i++) {
      var byte = file.readch();
      bytes.push(byte.charCodeAt(0));
      header += byte;
    }
    file.close();
    
    // 检查文件头部字节以识别常见图像格式
    var formatInfo = "";
    
    // JPEG: FF D8 FF
    if (bytes[0] === 0xFF && bytes[1] === 0xD8 && bytes[2] === 0xFF) {
      formatInfo = "Valid JPEG format";
    }
    // PNG: 89 50 4E 47 0D 0A 1A 0A
    else if (bytes[0] === 0x89 && bytes[1] === 0x50 && bytes[2] === 0x4E && bytes[3] === 0x47 &&
             bytes[4] === 0x0D && bytes[5] === 0x0A && bytes[6] === 0x1A && bytes[7] === 0x0A) {
      formatInfo = "Valid PNG format";
    }
    // GIF: 47 49 46 38
    else if (bytes[0] === 0x47 && bytes[1] === 0x49 && bytes[2] === 0x46 && bytes[3] === 0x38) {
      formatInfo = "Valid GIF format";
    }
    // PSD: 38 42 50 53
    else if (bytes[0] === 0x38 && bytes[1] === 0x42 && bytes[2] === 0x50 && bytes[3] === 0x53) {
      formatInfo = "Valid PSD format";
    }
    // TIFF: 49 49 2A 00 or 4D 4D 00 2A
    else if ((bytes[0] === 0x49 && bytes[1] === 0x49 && bytes[2] === 0x2A && bytes[3] === 0x00) ||
             (bytes[0] === 0x4D && bytes[1] === 0x4D && bytes[2] === 0x00 && bytes[3] === 0x2A)) {
      formatInfo = "Valid TIFF format";
    }
    else {
      formatInfo = "Unknown or unsupported format";
    }
    
    // 返回文件格式信息和头部字节
    return formatInfo + " | Header bytes: " + bytes.join(" ");
    
  } catch (e) {
    return "Error checking file format: " + e.toString();
  }
}
