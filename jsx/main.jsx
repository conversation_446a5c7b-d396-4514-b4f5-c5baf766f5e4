// 首次运行时解压ps-uploader压缩包
try {
  $.evalFile(new File($.fileName).parent + "/unzip-uploader.jsx");
} catch (e) {
  $.writeln("解压uploader脚本执行失败: " + e.toString());
}

//  在Photoshop中放置（插入）一个图像文件
function placeFile (file) {
  var desc = new ActionDescriptor()
  desc.putPath(charIDToTypeID('null'), file)

  // 设置插入图像的一些参数
  desc.putEnumerated(
    charIDToTypeID('FTcs'),
    charIDToTypeID('QCSt'),
    charIDToTypeID('Qcsa')
  )
  var offsetDesc = new ActionDescriptor()

  //图像插入的位置，水平-'Hrzn' 垂直-'Vrtc',0.0为坐标值，目前是放在中心位置
  offsetDesc.putUnitDouble(charIDToTypeID('Hrzn'), charIDToTypeID('#Pxl'), 0.0)
  offsetDesc.putUnitDouble(charIDToTypeID('Vrtc'), charIDToTypeID('#Pxl'), 0.0)

  desc.putObject(charIDToTypeID('Ofst'), charIDToTypeID('Ofst'), offsetDesc)

  //执行这个操作，DialogModes.NO表示在执行这操作时不显示任何对话框。
  executeAction(charIDToTypeID('Plc '), desc, DialogModes.NO)
  return 'success'
}

// 打开文件
function openDocument (filePath) {
  try {
    var file = new File(filePath)
    if (file.exists) {
      return placeFile(file)
    } else {
      return 'no-exist'
    }
  } catch (e) {
    return 'error'
  }
}
// 拷贝图层
function copeActiveLayers () {
  try {
    var activeDom = app.activeDocument
  } catch (e) {
    alert(e)
    return 'failed'
  }
  if (getSelectedLayersIdx()) {
    activeDom.selection.copy()
    activeDom.paste()
  } else {
    alert('请选择要合并上传的图层')
    return 'failed'
  }
}

// 转换智能对象
function converToSmartObject () {
  try {
    var idnewPlacedLayer = stringIDToTypeID('newPlacedLayer')
    executeAction(idnewPlacedLayer, undefined, DialogModes.NO)
    app.activeDocument.activeLayer.visible = false
    return 'success'
  } catch (e) {
    return 'failt'
  }
}

// function saveSmartObjectAsPng (fileUrl) {
//   var layer = app.activeDocument.activeLayer
//   var layerName = app.activeDocument.activeLayer.name
//   var docName = app.activeDocument.name.replace(/\.[^\.]+$/, '')
//   var timeString= +new Date()
//   var saveName = File(
//     fileUrl + '/' + timeString + '_' + docName + '_' + layerName + '_SmartObjectLayer.png'
//   )
//   if (layer.kind == 'LayerKind.SMARTOBJECT') {
//     var sTT = stringIDToTypeID
//     ;(dsc = new ActionDescriptor()).putPath(sTT('null'), saveName)
//     executeAction(sTT('placedLayerExportContents'), dsc)
//     app.activeDocument.activeLayer.remove()
//     return timeString + '_' + docName + '_' + layerName + '_SmartObjectLayer.png'
//   } else {
//     return 'failed'
//   }
// }

// function SavePng(saveFile) {
//   var pngSaveOptions = new PNGSaveOptions();
//   pngSaveOptions.compression = 0;
//   pngSaveOptions.interlaced = false;
//   app.activeDocument.saveAs(saveFile, pngSaveOptions, true, Extension.LOWERCASE);
// }

// 保存智能对象为PSB
function saveSmartObjectAsPSB (fileUrl) {
  var layer = app.activeDocument.activeLayer
  var layerName = app.activeDocument.activeLayer.name
  var docName = app.activeDocument.name.replace(/\.[^\.]+$/, '')
  var timeString = +new Date()
  var saveName = File(
    fileUrl +
      '/' +
      timeString +
      '_' +
      docName +
      '_' +
      layerName +
      '_SmartObjectLayer.psd'
  )
  if (layer.kind == 'LayerKind.SMARTOBJECT') {
    var sTT = stringIDToTypeID
    ;(dsc = new ActionDescriptor()).putPath(sTT('null'), saveName)
    executeAction(sTT('placedLayerExportContents'), dsc)
    app.activeDocument.activeLayer.remove()
    return (
      timeString + '_' + docName + '_' + layerName + '_SmartObjectLayer.psd'
    )
  } else {
    return 'failed'
  }
}

// function saveSmartObjectAsPng (fileUrl) {
//   try {
//     var timeString = +new Date()
//     app.activeDocument.activeLayer.name = timeString
//     quick_export_png(fileUrl, app.activeDocument.activeLayer)
//     return 'success'
//   } catch (e) {
//     return 'failed'
//   }
// }

// function quick_export_png (path, layer) {
//   try {
//     if (layer == undefined) layer = false
//     var d = new ActionDescriptor()
//     var r = new ActionReference()
//     r.putEnumerated(
//       stringIDToTypeID('layer'),
//       stringIDToTypeID('ordinal'),
//       stringIDToTypeID('targetEnum')
//     )
//     d.putReference(stringIDToTypeID('null'), r)
//     d.putString(stringIDToTypeID('fileType'), 'png')
//     d.putInteger(stringIDToTypeID('quality'), 32)
//     d.putInteger(stringIDToTypeID('metadata'), 0)
//     d.putString(stringIDToTypeID('destFolder'), path)
//     d.putBoolean(stringIDToTypeID('sRGB'), true)
//     d.putBoolean(stringIDToTypeID('openWindow'), false)
//     executeAction(
//       stringIDToTypeID(
//         layer
//           ? 'exportSelectionAsFileTypePressed'
//           : 'exportDocumentAsFileTypePressed'
//       ),
//       d,
//       DialogModes.NO
//     )
//   } catch (e) {
//     throw e
//   }
// }

// 获取选中的图层ID
function getSelectedLayersIdx () {
  var selectedLayers = new Array()
  var backGroundCounter = 1
  if (activeDocument.artLayers.length > 0) {
    backGroundCounter = activeDocument.artLayers[
      activeDocument.artLayers.length - 1
    ].isBackgroundLayer
      ? 0
      : 1
  }
  var ref = new ActionReference()
  ref.putProperty(charIDToTypeID('Prpr'), stringIDToTypeID('targetLayers'))
  ref.putEnumerated(
    charIDToTypeID('Dcmn'),
    charIDToTypeID('Ordn'),
    charIDToTypeID('Trgt')
  )
  var desc = executeActionGet(ref)
  if (desc.hasKey(stringIDToTypeID('targetLayers'))) {
    desc = desc.getList(stringIDToTypeID('targetLayers'))
    var c = desc.count
    var selectedLayers = new Array()
    for (var i = 0; i < c; i++) {
      selectedLayers.push(desc.getReference(i).getIndex() + backGroundCounter)
    }
    if (app.version.match(/^\d+/) > 15) return selectedLayers
  } else {
    if (app.version.match(/^\d+/) > 15) return selectedLayers
    var ref = new ActionReference()
    ref.putProperty(charIDToTypeID('Prpr'), charIDToTypeID('ItmI'))
    ref.putEnumerated(
      charIDToTypeID('Lyr '),
      charIDToTypeID('Ordn'),
      charIDToTypeID('Trgt')
    )
    if (!backGroundCounter) {
      selectedLayers.push(
        executeActionGet(ref).getInteger(charIDToTypeID('ItmI')) - 1
      )
    } else {
      selectedLayers.push(
        executeActionGet(ref).getInteger(charIDToTypeID('ItmI'))
      )
    }
    var vis = app.activeDocument.activeLayer.visible
    if (vis == true) app.activeDocument.activeLayer.visible = false
    var desc9 = new ActionDescriptor()
    var list9 = new ActionList()
    var ref9 = new ActionReference()
    ref9.putEnumerated(
      charIDToTypeID('Lyr '),
      charIDToTypeID('Ordn'),
      charIDToTypeID('Trgt')
    )
    list9.putReference(ref9)
    desc9.putList(charIDToTypeID('null'), list9)
    executeAction(charIDToTypeID('Shw '), desc9, DialogModes.NO)
    if (app.activeDocument.activeLayer.visible == false) selectedLayers.shift()
    app.activeDocument.activeLayer.visible = vis
  }
  return selectedLayers
}

// 删除元数据
function deleteDocumentAncestorsMetadata () {
  whatApp = String(app.name) //String version of the app name
  if (whatApp.search('Photoshop') > 0) {
    //Check for photoshop specifically, or this will cause errors
    //Function Scrubs Document Ancestors from Files
    if (!documents.length) {
      return
    }
    if (ExternalObject.AdobeXMPScript == undefined)
      ExternalObject.AdobeXMPScript = new ExternalObject('lib:AdobeXMPScript')
    var xmp = new XMPMeta(activeDocument.xmpMetadata.rawData)
    // Begone foul Document Ancestors!
    xmp.deleteProperty(XMPConst.NS_PHOTOSHOP, 'DocumentAncestors')
    app.activeDocument.xmpMetadata.rawData = xmp.serialize()
    return
  } else {
    return
  }
}

// 把动作组删除
function unLoadAction (aSet) {
  try {
    var desc = new ActionDescriptor()
    var ref = new ActionReference()
    ref.putName(charIDToTypeID('ASet'), decodeURI(aSet))
    desc.putReference(charIDToTypeID('null'), ref)
    executeAction(charIDToTypeID('Dlt '), desc, DialogModes.NO)
    return 'success'
  } catch (e) {
    return 'error'
  }
}

// 执行动作组
function todoAction (prjPath, actionName, actionSets) {
  try {
    if (!documents.length || !getSelectedLayersIdx().length) {
      return 'init'
    }
    app.load(File(prjPath + '/atn/netease.atn'))
    app.doAction(actionName, actionSets)
    return 'success'
  } catch (e) {
    return 'error'
  }
}

function deleteDocumentAncestorsMetadata2 () {
  if (String(app.name).search('Photoshop') > 0) {
    if (!documents.length) {
      alert(
        'There are no open documents. Please open a file to run this script.'
      )
      return
    }

    if (ExternalObject.AdobeXMPScript == undefined)
      ExternalObject.AdobeXMPScript = new ExternalObject('lib:AdobeXMPScript')

    var xmp = new XMPMeta(activeDocument.xmpMetadata.rawData)
    xmp.deleteProperty(XMPConst.NS_PHOTOSHOP, 'DocumentAncestors')
    app.activeDocument.xmpMetadata.rawData = xmp.serialize()

    clearDocumentAncestorsForAllLayers(app.activeDocument)

    if (app.activeDocument !== mainDocument) {
      app.activeDocument.close(SaveOptions.SAVECHANGES)
    } else {
      app.activeDocument.save()
    }
  }
}

function clearDocumentAncestorsForAllLayers (doc) {
  try {
    if (doc == undefined) {
      return
    }

    for (var i = 0; i < doc.layers.length; i++) {
      var curLayer = doc.layers[i]
      if (curLayer.typename != 'ArtLayer') {
        clearDocumentAncestorsForAllLayers(curLayer)
        continue
      }

      if (curLayer.kind == 'LayerKind.SMARTOBJECT') {
        app.activeDocument.activeLayer = curLayer

        var idplacedLayerEditContents = stringIDToTypeID(
          'placedLayerEditContents'
        )
        var actionDescriptor = new ActionDescriptor()
        executeAction(
          idplacedLayerEditContents,
          actionDescriptor,
          DialogModes.NO
        )

        if (app.activeDocument.activeLayer == curLayer) {
          continue
        }
        deleteDocumentAncestorsMetadata2()
        layerSetStr += '\n' + curLayer.name
      }
    }
  } catch (e) {
    alert('Layer clean fail.name=' + doc + ';e=' + e)
  }
}

function psAlert (cont) {
  alert(cont)
}

var layerSetStr = ''
var mainDocument = app.activeDocument
// 深度清除元数据
function psDeepCleanerMetaData () {
  mainDocument = app.activeDocument
  try {
    if (confirm('该素材文件较大，建议深度压缩\n压缩过程有二次操作，依次确认即可')) {
      deleteDocumentAncestorsMetadata2()
      psAlert('完成文件清理\n对Photoshop文档"'+ mainDocument.name +'"完成缓存清理')
      return 'success'
    } else {
      return 'failed'
    }
  } catch (e) {
    return 'failed'
  }
}

function clearDocMetaData () {
  mainDocument = app.activeDocument
  deleteDocumentAncestorsMetadata2()
}
