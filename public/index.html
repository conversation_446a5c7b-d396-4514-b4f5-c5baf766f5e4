<!DOCTYPE html>
<html lang="zh" style="width: 100%; height: 100%; margin: 0; padding: 0; overflow: hidden" class="spectrum spectrum--medium spectrum--dark" dir="ltr">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= htmlWebpackPlugin.options.title %></title>
    <!-- Google Analytics -->
    <!-- <script>
      (function (i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r; i[r] = i[r] || function () {
          (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date(); a = s.createElement(o),
          m = s.getElementsByTagName(o)[0]; a.async = 1; a.src = g; m.parentNode.insertBefore(a, m)
      })(window, document, 'script', 'https://www.google-analytics.com/analytics.js', 'ga');

      ga('create', '<%= VUE_APP_GA_ID %>', 'auto');
      ga('send', 'pageview');

    </script> -->
    <!-- End Google Analytics -->
  </head>
  <body style="width: 100%; height: 100%; margin: 0; padding: 0; overflow: hidden">
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <script src="<%= BASE_URL %>CSInterface.js"></script>
    <script>
      var custom_node = {
        path: require('path'),
        fs: require('fs'),
        request: require('request'),
        cp: require('child_process'),
        psd: require('psd'),
        util: require('util'),
      }
    </script>
    <!-- built files will be auto injected -->
  </body>
</html>
