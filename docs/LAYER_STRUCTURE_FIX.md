# 图层结构保持修复

## 问题描述

原有的 `$csCompose()` 和 `$csSave()` 函数存在以下问题：
1. **图层合并问题**: 会把选中的图层合并成一个图层
2. **图层选择限制**: 默认只会选中一个图层
3. **图层结构丢失**: 上传后丢失原有的图层结构

## 根本原因

### 1. 动作组导致图层合并
```javascript
// 问题代码
cs.evalScript(`todoAction("${prjPath}", "export", "netease")`, d => {
  // 这个动作组会执行合并图层的操作
})
```

### 2. 智能对象导出逻辑
```javascript
// 问题代码
if (layer.kind == 'LayerKind.SMARTOBJECT') {
  executeAction(sTT('placedLayerExportContents'), dsc)
  app.activeDocument.activeLayer.remove()  // 删除原图层
}
```

## 解决方案

### 1. 修改 `$csCompose()` 函数
**移除图层合并的动作组**:
```javascript
// 原逻辑（会合并图层）
cs.evalScript(`todoAction("${prjPath}", "export", "netease")`, d => {
  // 执行会合并图层的动作
})

// 新逻辑（保持图层结构）
cs.evalScript('deleteDocumentAncestorsMetadata()', () => {
  // 只清理元数据，不执行合并操作
  resolve('文档准备成功')
})
```

### 2. 修改 `saveSmartObjectAsPSB()` 函数
**保存完整文档而不是单个图层**:
```javascript
// 原逻辑（只保存单个智能对象层）
if (layer.kind == 'LayerKind.SMARTOBJECT') {
  executeAction(sTT('placedLayerExportContents'), dsc)
  app.activeDocument.activeLayer.remove()
}

// 新逻辑（保存完整文档）
var duplicateDoc = doc.duplicate(docName + '_temp')
var psdOptions = new PhotoshopSaveOptions()
psdOptions.layers = true  // 保存所有图层
duplicateDoc.saveAs(savePath, psdOptions)
duplicateDoc.close(SaveOptions.DONOTSAVECHANGES)
```

### 3. 修改 `$csSave()` 函数
**移除不必要的动作组清理**:
```javascript
// 原逻辑（包含动作组操作）
cs.evalScript(`todoAction("${prjPath}", "delete_record", "netease")`, d => {
  cs.evalScript('unLoadAction("netease")')
})

// 新逻辑（直接保存）
// 移除动作组操作，直接返回保存结果
resolve(d3)
```

## 修改后的行为

### 1. 图层结构完全保持
- ✅ 所有图层都会被保存
- ✅ 图层顺序保持不变
- ✅ 图层效果和样式保持
- ✅ 图层组结构保持
- ✅ 隐藏图层也会被保存

### 2. 文档完整性
- ✅ 保存整个文档的副本
- ✅ 包含所有通道信息
- ✅ 保持颜色配置文件
- ✅ 保存注释和元数据

### 3. 原文档不受影响
- ✅ 不修改原始文档
- ✅ 不删除任何图层
- ✅ 不改变图层选择状态
- ✅ 不执行任何破坏性操作

## 技术细节

### PSD保存选项配置
```javascript
var psdOptions = new PhotoshopSaveOptions()
psdOptions.embedColorProfile = true  // 嵌入颜色配置文件
psdOptions.layers = true             // 保存图层结构
psdOptions.annotations = true        // 保存注释
psdOptions.alphaChannels = true      // 保存Alpha通道
psdOptions.spotColors = true         // 保存专色
```

### 文档副本机制
```javascript
// 创建文档副本，避免影响原文档
var duplicateDoc = doc.duplicate(docName + '_temp')
// 保存副本
duplicateDoc.saveAs(savePath, psdOptions)
// 关闭副本，不保存更改
duplicateDoc.close(SaveOptions.DONOTSAVECHANGES)
```

## 验证方法

### 1. 图层结构验证
1. 在PS中创建多图层文档
2. 包含不同类型的图层（文字、形状、调整层等）
3. 创建图层组
4. 执行上传操作
5. 检查生成的PSD文件是否保持完整结构

### 2. 原文档完整性验证
1. 执行上传前记录图层状态
2. 执行上传操作
3. 验证原文档图层结构未被修改
4. 验证没有图层被删除或合并

### 3. 预览图准确性验证
1. 复杂图层结构的文档
2. 包含特效的图层
3. 半透明图层
4. 验证预览图是否反映完整的视觉效果

## 兼容性说明

### 保持的兼容性
- ✅ 函数调用方式不变
- ✅ 返回值格式不变
- ✅ 错误处理机制保持
- ✅ 日志记录格式保持

### 改进的功能
- ✅ 更完整的文档保存
- ✅ 更准确的预览图
- ✅ 更好的错误处理
- ✅ 更详细的日志信息

## 注意事项

1. **文件大小**: 完整文档可能比单层文件更大
2. **处理时间**: 复杂文档需要更多处理时间
3. **内存使用**: 文档副本会占用临时内存
4. **磁盘空间**: 确保有足够空间保存完整文档

## 测试建议

1. **简单文档**: 2-3个图层的基础测试
2. **复杂文档**: 10+图层，包含组和效果
3. **大型文档**: 高分辨率，多图层文档
4. **特殊情况**: 智能对象、调整层、蒙版等
