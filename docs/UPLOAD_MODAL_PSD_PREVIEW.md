# 上传模态框PSD预览功能

## 功能概述

上传模态框现在支持直接读取当前打开的PSD文件内容并生成预览图，让用户在上传前能够清楚地看到文件内容。

## 功能特性

1. **自动检测当前文档**：模态框打开时自动检测当前活动的PSD文档
2. **实时预览生成**：直接读取PSD文件内容并生成预览图
3. **智能文件名提取**：自动从文档路径提取文件名并填入输入框
4. **加载状态显示**：在生成预览时显示加载动画和进度提示
5. **错误处理**：当无法读取文件时显示友好的错误信息
6. **多种状态支持**：支持加载中、预览成功、预览失败、无文档等多种状态

## 工作流程

1. 用户在Photoshop中打开PSD文件
2. 点击上传按钮打开上传模态框
3. 模态框自动检测当前活动文档
4. 如果是PSD文件，自动生成预览图并显示
5. 自动填入文件名（去除扩展名）
6. 用户可以修改文件名并选择上传位置
7. 点击上传完成文件上传

## 界面状态

### 1. 默认状态
- 显示占位符图标和"当前文档预览"文字
- 当没有打开文档或文档不是PSD格式时显示

### 2. 加载状态
- 显示旋转的加载动画
- 显示"正在读取PSD文件内容..."提示文字
- 在生成预览图过程中显示

### 3. 预览成功状态
- 显示生成的PSD预览图
- 预览图自适应容器大小，保持宽高比
- 支持查看完整的文档内容

### 4. 错误状态
- 显示错误图标和具体错误信息
- 常见错误：
  - "请先打开一个PSD文件"
  - "当前文档不是PSD文件"
  - "无法读取当前文档"

## 技术实现

### 核心文件
- `src/pages/muse/components/upload-modal.vue` - 上传模态框组件

### 主要方法
- `loadCurrentDocumentPreview()` - 加载当前文档预览
- `getCurrentDocumentPath()` - 获取当前活动文档路径
- `isPSDFile(filePath)` - 检查是否为PSD文件
- `getFileNameFromPath(filePath)` - 从路径提取文件名

### 依赖
- `getPSDThumbnail` from `@/utils/fs/index.js` - PSD缩略图生成
- `CSInterface` - Adobe CEP接口，用于获取当前文档信息

## 样式特性

- **响应式设计**：预览容器高度为200px，自适应宽度
- **深色主题**：与整体UI风格保持一致
- **平滑动画**：加载状态使用CSS动画，视觉效果流畅
- **状态指示**：不同状态使用不同的图标和颜色

## 使用方式

1. 在Photoshop中打开要上传的PSD文件
2. 在素材库界面点击上传按钮
3. 上传模态框会自动显示当前文档的预览
4. 确认预览内容无误后，输入文件名
5. 选择上传位置
6. 点击"开始上传"完成上传

## 错误处理

- **文档检测失败**：提示用户先打开PSD文件
- **文件格式错误**：提示当前文档不是PSD文件
- **读取权限问题**：显示无法读取文档的错误信息
- **PSD解析失败**：显示文件损坏或格式不支持的提示

## 性能优化

- **异步处理**：预览生成过程不会阻塞UI界面
- **错误恢复**：预览失败时不影响其他功能的正常使用
- **资源清理**：临时生成的预览文件会被适当管理

## 注意事项

1. 需要在Adobe CEP环境中运行
2. 需要当前有活动的PSD文档
3. 大型PSD文件可能需要较长的处理时间
4. 功能依赖现有的PSD处理工具函数

## 未来改进

1. 支持预览图缓存，避免重复生成
2. 添加预览图质量设置选项
3. 支持预览图的缩放和拖拽功能
4. 添加预览图的导出功能
