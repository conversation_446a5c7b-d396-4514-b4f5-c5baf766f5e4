# PSD文件预览功能

## 功能概述

现在文件预览组件支持直接读取PSD文件内容并生成预览图，无需依赖服务器端的缩略图。

## 功能特性

1. **自动检测PSD文件**：当文件扩展名为PSD或PSB时，自动启用PSD预览功能
2. **直接读取文件内容**：使用Adobe CEP的PSD库直接读取PSD文件内容
3. **生成临时预览图**：将PSD文件转换为PNG格式的预览图
4. **加载状态显示**：在读取PSD文件时显示加载动画和进度提示
5. **错误处理**：当无法读取PSD文件时显示友好的错误信息
6. **自动清理**：组件销毁时自动清理临时生成的预览文件

## 工作流程

1. 用户点击PSD文件进行预览
2. 检测到PSD文件且没有有效的预览图时，启动PSD读取流程
3. 如果文件有下载链接，先下载到本地临时目录
4. 使用PSD库读取文件并生成PNG预览图
5. 在预览组件中显示生成的预览图
6. 组件关闭时清理临时文件

## 技术实现

### 核心文件

- `src/pages/components/file-preview/index.vue` - 文件预览组件
- `src/utils/psd-preview.js` - PSD预览处理工具

### 主要函数

- `generatePSDPreview(item)` - 生成PSD预览图
- `cleanupPSDPreview(previewPath)` - 清理单个预览文件
- `cleanupAllPSDPreviews()` - 清理所有临时预览文件

### 依赖

- `custom_node.psd` - Adobe CEP的PSD处理库
- `custom_node.fs` - 文件系统操作
- 现有的文件下载功能

## 使用方式

功能已集成到现有的文件预览流程中，用户无需额外操作：

1. 在素材库中点击PSD文件
2. 如果文件没有预览图，系统会自动尝试读取PSD内容
3. 显示加载状态直到预览图生成完成
4. 预览图生成后可以正常缩放、拖拽等操作

## 错误处理

- 文件下载失败：显示"无法读取PSD文件内容"
- PSD文件损坏：显示"无法读取PSD文件内容"
- 权限问题：显示相应错误信息
- 网络问题：显示下载失败信息

## 性能优化

- 临时文件缓存：相同文件的预览图会被缓存，避免重复生成
- 自动清理：组件销毁时自动清理临时文件，避免磁盘空间浪费
- 异步处理：PSD读取过程不会阻塞UI界面

## 注意事项

1. 需要Adobe CEP环境支持
2. 大型PSD文件可能需要较长的处理时间
3. 临时文件存储在用户数据目录的`temp_psd_preview`文件夹中
4. 功能依赖现有的文件下载机制

## 未来改进

1. 支持PSD图层预览
2. 添加预览图质量设置
3. 支持更多Adobe文件格式（AI、INDD等）
4. 添加预览图缓存管理界面
