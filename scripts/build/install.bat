@echo off
REM === [DubhePlugin] Windows post-install: unzip uploader ===

set "EXE_DIR=%~dp0"
set "ZIP_FILE=%EXE_DIR%ps-uploader-win.zip"
set "UNZIP_DIR=%EXE_DIR%ps-uploader-win"

if not exist "%ZIP_FILE%" (
    echo Skip: not found %ZIP_FILE%
    exit /b 0
)

if exist "%UNZIP_DIR%" (
    rmdir /s /q "%UNZIP_DIR%"
)

echo Unzipping %ZIP_FILE% to %UNZIP_DIR% ...
powershell -NoProfile -Command "try { Expand-Archive -LiteralPath '%ZIP_FILE%' -DestinationPath '%UNZIP_DIR%' -Force; Write-Host 'Unzip done.' } catch { Write-Host 'Error: unzip failed.' }"

echo === [DubhePlugin] Windows post-install script done ===
