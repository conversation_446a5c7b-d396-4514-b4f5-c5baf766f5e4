#!/bin/bash

# CEP 插件 exe 目录（注意路径有空格）
PKG_NAME="${package_name}"
EXT_DIR="/Library/Application Support/Adobe/CEP/extensions/com.netease.library.game/exe"

echo "插件 exe 目录：$EXT_DIR"

# 解压 mac 版本
MAC_ZIP="$EXT_DIR/ps-uploader-mac.zip"
if [[ -f "$MAC_ZIP" ]]; then
    echo "正在解压 $MAC_ZIP 到 $EXT_DIR/ps-uploader-mac ..."
    unzip -o "$MAC_ZIP" -d "$EXT_DIR/ps-uploader-mac"
    if [[ $? -ne 0 ]]; then
        echo "错误：解压 $MAC_ZIP 失败！"
        exit 1
    fi
    echo "解压完成。"

    echo "设置可执行权限..."
    chmod +x "$EXT_DIR/ps-uploader-mac/ps-uploader"
else
    echo "跳过：未找到 $MAC_ZIP"
fi

# 解压 win 版本（如果需要一起带）
WIN_ZIP="$EXT_DIR/ps-uploader-win.zip"
if [[ -f "$WIN_ZIP" ]]; then
    echo "正在解压 $WIN_ZIP 到 $EXT_DIR/ps-uploader-win ..."
    unzip -o "$WIN_ZIP" -d "$EXT_DIR/ps-uploader-win"
    echo "解压完成。"
fi

echo "=== [DubhePlugin] macOS post-install 脚本完成 ==="
