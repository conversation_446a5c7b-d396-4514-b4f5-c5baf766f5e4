<project>
    <shortName>DubhePlugin</shortName>
    <fullName>天枢插件-正式</fullName>
    <version>3.0.2</version>
    <logoImage>./logo.png</logoImage>
    <defaultLanguage>zh_CN</defaultLanguage>
    <componentList>
        <component>
            <name>default</name>
            <description>Default Component</description>
            <canBeEdited>0</canBeEdited>
            <selected>1</selected>
            <show>1</show>
            <folderList>
                <folder>
                    <description>Program Files</description>
                    <destination>${installdir}</destination>
                    <name>programfiles</name>
                    <platforms>all</platforms>
                    <shortcutList>
                        <shortcut>
                            <comment>Uninstall</comment>
                            <exec>${installdir}/${uninstallerName}</exec>
                            <icon></icon>
                            <name>Uninstall ${product_fullname}</name>
                            <path>${installdir}</path>
                            <platforms>all</platforms>
                            <runAsAdmin>0</runAsAdmin>
                            <runInTerminal>0</runInTerminal>
                            <windowsExec>${installdir}/${uninstallerName}.exe</windowsExec>
                            <windowsExecArgs></windowsExecArgs>
                            <windowsIcon></windowsIcon>
                            <windowsPath>${installdir}</windowsPath>
                        </shortcut>
                    </shortcutList>
                </folder>
                <folder>
                    <description>Program Files</description>
                    <destination>${installdir}</destination>
                    <name>programfileswindows</name>
                    <platforms>windows</platforms>
                    <distributionFileList>
                        <distributionFile>
                            <origin>./install.bat</origin>
                        </distributionFile>
                    </distributionFileList>
                </folder>
                <folder>
                    <description>Program Files</description>
                    <destination>${installdir}</destination>
                    <name>programfileswindows64</name>
                    <platforms>windows-x64</platforms>
                </folder>
                <folder>
                    <description>Program Files</description>
                    <destination>${installdir}</destination>
                    <name>programfilesosx</name>
                    <platforms>osx</platforms>
                    <distributionFileList>
                        <distributionFile>
                            <origin>./install.sh</origin>
                        </distributionFile>
                    </distributionFileList>
                </folder>
                <folder>
                    <description>PS extension</description>
                    <destination>/Library/Application Support/Adobe/CEP/extensions</destination>
                    <name>extension</name>
                    <platforms>osx</platforms>
                    <distributionFileList>
                        <distributionDirectory>
                            <origin>${plugin_folder}</origin>
                        </distributionDirectory>
                    </distributionFileList>
                </folder>
                <folder>
                    <description>PS extension</description>
                    <destination>${windows_folder_program_files_common}/Adobe/CEP/extensions</destination>
                    <name>extentiosn</name>
                    <platforms>windows</platforms>
                    <distributionFileList>
                        <distributionDirectory>
                            <origin>${plugin_folder}</origin>
                        </distributionDirectory>
                    </distributionFileList>
                </folder>
                <folder>
                    <description>PS extension exe</description>
                    <destination>${windows_folder_program_files_common}/Adobe/CEP/extensions/com.netease.library.game/exe</destination>
                    <name>extension_win_exe</name>
                    <platforms>windows</platforms>
                    <distributionFileList>
                        <distributionFile>
                            <origin>./install.bat</origin>
                        </distributionFile>
                    </distributionFileList>
                </folder>
            </folderList>
            <startMenuShortcutList>
                <startMenuShortcut>
                    <comment>Uninstall ${product_fullname}</comment>
                    <name>Uninstall ${product_fullname}</name>
                    <runAsAdmin>0</runAsAdmin>
                    <runInTerminal>0</runInTerminal>
                    <windowsExec>${installdir}/${uninstallerName}.exe</windowsExec>
                    <windowsExecArgs></windowsExecArgs>
                    <windowsIcon></windowsIcon>
                    <windowsPath>${installdir}/</windowsPath>
                </startMenuShortcut>
            </startMenuShortcutList>
        </component>
    </componentList>
    <showPostInstallationScriptResult>0</showPostInstallationScriptResult>
    <readyToInstallActionList>
        <deleteFile>
            <path>/Library/Application Support/Adobe/CEP/extensions/${package_name}/</path>
        </deleteFile>
    </readyToInstallActionList>
    <postInstallationActionList>
        <registrySet>
            <key>HKEY_CURRENT_USER\Software\Adobe\CSXS.7</key>
            <name>PlayerDebugMode</name>
            <type>REG_SZ</type>
            <value>1</value>
        </registrySet>
        <registrySet>
            <key>HKEY_CURRENT_USER\Software\Adobe\CSXS.8</key>
            <name>PlayerDebugMode</name>
            <type>REG_SZ</type>
            <value>1</value>
        </registrySet>
        <registrySet>
            <key>HKEY_CURRENT_USER\Software\Adobe\CSXS.9</key>
            <name>PlayerDebugMode</name>
            <type>REG_SZ</type>
            <value>1</value>
        </registrySet>
        <registrySet>
            <key>HKEY_CURRENT_USER\Software\Adobe\CSXS.10</key>
            <name>PlayerDebugMode</name>
            <type>REG_SZ</type>
            <value>1</value>
        </registrySet>
        <registrySet>
            <key>HKEY_CURRENT_USER\Software\Adobe\CSXS.11</key>
            <name>PlayerDebugMode</name>
            <type>REG_SZ</type>
            <value>1</value>
        </registrySet>
        <registrySet>
            <key>HKEY_CURRENT_USER\Software\Adobe\CSXS.12</key>
            <name>PlayerDebugMode</name>
            <type>REG_SZ</type>
            <value>1</value>
        </registrySet>
        <kill>
            <name>ps-uploader.exe</name>
            <showMessageOnError>0</showMessageOnError>
        </kill>
    </postInstallationActionList>
    <createOsxBundleDmg>1</createOsxBundleDmg>
    <disableSplashScreen>1</disableSplashScreen>
    <enableRollback>1</enableRollback>
    <enableTimestamp>1</enableTimestamp>
    <installationScope>user</installationScope>
    <osxApplicationBundleIcon>./logo.icns</osxApplicationBundleIcon>
    <requireInstallationByRootUser>1</requireInstallationByRootUser>
    <vendor>NetEase</vendor>
    <windowsExecutableIcon>./logo.ico</windowsExecutableIcon>
    <windowsSoftwareRegistryPrefix>${project.vendor}\${product_fullname}\1</windowsSoftwareRegistryPrefix>
    <finalPageActionList>
        <consoleWrite>
            <progressText>请重启Photoshop后在菜单：”窗口“-”扩展功能“中启用</progressText>
            <text>msg</text>
        </consoleWrite>
    </finalPageActionList>
    <parameterList>
        <directoryParameter>
            <name>installdir</name>
            <description>Installer.Parameter.installdir.description</description>
            <explanation>Installer.Parameter.installdir.explanation</explanation>
            <value></value>
            <default>${platform_install_prefix}/${product_shortname}-${product_version}</default>
            <allowEmptyValue>0</allowEmptyValue>
            <ask>no</ask>
            <cliOptionName>prefix</cliOptionName>
            <mustBeWritable>yes</mustBeWritable>
            <mustExist>0</mustExist>
            <width>30</width>
        </directoryParameter>
        <stringParameter name="plugin_folder" ask="0"/>
        <stringParameter name="package_name" ask="0"/>
    </parameterList>
    <platformOptionsList>
        <platformOptions>
            <platform>osx</platform>
            <postInstallationScript>${installdir}/install.sh</postInstallationScript>
        </platformOptions>
        <platformOptions>
            <platform>windows</platform>
            <postInstallationScript>${windows_folder_program_files_common}/Adobe/CEP/extensions/com.netease.library.game/exe/install.bat</postInstallationScript>
        </platformOptions>
    </platformOptionsList>

    <!-- sign -->
    <osxSigningIdentity>Developer ID Application: NetEase (Hangzhou) Network Co., Ltd. (CF44QJESLS)</osxSigningIdentity>
    <osxApplicationBundleIdentifier>com.netease.library</osxApplicationBundleIdentifier>
</project>
