var fs = require('fs')
var path = require('path')

// 递归删除目录 同步方法
function removeDir (dir) {
  const files = fs.readdirSync(dir)
  for (var i = 0; i < files.length; i++) {
    const newPath = path.join(dir, files[i])
    const stat = fs.statSync(newPath)
    if (stat.isDirectory()) {
      removeDir(newPath)
    } else {
      fs.unlinkSync(newPath)
    }
  }
  fs.rmdirSync(dir)
}

module.exports = (path) => {
  removeDir(path)
}
