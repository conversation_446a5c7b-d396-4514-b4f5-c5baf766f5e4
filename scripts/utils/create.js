const fs = require('fs')
module.exports = (path) => {
  if (fs.existsSync(path)) {
    try {
      fs.rmSync(path, { recursive: true, force: true })
    } catch (err) {
      console.warn(`删除目录失败: ${path}, 错误: ${err.message}`)
      // 如果删除失败，尝试继续创建
    }
  }

  try {
    fs.mkdirSync(path, { recursive: true })
  } catch (err) {
    if (err.code !== 'EEXIST') {
      throw err
    }
    // 如果目录已存在，忽略错误
  }
}
