const fs = require('fs')
const yauzl = require('yauzl')
const path = require('path')
const remove = require('./utils/remove.js')
const create = require('./utils/create.js')

const Minio = require('minio')

const config = {
  BucketName: 'uedc',
  OutFilePath: path.join(__dirname, '../exe'),
  FileList: [{
    name: 'ps-uploader-mac.zip',
    localPath: path.join(__dirname, '../exe/ps-uploader-mac.zip') // 本地zip文件路径
  }, {
    name: 'ps-uploader-win.zip',
    localPath: path.join(__dirname, '../exe/ps-uploader-win.zip')
  }]
}

const minioClient = new Minio.Client({
  endPoint: '*************',
  port: 9000,
  useSSL: false,
  accessKey: 'kKWfqi3gTfwNcQlbf7ss',
  secretKey: 'HlGFsKvOWQGwYNaCYNUAQyLCYUCiHLvPzOhF0A9P',
  bucket: 'uedcbash'
})

function clearFileFolder () {
  if (fs.existsSync(config.OutFilePath)) {
    remove(config.OutFilePath)
    console.log('删除exe文件夹内容成功')
  }
  create(config.OutFilePath)
  console.log('创建exe文件夹成功，开始下载zip包')
}

const unzipFile = (src) => {
  yauzl.open(src, { lazyEntries: true }, (err, zipfile) => {
    if (err) throw err

    zipfile.readEntry()

    zipfile.on('entry', (entry) => {
      const filePath = `${config.OutFilePath}/${entry.fileName}`

      if (/\/$/.test(entry.fileName)) {
        // Directory entry
        fs.mkdir(filePath, { recursive: true }, (err) => {
          if (err) throw err
          zipfile.readEntry()
        })
      } else {
        // File entry
        zipfile.openReadStream(entry, (err, readStream) => {
          if (err) throw err

          fs.mkdirSync(require('path').dirname(filePath), { recursive: true })

          readStream.pipe(fs.createWriteStream(filePath))
          readStream.on('end', () => {
            zipfile.readEntry()
          })
        })
      }
    })

    zipfile.on('end', () => {
      console.log('解压文件成功，保存至', config.OutFilePath)
      fs.unlink(src, err => {
        if (err) {
          console.logs('zip包删除失败')
        } else {
          console.error('zip包删除成功')
        }
      })
    })
  })
}

async function downloadFileList () {
  try {
    config.FileList.forEach(item => {
      minioClient.fGetObject(config.BucketName, item.name, item.localPath, function (err) {
        if (err) {
          return console.log(err)
        }
        console.log('File downloaded and saved to', item.localPath)

        unzipFile(item.localPath)
      })
    })
  } catch (err) {
    console.log(err.message)
  }
}

// 清除文件夹内容
clearFileFolder()
// 下载文件列表
downloadFileList()
