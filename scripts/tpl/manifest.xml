<?xml version='1.0' encoding='UTF-8'?>
<ExtensionManifest ExtensionBundleId="{{name}}" ExtensionBundleVersion="{{version}}" Version="7.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <ExtensionList>
    <Extension Id="{{name}}.panel" Version="1.0.0" />
  </ExtensionList>
  <ExecutionEnvironment>
    <HostList>
      <Host Name="PHXS" Version="[11.0,99.9]"/>
      <Host Name="PHSP" Version="[11.0,99.9]"/>
      <Host Name="ILST" Version="[18,99.9]" />
    </HostList>
    <LocaleList>
      <Locale Code="All" />
    </LocaleList>
    <RequiredRuntimeList>
      <RequiredRuntime Name="CSXS" Version="7.0" />
    </RequiredRuntimeList>
  </ExecutionEnvironment>
  <DispatchInfoList>
    <Extension Id="{{name}}.panel">
      <DispatchInfo>
        <Resources>
          <!-- <MainPath>./index.html</MainPath> -->
          <MainPath>./dist/index.html</MainPath>
          <ScriptPath>./jsx/main.jsx</ScriptPath>
          <CEFCommandLine>
            <Parameter>--enable-nodejs</Parameter>
            <!-- Doesn't show in Window > Extensions unless mixed content is also enabled -->
            <Parameter>--mixed-context</Parameter>
          </CEFCommandLine>
        </Resources>
        <Lifecycle>
          <AutoVisible>true</AutoVisible>
        </Lifecycle>
        <UI>
          <Type>Panel</Type>
          <Menu>{{description}}</Menu>
          <Geometry>
            <Size>
              <Height>600</Height>
              <Width>680</Width>
            </Size>
            <MaxSize>
              <Height>9999</Height>
              <Width>9999</Width>
            </MaxSize>
            <MinSize>
              <Height>600</Height>
              <Width>403</Width>
            </MinSize>
          </Geometry>
          <Icons>
            <Icon Type="Normal">./images/IconLight.png</Icon>
            <Icon Type="RollOver">./images/IconLight.png</Icon>
            <Icon Type="DarkNormal">./images/IconDark.png</Icon>
            <Icon Type="DarkRollOver">./images/IconDark.png</Icon>
          </Icons>
        </UI>
      </DispatchInfo>
    </Extension>
  </DispatchInfoList>
</ExtensionManifest>
