const fs = require('fs')
const path = require('path')
const Minio = require('minio')

const { version } = require('./../package.json')
const { allpkg } = require('./config/index.json')

const config = {
  BucketName: 'uedc',
  folderFilePath: path.join(__dirname, `../zxp/v${version}`),
  fileNames:{
    mac: `-${version}-osx-installer.dmg`,
    win: `-${version}-windows-installer.exe`
  }
}

// 组装需要上传的文件
const filesList=[]
for (let i = 0; i < allpkg.length; i++) {
  const { description, shortName } = allpkg[i]
  const folderPath =config.folderFilePath+'/'+ description

  filesList.push(`${folderPath}/${shortName}${config.fileNames.mac}`)
  filesList.push(`${folderPath}/${shortName}${config.fileNames.win}`)
}

const minioClient = new Minio.Client({
  endPoint: '*************',
  port: 9000,
  useSSL: false,
  accessKey: 'kKWfqi3gTfwNcQlbf7ss',
  secretKey: 'HlGFsKvOWQGwYNaCYNUAQyLCYUCiHLvPzOhF0A9P',
  bucket: 'uedcbash'
})

function upload (filePath) {
  const fileName = filePath.split('/').pop()
  console.log('开始上传', fileName)
  const fileStream = fs.createReadStream(filePath)

  fs.stat(config.folderFilePath, function (err, stats) {
    if (err) {
      return console.log(err)
    }
    minioClient.putObject(config.BucketName, version+'/' +fileName, fileStream, stats.size, function (err, objInfo) {
      if (err) {
        return console.log(err) // err should be null
      }
      console.log('上传成功', fileName)
    })
  })
}
filesList.forEach(filePath=>{
  if (fs.existsSync(filePath)) {
    upload(filePath)
  }
})