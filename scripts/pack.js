/**
 * TODO
 * lib自动化打包代码
 * ZXPSignCmd-64bit -sign [打包目录] [输出文件.zxp] [证书.p12] [证书密码]
 * scripts/zxpsign/ZXPSignCmd-64bit -sign lib/com.netease.library.qa zxp/com.netease.library.qa.zxp scripts/zxpsign/netease-design.p12 passwd
 */

const fs = require('fs')
const path = require('path')
const childProcess = require('child_process')
const copy = require('./utils/copy.js')
const create = require('./utils/create.js')
const remove = require('./utils/remove.js')
const { version } = require('./../package.json')
const { allpkg } = require('./config/index.json')
const prjPath = path.join(__dirname, '../')
const libPath = path.join(__dirname, `../lib/v${version}`)
const zxpPath = path.join(__dirname, `../zxp/v${version}`)
const exePath = path.join(__dirname, '../exe')
const dayjs = require('dayjs')
const zipFolder = require('zip-folder')
const yauzl = require('yauzl')

const argvs = process.argv.slice(2)

process.env.VUE_APP_TIMETAMP = dayjs().format('YY.MM.DD.HH')

// 解压zip文件的辅助函数
function unzipFile(zipPath, targetDir) {
  return new Promise((resolve, reject) => {
    yauzl.open(zipPath, { lazyEntries: true }, (err, zipfile) => {
      if (err) {
        reject(err)
        return
      }

      zipfile.readEntry()

      zipfile.on('entry', (entry) => {
        const filePath = path.join(targetDir, entry.fileName)

        if (/\/$/.test(entry.fileName)) {
          // 目录条目
          fs.mkdir(filePath, { recursive: true }, (err) => {
            if (err) {
              reject(err)
              return
            }
            zipfile.readEntry()
          })
        } else {
          // 文件条目
          zipfile.openReadStream(entry, (err, readStream) => {
            if (err) {
              reject(err)
              return
            }

            // 确保父目录存在
            fs.mkdirSync(path.dirname(filePath), { recursive: true })

            const writeStream = fs.createWriteStream(filePath)
            readStream.pipe(writeStream)

            writeStream.on('close', () => {
              zipfile.readEntry()
            })

            writeStream.on('error', (err) => {
              reject(err)
            })
          })
        }
      })

      zipfile.on('end', () => {
        resolve()
      })

      zipfile.on('error', (err) => {
        reject(err)
      })
    })
  })
}

// 处理exe目录，将ps-uploader压缩成zip文件用于安装时解压
function copyExeWithCompression(sourcePath, targetPath) {
  return new Promise((resolve, reject) => {
    async function processExeDirectory() {
      try {
        console.log('🗂️  开始处理exe目录，将ps-uploader压缩成zip文件用于安装时解压...')

        // 确保目标目录存在
        fs.mkdirSync(targetPath, { recursive: true })

        const sourceFiles = fs.readdirSync(sourcePath)

        for (const file of sourceFiles) {
          const sourceFilePath = path.join(sourcePath, file)
          const stat = fs.statSync(sourceFilePath)

          if (stat.isDirectory() && file.startsWith('ps-uploader')) {
            // 对ps-uploader目录压缩成zip文件，不解压，留给安装时处理
            console.log(`🗜️  压缩 ${file} 目录为zip文件（安装时将自动解压，避免大量文件IO错误）...`)

            const targetZipPath = path.join(targetPath, `${file}.zip`)

            // 直接压缩到目标位置
            await new Promise((resolve, reject) => {
              zipFolder(sourceFilePath, targetZipPath, (err) => {
                if (err) {
                  console.error(`❌ 压缩 ${file} 失败:`, err)
                  reject(err)
                  return
                }
                console.log(`✅ ${file} 已压缩为 ${file}.zip，包含大量文件，安装时将自动解压`)
                resolve()
              })
            })

          } else {
            // 其他文件直接复制
            const targetFilePath = path.join(targetPath, file)
            if (stat.isDirectory()) {
              console.log(`📁 直接复制目录: ${file}`)
              await copy(sourceFilePath, targetFilePath)
            } else {
              console.log(`📄 直接复制文件: ${file}`)
              fs.copyFileSync(sourceFilePath, targetFilePath)
            }
          }
        }

        console.log('✅ exe目录处理完成，ps-uploader已压缩为zip文件，安装时将自动解压')
        resolve()

      } catch (error) {
        console.error('❌ 处理exe目录时出错:', error)
        reject(error)
      }
    }
    processExeDirectory()
  })
}

// 如果不存在exe文件得先执行指令更新下载exe包
if (!fs.existsSync(exePath)) {
  childProcess.execSync('npm run update-exe', { cwd: prjPath })
  // throw new Error('请先执行npm run update-exe指令更新下载exe包')
}

create(libPath)
create(zxpPath)

// 全量包(qa、game、prod)
// const pkgs = argvs[0] === 'qa' ? allpkg.filter(ele => ele.name === 'com.netease.library.qa') : allpkg

// 只打包qa
const pkgs = allpkg.filter(ele => ele.name === 'com.netease.library.game')

pkgs.forEach(async ele => {
  const pkgPath = path.join(__dirname, `../lib/v${version}/${ele.name}`)

  // 使用create函数来处理目录创建，避免EEXIST错误
  if (fs.existsSync(pkgPath)) {
    try {
      fs.rmSync(pkgPath, { recursive: true, force: true })
    } catch (err) {
      console.warn(`删除包目录失败: ${pkgPath}, 错误: ${err.message}`)
    }
  }

  fs.mkdirSync(pkgPath, { recursive: true })
  fs.mkdirSync(pkgPath + '/CSXS', { recursive: true })

  // const pkgTpl = require('./../package.json')
  // console.log(pkgTpl)
  const pkgTpl = JSON.parse(
    fs.readFileSync(`${__dirname}/config/index.json`).toString()
  ).package
  // let htmlTpl = fs.readFileSync(`${__dirname}/tpl/index.html`).toString()
  let xmlTpl = fs.readFileSync(`${__dirname}/tpl/manifest.xml`).toString()

  pkgTpl.name = ele.name
  pkgTpl.version = version
  pkgTpl.description = ele.description

  // htmlTpl = htmlTpl.replace(/\{\{\w+\}\}/g, item => {
  //   const result = item.match(/\w+/)[0]
  //   return ele[result] || ''
  // })

  xmlTpl = xmlTpl.replace(/\{\{\w+\}\}/g, item => {
    const result = item.match(/\w+/)[0]
    return result === 'version' ? version : ele[result] || ''
  })

  if (ele.debugPort) {
    let debugTpl = fs.readFileSync(`${__dirname}/tpl/.debug`).toString()
    debugTpl = debugTpl.replace(/\{\{\w+\}\}/g, item => {
      const result = item.match(/\w+/)[0]
      return ele[result] || ''
    })
    fs.writeFileSync(`${pkgPath}/.debug`, debugTpl)
  }

  fs.writeFileSync(`${pkgPath}/package.json`, JSON.stringify(pkgTpl))
  // fs.writeFileSync(`${pkgPath}/index.html`, htmlTpl)
  fs.writeFileSync(`${pkgPath}/CSXS/manifest.xml`, xmlTpl)

  childProcess.execSync('yarn install', { cwd: prjPath })
  childProcess.execSync(ele.script, { cwd: prjPath })
  await copy(path.join(__dirname, '../dist'), `${pkgPath}/dist`)

  // fs.copyFileSync(
  //   `${__dirname}/tpl/yarn.lock`,
  //   `${pkgPath}/yarn.lock`
  // )
  await copy(path.join(__dirname, '../jsx'), `${pkgPath}/jsx`)
  await copy(path.join(__dirname, '../atn'), `${pkgPath}/atn`)
  // 使用压缩包方式处理exe目录，在安装时减少IO错误
  await copyExeWithCompression(exePath, `${pkgPath}/exe`)
  await copy(path.join(`${__dirname}/images/${ele.name}`), `${pkgPath}/images`)
  // chmod命令移到解压脚本中执行，因为现在ps-uploader-mac是zip文件
  // childProcess.execSync('chmod +x ps-uploader', { cwd: `${pkgPath}/exe/ps-uploader-mac` })
  childProcess.execSync('npm install', { cwd: pkgPath })
  childProcess.execSync(`scripts/zxpsign/ZXPSignCmd-64bit -sign lib/v${version}/${ele.name} zxp/v${version}/${ele.name}.zxp scripts/zxpsign/netease-design.p12 passwd`,
    {
      cwd: path.join(__dirname, '..')
    }
  )
  console.log(`包生成: ${ele.name}`)
  console.log(`解压zxp: ${ele.name}`)
  childProcess.execSync(`unzip -o zxp/v${version}/${ele.name}.zxp -d zxp/v${version}/${ele.name}`)

  // /Applications/VMware InstallBuilder Enterprise 21.12.0 => /Applications/InstallBuilder Enterprise 24.11.1
  childProcess.execSync(`"/Applications/InstallBuilder Enterprise 24.11.1/bin/builder" build scripts/build/DubhePlugin.xml osx --setvars project.version=${version} plugin_folder=../../zxp/v${version}/${ele.name} project.fullName=${ele.description} project.shortName=${ele.shortName} package_name=${ele.name}`,
    {
      cwd: path.join(__dirname, '..')
    }
  )
  console.log(`Mac安装包生成: ${ele.name}`)

  const installerDir = path.join(__dirname, `../zxp/v${version}/${ele.description}/`)
  create(installerDir)

  const macName = `${ele.shortName}-${version}-osx-installer.dmg`
  fs.rename('/Applications/InstallBuilder Enterprise 24.11.1/output/' + macName, installerDir + macName, function (err) {
    if (err) {
      throw err
    }
  })
  // GUI线上包才进行apple公正
  // if (ele.name === 'com.netease.library.game') {
  //   console.log('apple公正')
  //   console.log(`xcrun notarytool submit '${installerDir}${macName}' --keychain-profile "Notarization" --wait`)
  //   childProcess.execSync(`xcrun notarytool submit '${installerDir}${macName}' --keychain-profile "Notarization" --wait`,
  //     {
  //       cwd: path.join(__dirname, '..')
  //     }
  //   )
  // }

  childProcess.execSync(`"/Applications/InstallBuilder Enterprise 24.11.1/bin/builder" build scripts/build/DubhePlugin.xml windows --setvars project.version=${version} plugin_folder=../../zxp/v${version}/${ele.name} project.fullName=${ele.description} project.shortName=${ele.shortName}`,
    {
      cwd: path.join(__dirname, '..')
    }
  )
  console.log(`Windows安装包生成: ${ele.name}`)

  const windowsName = `${ele.shortName}-${version}-windows-installer.exe`
  fs.rename('/Applications/InstallBuilder Enterprise 24.11.1/output/' + windowsName, installerDir + windowsName, function (err) {
    if (err) {
      throw err
    }
  })

  console.log('清理文件')
  remove(path.join(__dirname, `../zxp/v${version}/${ele.name}`))
})

console.log('success')
