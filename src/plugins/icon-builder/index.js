const WebpackIconfontPluginNodejs = require('webpack-iconfont-plugin-nodejs')
const path = require('path')
const options = {
  fontName: 'myIcon',
  cssPrefix: 'myico',
  svgs: path.join(path.resolve(__dirname, '../../assets'), 'svg/*.svg'),
  fontsOutput: path.join(path.resolve(__dirname, '../../assets'), 'iconfont/'),
  cssOutput: path.join(path.resolve(__dirname, '../../assets'), 'iconfont/font.css'),
  htmlOutput: path.join(path.resolve(__dirname, '../../assets'), 'iconfont/_font-preview.html'),
  jsOutput: path.join(path.resolve(__dirname, '../../assets'), 'iconfont/fonts.js')
}
module.exports = new WebpackIconfontPluginNodejs(options)
