import { CLOUD_SOURCE_PATHNAME, HOST, LOG_SOURCE_PATHNAME, VERSION,TIMETAMP } from '@/config/index.js'
import app from '@/main'
import moment from 'moment'
import Vue from 'vue'

const JSZip = require('jszip')
// eslint-disable-next-line no-undef
// const cs = new CSInterface()
// eslint-disable-next-line no-undef
const fs = custom_node.fs
// eslint-disable-next-line no-undef
const path = custom_node.path
// eslint-disable-next-line no-undef
const request = custom_node.request
// eslint-disable-next-line no-undef, camelcase
const child_process = custom_node.cp
// eslint-disable-next-line no-undef, camelcase
const { exec } = child_process

// eslint-disable-next-line no-undef
const csInterface = new CSInterface()
// eslint-disable-next-line no-undef
const LogFileDir = `${csInterface.getSystemPath(SystemPath.USER_DATA)}/${LOG_SOURCE_PATHNAME}`
// eslint-disable-next-line no-undef
const CloudeLogFileDir = `${csInterface.getSystemPath(SystemPath.USER_DATA)}/${CLOUD_SOURCE_PATHNAME}`
console.log('=============LogFileDir=============', LogFileDir)

// 确保文件夹已经生成
function ensureLogFileDirExists () {
  if (!fs.existsSync(LogFileDir)) {
    fs.mkdirSync(LogFileDir, { recursive: true })
  }
}

// 写入文件
async function writeLogToFile (message) {
  const dateString = moment().format('YYYY-MM-DD')
  const logFilePath = `${LogFileDir}/com.netease.cep.dubhe-${dateString}.log`

  ensureLogFileDirExists()
  await fs.appendFile(logFilePath, message + '\n', (error) => {
    if (error) {
      console.error(`写入日志文件时发生错误：${error.message}`)
    }
  })
}

// 提取文件名字里的日期
function extractDateFromFilename (filename) {
  const regex = /(\d{4})-(\d{2})-(\d{2})/ // 假设文件名中的日期格式为 YYYY-MM-DD
  const match = filename.match(regex)

  if (match) {
    const year = parseInt(match[1], 10)
    const month = parseInt(match[2], 10) - 1 // JavaScript 中的月份从 0 开始
    const day = parseInt(match[3], 10)

    return new Date(year, month, day)
  }

  return null
}

// 读取文件夹中的文件
function readFilesFromFolder (folderPath, callback) {
  return new Promise((resolve, reject) => {
    if (fs.existsSync(folderPath)) {
      fs.readdir(folderPath, (err, files) => {
        if (err) {
          reject(err)
        } else {
          callback(files, resolve, reject)
        }
      })
    } else {
      resolve([])
    }
  })
}

// 创建 ZIP 文件
async function createZipFromFiles (files) {
  const zip = new JSZip()

  for (const file of files) {
    const content = fs.readFileSync(file)
    zip.file(path.basename(file), content)
  }
  const zipData = await zip.generateAsync({ type: 'nodebuffer' })
  const zipName = `cepdubhe-log-${moment().format('YYYY-MM-DD-HH-mm-ss')}-${VERSION}.zip`
  const outputPath = `${LogFileDir}/${zipName}`
  // 输出到本地
  await fs.writeFileSync(outputPath, zipData)

  return outputPath
}

// 上传 ZIP 文件
async function uploadZipFile (zipPath) {
  const formData = {
    file: fs.createReadStream(zipPath)
  }
  console.log('zipPath============', zipPath)

  return new Promise((resolve, reject) => {
    request.post({
      timeout: 1000 * 60 * 5,
      url: `${HOST}/api/plugin/storage/uploadFile`,
      formData
    }, function (error, response, body) {
      if (response && response.statusCode === 200) {
        const bodyData = JSON.parse(body)
        if (bodyData.code === 200) {
          resolve(bodyData.result)
        } else {
          reject(new Error(body.message))
        }
        resolve(body)
      } else {
        reject(error)
      }
    })
  })
}
// 获取设备ID
export function getMacUUID () {
  const osInfo = app.$csInterface.getOSInformation()
  const isWindows = osInfo.indexOf('Windows') > -1
  return new Promise((resolve, reject) => {
    if (isWindows) {
      exec('wmic csproduct get UUID', (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`exec error: ${error}`))
        } else {
          const uuid = stdout.split('\n')[1].trim()
          resolve(uuid)
        }
      })
    } else {
      exec("ioreg -rd1 -c IOPlatformExpertDevice | awk '/IOPlatformUUID/ { split($0, line, \"\\\"\"); printf(\"%s\\n\", line[4]); }'", (error, stdout, stderr) => {
        if (error) {
          reject(new Error(`exec error: ${error}`))
        } else {
          resolve(stdout.trim())
        }
      })
    }
  })
}

// 上报日志
async function uploadLogs () {
  try {
    // 前端日志文件
    const files = await readFilesFromFolder(LogFileDir, (files, resolve) => {
      // 过滤最近三天的日期的文件
      const now = new Date()
      const logFiles = files
        .filter((file) => {
          const isLog = path.extname(file) === '.log'
          let isConformDate = false
          if (isLog) {
            const date = extractDateFromFilename(file)
            console.log('date========', date)
            const diffTime = now - date
            const diffDays = diffTime / (1000 * 60 * 60 * 24)

            if (diffDays <= 3) {
              isConformDate = true
            }
          }
          return isLog && isConformDate
        })
        .map(file => path.join(LogFileDir, file))
      resolve(logFiles)
    })

    // 网盘日志文件
    const cloudFiles = await readFilesFromFolder(CloudeLogFileDir, (files, resolve) => {
      const logFiles = files
        .filter((file) => {
          return file.includes('uploader.log')
        })
        .map(file => path.join(CloudeLogFileDir, file))
      resolve(logFiles)
    })

    const zipPath = await createZipFromFiles([...files, ...cloudFiles])
    const data = await uploadZipFile(zipPath)

    console.log('==========上传成功', data, zipPath)
    // 上传成功删除本地zip包
    fs.unlink(zipPath, err => {
      if (err) {
        console.logs('zip包删除失败')
      } else {
        console.error('zip包删除成功')
      }
    })
    // 得到上传链接用埋点形式上传
    const deviceID = await getMacUUID()
    console.log('======deviceID', deviceID)
    await app.$et('UPLOAD_LOGGER', {
      content: JSON.stringify({
        url: data.url,
        name: data.displayName,
        deviceID,
        version: VERSION
      })
    })

    // console.log('文件打包并上传成功', data.url)
    app.$CustomToast({
      type: 'success',
      duration: 2,
      content: '日志上报成功'
    })
  } catch (error) {
    logMessage('Error', error.message)
    app.$CustomToast({
      type: 'error',
      duration: 2,
      content: '上传日志失败 ' + error.message
    })
  }
}

// 日志打点
async function logMessage (type, message, pageName = 'common') {
  const timestamp = moment().format('YYYY-MM-DD HH:mm:ss')
  const formattedMessage = `【${type}】【${timestamp}】[${VERSION}(${TIMETAMP})]【${pageName}】 ${message}`
  console.log(formattedMessage)
  await writeLogToFile(formattedMessage)
}

const logMsg = {
  info: (message, pageName) => {
    logMessage('Info', message, pageName)
  },
  error: (message, pageName) => {
    logMessage('Error', message, pageName)
  }
}
window.$logMsg = logMsg
window.$uploadLogs = uploadLogs
Vue.prototype.$logMsg = logMsg
Vue.prototype.$uploadLogs = uploadLogs

logMsg.info('日志路径:' + LogFileDir)

export default logMsg
