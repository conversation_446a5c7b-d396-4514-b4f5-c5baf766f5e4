import logMsg from './logMsg'
import Vue from 'vue'

const ErrorLogger = {
  install (Vue) {
    Vue.config.errorHandler = function (err, vm, info) {
      logMsg.error('[Vue error]' + err.message, 'Global Error Logger')
    }

    window.onerror = function (message, source, lineno, colno, error) {
      logMsg.error('[Global runtime error]' + error.message, 'Global Error Logger')
    }

    window.addEventListener('unhandledrejection', function (event) {
      logMsg.error('[Unhandled promise rejection]' + event.reason, 'Global Error Logger')
    })
  }
}
Vue.use(ErrorLogger)
