export const gonzPersistent = () => {
  // eslint-disable-next-line no-undef
  const cs = new CSInterface()
  // eslint-disable-next-line no-undef
  const event = new CSEvent() // 创建一个事件
  event.type = 'com.adobe.PhotoshopPersistent' // 注册持久化运行事件
  event.scope = 'APPLICATION'
  event.extensionId = cs.getExtensionID()
  cs.dispatchEvent(event)
}
export const gonzUnPersistent = () => {
  // eslint-disable-next-line no-undef
  const cs = new CSInterface()
  // eslint-disable-next-line no-undef
  const event = new CSEvent() // 创建一个事件
  event.type = 'com.adobe.PhotoshopUnPersistent' // 注册持久化运行事件
  event.scope = 'APPLICATION'
  event.extensionId = cs.getExtensionID()
  cs.dispatchEvent(event)
}

gonzPersistent()
