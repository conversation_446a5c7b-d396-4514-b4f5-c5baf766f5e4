/**
 * 客户端对文件上传下载的方法
 */
import { uploadFile } from '@/bridge/api'
import path from 'path'
import Vue from 'vue'

// 开始上传文件请求
async function uploadFileAction(instance, {
  id,
  groupId,
  groupName,
  size,
  fileName,
  sourcePath,
  isSharedWithMe
}) {
  try {
    if (!instance.$bus.canUpload()) {
      instance.$CustomToast({
        type: 'error',
        duration: 2,
        content: '操作频繁，请稍后再试'
      })
      return
    }

    instance.$logMsg.info(
      '请求上传参数:' +
      JSON.stringify({
        id,
        groupId,
        groupName,
        size,
        fileName,
        sourcePath,
        isSharedWithMe
      }),
      'Vue.prototype.$uploadFileAction'
    )

    // 文件后缀名
    const fileSuffix = /(?:\.([^.]+))?$/.exec(path.basename(sourcePath))[1]

    const requestBody = {
      gid: groupId,
      files: [
        {
          sid: id,
          path: sourcePath,
          file_name: `${fileName}.${fileSuffix}`,
          is_saving: size !== null
        }
      ]
    }

    try {
      await instance.$saveFile()
      await uploadFile(groupId, requestBody)
    } catch (e) {
      this.$bus.uploadStack.forEach(i => {
        if (i.id === id) i.status = 'failure'
      })
    }

    instance.$bus.minusUploadCount()
    if (size === null) {
      instance.$bus.addNewPrivateUploadFile({
        new: true,
        name: fileName,
        // cover: thumbnailRes.url,
        lastUpdater: {
          avatar: instance.$bus.profile.avatar,
          collaboratorKey: '',
          collaboratorName: ''
        },
        id,
        groupId
      })
    }

    instance.$logMsg.info(
      '上传文件调用成功',
      'Vue.prototype.$uploadFileAction'
    )
  } catch (error) {
    instance.$CustomToastRemove()
    instance.$CustomToast({
      type: 'error',
      duration: 3,
      content: '上传文件出错:' + error.message
    })
    instance.$logMsg.error(
      '上传文件出错:' + error.message,
      'Vue.prototype.$uploadFileAction'
    )
  }
}

Vue.prototype.$uploadFileAction = uploadFileAction
