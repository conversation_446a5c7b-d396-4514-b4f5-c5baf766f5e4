/* eslint-disable space-before-function-paren */
import { getResourceProjectList, getUserInfo } from '@/api/index.js'
import { myStorage } from '@/utils/index.js'
import Vue from 'vue'
import { getDownloadFilesList, getUploadFilesList } from '@/bridge/api'
import { getMuseToken } from '@/api/muse'

export const Bus = new Vue({
  data() {
    return {
      isWSInit: false,
      profile: myStorage.get('USERINFO') || {
        id: '',
        avatar: '',
        name: '',
        email: '',
        popoAvatar: '',
        type: 'NORMAL' // NORMAL-普通用户,EXTERNAL-外部用户
      },
      detail: null,
      list: [],
      downloadFilesList: [], // 下载中的文件
      uploadFilesList: [], // 所有上传中的文件数据
      privateUploadFilesList: [], // 组别列表里面展示的上传中占位符文件
      searchParams: {
        tags: [],
        colorRange: null,
        projectId: '',
        reqTime: 'ALL',
        startTime: '',
        endTime: ''
      },
      projectList: [],
      picData: null,
      downloadFiles: {},
      cancelDownloadFiles: [],
      refreshPage: 0,
      downloadFileKey: '',
      uploadCount: 10, // 控制5s内最多保存10次，频繁操作控制
      waitLoadingProcess: 100, // 上传等待进度条
      breadcrumbList: JSON.parse(sessionStorage.getItem('breadcrumbList') || '[]'),
      uploadStack: [],
      fakeProgressIntervals: {},
      fakeProgressMap: {},
      showGameNews: null,
      gameTitle: '',
      gameMaterialList: {},
      gameDetail: {},
      gameSearchKey: '',
      gameSearchType: 'game',
      filterTagIds: [],
      projectSelectedId: '',
      museToken: '',
      museAssetsList: [],
      museAssetDetail: {},
      museKeyword: '',
      museSearchList: [],
      museRepositoryList: [], // muse项目列表
      showMuseNews: null,
      // muse sider状态缓存
      museSiderState: {
        repositoryId: '',
        repositoryCode: '',
        curNodeId: '',
        curNode: null
      },
      // muse数据缓存
      museCacheData: {
        repositoryList: [], // 仓库列表缓存
        folderTrees: {}, // 文件夹树缓存，key为repositoryCode
        cacheTimestamp: null // 缓存时间戳
      },
      // 云盘组列表缓存
      cloudGroupsCache: {
        groupList: [], // 普通组列表
        projectGroupList: [], // 项目组列表
        cacheTimestamp: null // 缓存时间戳
      }
    }
  },
  created() {
    this.showGameNews = window.localStorage.getItem('showGameNews') === null
    this.showMuseNews = window.localStorage.getItem('showMuseNews') === null
    // 初始化时从 localStorage 获取 token
    const localToken = window.localStorage.getItem('museToken')
    if (localToken) {
      this.museToken = localToken
    }
    // 初始化时从 localStorage 获取 muse sider 状态
    this.loadMuseSiderState()
    // 初始化时从 localStorage 获取 muse 缓存数据
    this.loadMuseCacheData()
    // 初始化时从 localStorage 获取云盘组列表缓存
    this.loadCloudGroupsCache()
    setInterval(this.resetUploadCount, 60 * 1000)
  },
  watch: {
    waitLoadingProcess(num) {
      if (num < 100) return
      this.waitLoadingProcess = 100
      this.resetUploadCount()
    },
    uploadCount(num) {
      if (num !== 0) return
      const ins = setInterval(() => {
        this.waitLoadingProcess += 100 / 50 // 时间为5s
        if (this.waitLoadingProcess >= 100) {
          clearInterval(ins)
        }
      }, 100)
    },
    breadcrumbList: {
      deep: true,
      handler(newList) {
        sessionStorage.setItem('breadcrumbList', JSON.stringify(newList))
      }
    }
  },
  methods: {
    getUserInfo() {
      return new Promise((resolve, reject) => {
        getUserInfo()
          .then(d => {
            this.profile = d
            // 仅为了用于测试外部账号场景
            // this.profile.type = 'EXTERNAL'
            myStorage.set('USERINFO', {
              id: d.id,
              avatar: d.avatar,
              name: d.name,
              email: d.email,
              popoAvatar: d.popoAvatar,
              type: d.type,
              role: d.role
            })
            resolve(d)
          })
          .catch(e => {
            reject(e)
          })
      })
    },
    canUpload() {
      return this.uploadCount !== 0
    },
    resetUploadCount() {
      this.uploadCount = 10
    },
    minusUploadCount() {
      this.uploadCount = Math.max(0, (this.uploadCount - 1))
      if (this.uploadCount === 1) this.waitLoadingProcess = 0
    },
    updateDownloadFile(id, process) {
      this.$set(this.downloadFiles, id, process)
    },
    removeDownloadFile(id) {
      this.$delete(this.downloadFiles, id)
    },
    removeAllDownloadFiles() {
      this.downloadFiles = {}
    },
    clearSearchData(keyName = '') {
      for (const key of Object.keys(this.searchParams)) {
        if (key.startsWith(keyName)) delete this.searchParams[key]
      }
    },
    setPicData(d) {
      this.picData = d
    },
    clearPicData() {
      this.picData = null
    },
    setProjectList() {
      this.$logMsg.info('[setProjectList] 开始设置项目列表', 'BUS')
      return new Promise((resolve, reject) => {
        getResourceProjectList()
          .then(d => {
            let storageProjectId = myStorage.get('PROJECT_ID')
            this.projectList = Array.isArray(d) ? d.map(item => ({
              name: item.name,
              id: item.id
            })) : []
            const allIDs = this.projectList.map(item => item.id)
            const newID = this.searchParams.projectId || (allIDs[1] ?? '')
            // 如果缓存没有数据或者不在列表内，更新缓存数据，默认第二条
            if (!storageProjectId || !allIDs.includes(storageProjectId)) {
              storageProjectId = newID
              myStorage.set('PROJECT_ID', storageProjectId)
            }
            this.searchParams.projectId = storageProjectId
            this.$logMsg.info(`[setProjectList] 获取成功，设置为${storageProjectId}`, 'BUS')
            resolve(this.searchParams.projectId)
          })
          .catch(e => {
            this.$logMsg.error('[setProjectList] 获取失败', 'BUS')
            reject(e)
          })
      })
    },
    setSearchValue(key, value) {
      this.searchParams[key] = value
      if (key === 'projectId') myStorage.set('PROJECT_ID', value)
      this.$logMsg.info('[setSearchValue] ' + value, 'BUS')
    },
    setDetailData(value) {
      this.detail = value
      this.$logMsg.info('[setDetailData] ' + value, 'BUS')
    },
    getDetailData() {
      this.$logMsg.info('[getDetailData] ' + this.detail, 'BUS')
      return this.detail
    },
    getUploadFilesList(gid) {
      this.$logMsg.info('[getUploadFilesList] 开始获取正在上传文件列表', 'BUS')
      getUploadFilesList(gid, { gid }).then(d => {
        this.uploadFilesList = d.data.data
        this.$logMsg.info('[getUploadFilesList] 获取成功' + JSON.stringify(this.uploadFilesList), 'BUS')
      })
    },
    getDownloadFilesList(gid) {
      this.$logMsg.info('[getDownloadFilesList] 开始获取正在下载文件列表', 'BUS')
      getDownloadFilesList(gid, { gid }).then(d => {
        this.downloadFilesList = d.data.data
        this.$logMsg.info('[getDownloadFilesList] 获取成功', 'BUS')
      })
    },
    addCancelDownloadFiles(fileId) {
      this.cancelDownloadFiles.push(fileId)
    },
    removeCancelDownloadFiles(fileId) {
      this.cancelDownloadFiles = this.cancelDownloadFiles.filter(id => id !== fileId)
    },
    uploadProcess(data) {
      this.$logMsg.info('[uploadProcess] 上传进度' + data.command, 'BUS')
      const {
        sid,
        gid,
        content_length: contentLength,
        preview_url: previewUrl
      } = (data && data.data) || {}

      // 存储定时器的引用
      if (!this.fakeProgressIntervals) {
        this.fakeProgressIntervals = {}
      }

      const updateProgress = (process, status = 'progress') => {
        this.$bus.uploadStack.forEach(i => {
          if (i.id === sid) {
            i.process = process
            i.status = status
          }
        })
        // 更新 fakeProgressMap
        this.$set(this.fakeProgressMap, sid, process)
      }

      const startFakeProgress = () => {
        let fakeProgress = 0
        this.fakeProgressIntervals[sid] = setInterval(() => {
          fakeProgress += Math.random() < 0.5 ? 1 : 2
          if (fakeProgress >= 90) {
            clearInterval(this.fakeProgressIntervals[sid])
          }
          updateProgress(fakeProgress)
        }, 2000)
        this.$set(this.fakeProgressMap, sid, 0)
      }

      const clearFakeProgressInterval = () => {
        if (this.fakeProgressIntervals[sid]) {
          clearInterval(this.fakeProgressIntervals[sid])
          delete this.fakeProgressIntervals[sid]
        }
      }

      switch (data.command) {
        case 'prepare': {
          updateProgress(0)
          startFakeProgress()
          const params = {
            gid,
            sid,
            process: 10,
            upload_length: 0,
            content_length: contentLength
          }
          const fileIndex = this.uploadFilesList.findIndex(item => item.sid === sid)
          if (fileIndex > -1) {
            this.$set(this.uploadFilesList, fileIndex, {
              ...this.uploadFilesList[fileIndex],
              ...params
            })
          } else {
            this.uploadFilesList.push(params)
          }
          break
        }

        case 'finished': {
          clearFakeProgressInterval()
          updateProgress(100, 'success')
          this.uploadFilesList = this.uploadFilesList.filter(item => item.sid !== sid)
          this.privateUploadFilesList = this.privateUploadFilesList.filter(item => item.id !== sid)
          this.sid = sid
          this.gid = gid
          // refresh flag
          setTimeout(() => this.refreshPage++, 2000)
          if (this.uploadStack.every(item => item.status === 'success')) {
            setTimeout(() => {
              this.$emit('allUploadsCompleted')
              this.uploadStack = []
            }, 2000)
          } else {
            const cur = this.uploadStack.find(item => item.id === sid)
            if (!cur) return
            this.$CustomToast({
              type: 'success',
              duration: 2,
              content: `${cur.filename} 上传成功，正在加载`
            })
            setTimeout(() => {
              this.uploadStack = this.uploadStack.filter(item => item.id !== sid)
            }, 2000)
          }
          break
        }

        case 'preview_url': {
          const fileIndex = this.uploadFilesList.findIndex(item => item.sid === sid)
          if (fileIndex > -1) {
            this.$set(this.uploadFilesList, fileIndex, {
              ...this.uploadFilesList[fileIndex],
              preview_url: previewUrl
            })
          }
          break
        }

        case 'error': {
          clearFakeProgressInterval()
          updateProgress(0, 'failure')
          this.uploadFilesList = this.uploadFilesList.filter(item => item.sid !== sid)
          this.privateUploadFilesList = this.privateUploadFilesList.filter(item => item.id !== sid)
          this.$CustomToast({
            type: 'error',
            duration: 2,
            content: '上传失败，请重试'
          })
          this.$logMsg.error('[uploadProcess] 上传失败', 'BUS')
          break
        }

        default:
          break
      }
    },
    downloadProcess(data) {
      this.$logMsg.info('[downloadProcess] 下载进度' + data.command, 'BUS')
      // 客户端推送事件参数返回
      const {
        file_key: fileKey,
        content_length: contentLength,
        download_length: downloadLength,
        operate_code: operateCode
      } = (data && data.data) || {}
      // 当前文件索引
      const fileIndex = this.downloadFilesList.findIndex(item => item.file_key === fileKey)
      switch (data.command) {
        case 'prepare': {
          const params = {
            file_key: fileKey,
            process: 0,
            download_length: 0,
            content_length: contentLength,
            operate_code: operateCode
          }
          if (fileIndex > -1) {
            this.$set(this.downloadFilesList, fileIndex, {
              ...this.downloadFilesList[fileIndex],
              ...params
            })
          } else {
            this.downloadFilesList.push(params)
          }
          break
        }

        case 'sync_percentage': {
          const params = {
            file_key: fileKey,
            process: Math.min(100, parseInt(downloadLength * 100 / contentLength)),
            download_length: downloadLength,
            content_length: contentLength,
            operate_code: operateCode
          }
          if (fileIndex > -1) {
            this.$set(this.downloadFilesList, fileIndex, {
              ...this.downloadFilesList[fileIndex],
              ...params
            })
          } else {
            this.downloadFilesList.push(params)
          }
          break
        }

        case 'finished': {
          this.downloadFilesList = this.downloadFilesList.filter(item => item.file_key !== fileKey)
          this.downloadFileKey = fileKey
          break
        }

        case 'error': {
          this.downloadFilesList = this.downloadFilesList.filter(item => item.file_key !== fileKey)
          this.$CustomToast({
            type: 'error',
            duration: 2,
            content: '操作失败，请重试'
          })
          this.$logMsg.error('[downloadProcess] 操作失败', 'BUS')
          break
        }

        default:
          break
      }
    },
    initPrivateUploadFiles(gid) {
      this.$logMsg.info('[initPrivateUploadFiles] getUploadFilesList START', 'BUS')
      getUploadFilesList(gid, { gid })
        .then(d => {
          this.$logMsg.info('[initPrivateUploadFiles] getUploadFilesList END ~ ' + JSON.stringify(d.data.data), 'BUS')
          this.privateUploadFilesList = d.data.data.filter(item => item.source === 'loaded').map((item) => ({
            new: true,
            cover: item.preview_url || require('@/assets/img/svg/default.svg'),
            name: item.name,
            lastUpdater: {
              avatar: this.$bus.profile.popoAvatar,
              collaboratorKey: this.$bus.profile.email,
              collaboratorName: this.$bus.profile.name
            },
            id: item.sid,
            groupId: item.gid
          }))
        })
    },
    addNewPrivateUploadFile(data) {
      this.$logMsg.info('[addNewPrivateUploadFile]', 'BUS')
      this.privateUploadFilesList.push(data)
    },
    setList(list) {
      this.list = list
    },
    setBreadcrumbList(list) {
      this.breadcrumbList = list
      sessionStorage.setItem('breadcrumbList', JSON.stringify(list))
    },
    changeIsWSInit(flag) {
      this.isWSInit = flag
    },
    setGameTitle(title) {
      window.sessionStorage.setItem('gameTitle', title)
      this.gameTitle = title
    },
    setGameMaterialList(list) {
      this.gameMaterialList = list
    },
    setGameDetail(item) {
      this.gameDetail = item
    },
    setGameSearchKey(key) {
      this.gameSearchKey = key
    },
    setGameSearchType(type) {
      this.gameSearchType = type
    },
    setFilterTagIds(ids) {
      this.filterTagIds = ids
    },
    updateProjectSelectedId(id) {
      this.projectSelectedId = id
    },
    async fetchMuseToken() {
      try {
        if (this.museToken) {
          return this.museToken
        }
        const localToken = window.localStorage.getItem('museToken')
        if (localToken) {
          this.museToken = localToken
          return localToken
        }
        const res = await getMuseToken()
        const newToken = res.access_token
        this.museToken = newToken
        window.localStorage.setItem('museToken', newToken)
        return newToken
      } catch (error) {
        console.error('获取 token 失败:', error)
        throw error
      }
    },
    setMuseAssetsList(list) {
      this.museAssetsList = list
    },
    setMuseAssetDetail(item) {
      this.museAssetDetail = item
    },
    setMuseKeyword(keyword) {
      this.museKeyword = keyword
    },
    setMuseSearchList(list) {
      this.museSearchList = list
    },
    setMuseRepositoryList(list) {
      this.museRepositoryList = list
    },
    // muse sider 状态管理方法
    loadMuseSiderState() {
      try {
        const savedState = window.localStorage.getItem('museSiderState')
        if (savedState) {
          const state = JSON.parse(savedState)
          this.museSiderState = { ...this.museSiderState, ...state }
        }
      } catch (error) {
        console.error('加载 muse sider 状态失败:', error)
      }
    },
    saveMuseSiderState() {
      try {
        window.localStorage.setItem('museSiderState', JSON.stringify(this.museSiderState))
      } catch (error) {
        console.error('保存 muse sider 状态失败:', error)
      }
    },
    setMuseSiderState(state) {
      this.museSiderState = { ...this.museSiderState, ...state }
      this.saveMuseSiderState()
    },
    getMuseSiderState() {
      return this.museSiderState
    },
    clearMuseSiderState() {
      this.museSiderState = {
        repositoryId: '',
        repositoryCode: '',
        curNodeId: '',
        curNode: null
      }
      window.localStorage.removeItem('museSiderState')
    },
    // muse 缓存数据管理方法
    loadMuseCacheData() {
      try {
        const savedCache = window.localStorage.getItem('museCacheData')
        if (savedCache) {
          const cache = JSON.parse(savedCache)
          // 检查缓存是否过期（24小时）
          const now = Date.now()
          const cacheAge = now - (cache.cacheTimestamp || 0)
          const maxAge = 24 * 60 * 60 * 1000 // 24小时

          if (cacheAge < maxAge) {
            this.museCacheData = { ...this.museCacheData, ...cache }
            console.log('从缓存加载 muse 数据:', cache)
          } else {
            console.log('muse 缓存已过期，将重新获取数据')
            this.clearMuseCacheData()
          }
        }
      } catch (error) {
        console.error('加载 muse 缓存数据失败:', error)
      }
    },
    saveMuseCacheData() {
      try {
        this.museCacheData.cacheTimestamp = Date.now()
        window.localStorage.setItem('museCacheData', JSON.stringify(this.museCacheData))
      } catch (error) {
        console.error('保存 muse 缓存数据失败:', error)
      }
    },
    setMuseRepositoryListCache(list) {
      this.museCacheData.repositoryList = list
      this.museRepositoryList = list
      this.saveMuseCacheData()
    },
    getMuseRepositoryListCache() {
      return this.museCacheData.repositoryList
    },
    setMuseFolderTreeCache(repositoryCode, tree) {
      this.museCacheData.folderTrees[repositoryCode] = tree
      this.saveMuseCacheData()
    },
    getMuseFolderTreeCache(repositoryCode) {
      return this.museCacheData.folderTrees[repositoryCode]
    },
    clearMuseCacheData() {
      this.museCacheData = {
        repositoryList: [],
        folderTrees: {},
        cacheTimestamp: null
      }
      window.localStorage.removeItem('museCacheData')
    },

    // 云盘组列表缓存管理方法
    loadCloudGroupsCache() {
      try {
        const savedCache = window.localStorage.getItem('cloudGroupsCache')
        if (savedCache) {
          const cache = JSON.parse(savedCache)
          // 检查缓存是否过期（1小时）
          const now = Date.now()
          const cacheAge = now - (cache.cacheTimestamp || 0)
          const maxAge = 60 * 60 * 1000 // 1小时

          if (cacheAge < maxAge) {
            this.cloudGroupsCache = { ...this.cloudGroupsCache, ...cache }
            console.log('从缓存加载云盘组列表:', cache)
          } else {
            console.log('云盘组列表缓存已过期，将重新获取数据')
            this.clearCloudGroupsCache()
          }
        }
      } catch (error) {
        console.error('加载云盘组列表缓存失败:', error)
      }
    },

    saveCloudGroupsCache() {
      try {
        this.cloudGroupsCache.cacheTimestamp = Date.now()
        window.localStorage.setItem('cloudGroupsCache', JSON.stringify(this.cloudGroupsCache))
      } catch (error) {
        console.error('保存云盘组列表缓存失败:', error)
      }
    },

    setCloudGroupListCache(groupList, projectGroupList) {
      this.cloudGroupsCache.groupList = groupList || []
      this.cloudGroupsCache.projectGroupList = projectGroupList || []
      this.saveCloudGroupsCache()
      console.log('云盘组列表已缓存:', { groupList, projectGroupList })
    },

    getCloudGroupListCache() {
      return {
        groupList: this.cloudGroupsCache.groupList,
        projectGroupList: this.cloudGroupsCache.projectGroupList
      }
    },

    clearCloudGroupsCache() {
      this.cloudGroupsCache = {
        groupList: [],
        projectGroupList: [],
        cacheTimestamp: null
      }
      window.localStorage.removeItem('cloudGroupsCache')
    }
  }
})

Vue.prototype.$bus = Bus
