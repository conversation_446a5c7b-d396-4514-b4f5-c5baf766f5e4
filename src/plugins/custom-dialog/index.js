import Vue from 'vue'
import Main from './main.vue'

const DialogConstructor = Vue.extend(Main)
let customDialogInstances = null
const customDialog = (options) => {
  const instance = customDialogInstances || new DialogConstructor({
    data: {
      ...options
    },
    methods: {
      remove () {
        this.$el.parentNode.removeChild(this.$el)
        customDialogInstances = null
        this.$bus.$emit('customDialogClosed')
      }
    }
  })
  instance.vm = instance.$mount()
  document.body.appendChild(instance.vm.$el)
  customDialogInstances = instance
}
const customDialogRemove = () => {
  customDialogInstances && customDialogInstances.vm.$el.parentNode.removeChild(customDialogInstances.vm.$el)
  customDialogInstances = null
}
Vue.prototype.$CustomDialog = customDialog
Vue.prototype.$CustomDialogRemove = customDialogRemove
