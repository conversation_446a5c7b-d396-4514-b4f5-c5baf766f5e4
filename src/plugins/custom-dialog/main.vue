<script>
export default {
  name: 'CustomToast',
  data() {
    return {
      type: 'default',
      title: '提示',
      content: 'Toast',
      closabled: true,
      width: 264,
      btnText: '确定',
      onBtnClick: null,
      versionData: null,
      custom: false,
      customParams: {},
      style: null
    }
  },
  render(h) {
    return (
      <div class="custom-dialog-container" onClick={(e) => this.clicked(e)}>
        {this.custom && this.contRender ? (
          <div
            class="custom-dialog-wraper custom"
            style={{ width: `${this.width}`, ...this.style }}
          >
            {this.contRender(h, this.handleClose)}
          </div>
        ) : (
          <div
            class="custom-dialog-wraper"
            style={{ width: `${this.width}px` }}
          >
            {/* 头部 */}
            <div class="custom-dialog-title">
              <div class="title">{this.title}</div>
              {this.closabled && (
                <IconFont
                  icon="close"
                  class="dialog-btn-close"
                  size="18"
                  onClick={() => this.remove()}
                />
              )}
            </div>

            {/* PS中打开文件大小不一致提示 */}
            {this.type === 'PSOpenTip' && (
              <div class="custom-dialog-cont" style={{ textAlign: 'center' }}>
                <p>已为你从本地打开此文件</p>
                <p style="font-weight:bold">
                  系统检测：该文件和网盘中文件版本不同，请及时上传更新
                </p>
                <p>{this.customParams.name}</p>

                <div>
                  <div class="btn-group">
                    <GButton
                      style="margin-right:24px"
                      shape="circle"
                      onClick={() => this.remove()}
                      type="info"
                      ghost
                    >
                      取消
                    </GButton>

                    <GButton
                      shape="circle"
                      onClick={() => this.onBtnClick && this.onBtnClick()}
                      type="primary"
                    >
                      上传至网盘
                    </GButton>
                  </div>
                </div>
              </div>
            )}

            {/* 检测版本更新 */}
            {this.type === 'versionInfo' && (
              <div class="custom-dialog-cont">
                <div class="update-cont">
                  <div class="title">
                    🎉 V{this.versionData.version}版本上线啦！
                  </div>
                  <div class="btn-group">
                    {!this.versionData.forceUpdate ? (
                      <GButton
                        style="margin-right:12px"
                        shape="circle"
                        onClick={() => this.handleClose()}
                        type="info"
                        ghost
                      >
                        下次再说
                      </GButton>
                    ) : null}

                    <GButton
                      shape="circle"
                      onClick={() => this.toDownload(this.versionData)}
                      type="primary"
                    >
                      立即更新
                    </GButton>
                  </div>
                  <div class="cont">
                    <div class="cont-title">📋 更新内容：</div>
                    <div>
                      {this.versionData.description &&
                        this.versionData.description.map((item, index) => (
                          <div>
                            {index + 1}. {item}
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* 默认展示 */}
            {this.type === 'default' && (
              <div class="custom-dialog-cont">
                {this.contRender ? (
                  <div>{this.contRender(h, this.handleClose)}</div>
                ) : (
                  <div>
                    <div class="cont-text">{this.content}</div>
                    <div class="btn-group">
                      <GButton
                        shape="circle"
                        onClick={() => this.onBtnClick && this.onBtnClick()}
                        type="primary"
                      >
                        {this.btnText}
                      </GButton>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    )
  },
  methods: {
    handleClose() {
      this.remove()
    },
    toDownload(item) {
      this.$logMsg.info('[custom-dialog] 开始下载最新版本包', 'Plugin')

      const osInfo = this.$csInterface.getOSInformation()
      if (osInfo.indexOf('Mac OS X') > -1) {
        item.macDownloadUrl &&
        this.$csInterface.openURLInDefaultBrowser(item.macDownloadUrl)
      }
      if (osInfo.indexOf('Windows') > -1) {
        item.windowsDownloadUrl &&
        this.$csInterface.openURLInDefaultBrowser(item.windowsDownloadUrl)
      }
    },
    clicked(e) {
      e.stopPropagation()
      this.$bus.$emit('customDialogClicked', e)
    }
  }
}
</script>
<style lang="less" scoped>
.custom-dialog-container {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  outline: 0;
  background: rgba(0, 0, 0, 0.6);
  overflow-y: auto;
  text-align: center;
  z-index: 2001;

  &::before {
    display: inline-block;
    vertical-align: middle;
    width: 0;
    height: 100%;
    content: '';
  }

  .custom-dialog-wraper {
    position: relative;
    top: 0;
    display: inline-block;
    vertical-align: middle;
    margin: 20px auto;
    text-align: left;
    border-radius: 3px;
    background: #535353;
    border: 1px solid #383838;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);

    &.custom {
      border-radius: 0px;
      background: transparent;
      border: none;
      box-shadow: none;
    }

    .custom-dialog-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 32px;
      padding: 7px 16px;
      background: #4d4d4d;
      border-bottom: 1px solid #383838;
      font-size: 12px;
      font-weight: normal;

      .title {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .dialog-btn-close {
        cursor: pointer;
        float: right;
        transform: translateX(3px);
      }
    }

    .custom-dialog-cont {
      padding: 16px;

      .cont-text {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        text-align: center;
        margin-bottom: 16px;
      }

      .btn-group {
        text-align: center;
      }

      .update-cont {
        .title {
          font-size: 20px;
          font-weight: bold;
          text-align: center;
        }

        .btn-group {
          margin: 16px 0;
          text-align: center;
        }

        .cont {
          margin: 8px 0;
          padding: 16px;
          background-color: #5a5a5a;
          border-radius: 4px;
          font-size: 12px;
          line-height: 22px;

          .cont-title {
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
