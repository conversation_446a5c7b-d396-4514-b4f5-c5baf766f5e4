import Vue from 'vue'
import Main from './main.vue'

const ToastConstructor = Vue.extend(Main)
let customToastInstances = []
const customToast = (options) => {
  const instance = new ToastConstructor({
    data: {
      ...options
    },
    methods: {
      remove () {
        this.$el.parentNode.removeChild(this.$el)
        customToastInstances.shift()
      }
    }
  })
  instance.vm = instance.$mount()
  document.body.appendChild(instance.vm.$el)
  customToastInstances.push(instance)
}
const customToastRemove = () => {
  customToastInstances.forEach(instance => {
    instance.vm.$el.parentNode.removeChild(instance.vm.$el)
  })
  customToastInstances = []
}
Vue.prototype.$CustomToast = customToast
Vue.prototype.$CustomToastRemove = customToastRemove
