<template>
  <div
    :class="{
      'spectrum-Toast--info': type === 'info',
      'spectrum-Toast--negative': type === 'error',
      'spectrum-Toast--positive': type === 'success',
      'spectrum-Toast--warning': type === 'warning'
    }"
    class="custom-toast-container spectrum-Toast"
  >
    <template v-if="type === 'error' || type === 'warning'">
      <svg class="spectrum-Icon spectrum-Icon--sizeM spectrum-Toast-typeIcon" focusable="false" aria-hidden="true">
        <!-- <use xlink:href="#spectrum-icon-18-Alert" /> -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="18"
          viewBox="0 0 18 18"
          width="18"
        >
          <rect
            id="Canvas"
            fill="#ff13dc"
            opacity="0"
            width="18"
            height="18"
          />
          <path class="a" d="M8.5635,1.2895.2,16.256A.5.5,0,0,0,.636,17H17.364a.5.5,0,0,0,.436-.744L9.4365,1.2895a.5.5,0,0,0-.873,0ZM10,14.75a.25.25,0,0,1-.25.25H8.25A.25.25,0,0,1,8,14.75v-1.5A.25.25,0,0,1,8.25,13h1.5a.25.25,0,0,1,.25.25Zm0-3a.25.25,0,0,1-.25.25H8.25A.25.25,0,0,1,8,11.75v-6a.25.25,0,0,1,.25-.25h1.5a.25.25,0,0,1,.25.25Z" />
        </svg>
      </svg>
    </template>
    <template v-else-if="type === 'info'">
      <svg class="spectrum-Icon spectrum-Icon--sizeM spectrum-Toast-typeIcon" focusable="false" aria-hidden="true">
        <!-- <use xlink:href="#spectrum-icon-18-Info" /> -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="18"
          viewBox="0 0 18 18"
          width="18"
        >
          <rect
            id="Canvas"
            fill="#ff13dc"
            opacity="0"
            width="18"
            height="18"
          />
          <path class="a" d="M9,1a8,8,0,1,0,8,8A8,8,0,0,0,9,1ZM8.85,3.15a1.359,1.359,0,0,1,1.43109,1.28286q.00352.06452.00091.12914A1.332,1.332,0,0,1,8.85,5.9935a1.3525,1.3525,0,0,1-1.432-1.432A1.3585,1.3585,0,0,1,8.72033,3.14907Q8.78516,3.14643,8.85,3.15ZM11,13.5a.5.5,0,0,1-.5.5h-3a.5.5,0,0,1-.5-.5v-1a.5.5,0,0,1,.5-.5H8V9H7.5A.5.5,0,0,1,7,8.5v-1A.5.5,0,0,1,7.5,7h2a.5.5,0,0,1,.5.5V12h.5a.5.5,0,0,1,.5.5Z" />
        </svg>
      </svg>
    </template>
    <template v-else-if="type === 'success'">
      <svg class="spectrum-Icon spectrum-Icon--sizeM spectrum-Toast-typeIcon" focusable="false" aria-hidden="true">
        <!-- <use xlink:href="#spectrum-icon-18-CheckmarkCircle" /> -->
        <svg
          xmlns="http://www.w3.org/2000/svg"
          height="18"
          viewBox="0 0 18 18"
          width="18"
        >
          <rect
            id="Canvas"
            fill="#ff13dc"
            opacity="0"
            width="18"
            height="18"
          />
          <path class="a" d="M9,1a8,8,0,1,0,8,8A8,8,0,0,0,9,1Zm5.333,4.54L8.009,13.6705a.603.603,0,0,1-.4375.2305H7.535a.6.6,0,0,1-.4245-.1755L3.218,9.829a.6.6,0,0,1-.00147-.84853L3.218,8.979l.663-.6625A.6.6,0,0,1,4.72953,8.315L4.731,8.3165,7.4,10.991l5.257-6.7545a.6.6,0,0,1,.8419-.10586L13.5,4.1315l.7275.5685A.6.6,0,0,1,14.333,5.54Z" />
        </svg>
      </svg>
    </template>
    <div class="spectrum-Toast-body">
      <div class="spectrum-Toast-content toast-title" v-if="title" style="{width: 100%; text}">{{ title }}</div>
      <div class="spectrum-Toast-content">
        {{ content }}
        <span class="toast-ref" v-if="refText" @click="handleRef">{{ refText }}</span>
      </div>
    </div>
    <div class="spectrum-Toast-buttons">
      <button class="spectrum-ClearButton spectrum-ClearButton--medium spectrum-ClearButton--overBackground" @click="handleClose">
       <IconFont
          icon='close'
          size='16'
        />
      </button>
    </div>
  </div>
</template>
<script>
export default {
  name: 'CustomToast',
  data () {
    return {
      type: '',
      content: 'Toast',
      duration: 2,
      hide: false,
      title: '',
      refText: '',
      handleRef: () => {}
    }
  },
  mounted () {
    this.$nextTick(() => {
      setTimeout(() => {
        this.hide = true
        setTimeout(() => {
          this.remove()
        }, 500)
      }, this.duration * 1000)
    })
  },
  // render (h) {
  //   return (
  //     <div class="custom-toast-container">
  //       {
  //         this.render
  //           ? <div class={ this.hide ? '' : '' }>{this.render()}</div>
  //           : <div class={ this.hide ? 'custom-toast-content hide' : 'custom-toast-content'}>{this.content}</div>
  //       }
  //     </div>
  //   )
  // },
  methods: {
    handleClose () {
      this.remove()
    }
  }
}
</script>
<style lang="less" scoped>
.custom-toast-container {
  position: fixed;
  width: fit-content;
  left: 50%;
  top: 20px;
  transform: translateX(-50%);
  overflow: hidden;
  z-index: 4001;
  .custom-toast-content {
    padding: 16px 30px;
    border-radius: 4px;
    font-family: PingFangSC-Regular;
    font-size: 18px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    line-height: 24px;
    background: rgba(34,34,34,0.80);
    &.hide {
      opacity: 0;
      transform: translateX(-100%);
      transition: all .5s;
    }
  }

  .toast-title {
    width: 100%;
    text-align: center;
  }

  .toast-ref {
    color: #fff;
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
