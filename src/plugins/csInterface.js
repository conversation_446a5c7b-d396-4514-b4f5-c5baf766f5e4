/* eslint-disable no-undef */
import { MATERIAL_SOURCE_PATHNAME } from '@/config/index.js'
import { fileIsExist, pathIsExist } from '@/utils/fs/index.js'
import moment from 'moment/moment'
import Vue from 'vue'

const PATH = require('path')
const fs = custom_node.fs

// eslint-disable-next-line no-undef
Vue.prototype.$csInterface = new CSInterface()

Vue.prototype.$saveFile = () => {
  const cs = new CSInterface()

  window.$logMsg.info('[$saveFile] 保存本地文件', 'Vue.prototype')

  return new Promise((resolve, reject) => {
    // 先检查是否有活动文档
    cs.evalScript('app.documents.length', (docCount) => {
      if (!docCount || docCount === '0') {
        window.$logMsg.error('[$saveFile] 没有打开的文档', 'Vue.prototype')
        reject(new Error('没有打开的文档'))
        return
      }

      // 检查文档是否需要保存
      cs.evalScript('app.activeDocument.saved', (saved) => {
        if (saved === 'true') {
          window.$logMsg.info('[$saveFile] 文档已保存，无需重复保存', 'Vue.prototype')
          resolve()
          return
        }

        // 执行保存操作
        cs.evalScript('app.activeDocument.save()', (res) => {
          if (res === 'EvalScript error.') {
            window.$logMsg.error('[$saveFile] 文档保存失败，可能是新建文档未指定保存路径', 'Vue.prototype')
            reject(new Error('文档保存失败，请先手动保存文档'))
          } else {
            window.$logMsg.info('[$saveFile] 保存本地文件成功', 'Vue.prototype')
            resolve()
          }
        })
      })
    })
  })
}

Vue.prototype.$getActivePath = () => {
  const cs = new CSInterface()
  const osInfo = cs.getOSInformation()

  window.$logMsg.info('[$getActivePath] 获取文件路径', 'Vue.prototype')

  return new Promise((resolve, reject) => {
    cs.evalScript('app.activeDocument.fullName', (path) => {
      if (path === 'EvalScript error.') {
        window.$logMsg.error('[$getActivePath] 获取文件路径失败', 'Vue.prototype')

        reject(new Error('当前文件无法被保存'))
      }

      cs.evalScript('app.activeDocument.name', (name) => {
        if (name === 'EvalScript error.') {
          window.$logMsg.error('[$getActivePath] 获取app.activeDocument.name失败', 'Vue.prototype')
          reject(new Error('当前文件无法被保存'))
        }
        if (osInfo.indexOf('Windows') > -1) {
          const rePath = [decodeURI(PATH.resolve(path).split('/').slice(1).map((v, i) => {
            // 修复 Windows 盘符
            if (i === 0 && v !== '~') {
              return `${v}:`
            }

            // 更换用户路径
            if (v === '~') {
              // eslint-disable-next-line no-undef
              return PATH.resolve(cs.getSystemPath(SystemPath.USER_DATA), '..', '..').slice(1)
            }

            return v
          }).join('/')), name]

          window.$logMsg.info(`[$getActivePath] 获取文件路径成功: path:${rePath[0]};name:${rePath[1]}`, 'Vue.prototype')

          resolve(rePath)
        } else {
          let rePath = path
          // eslint-disable-next-line no-undef
          const userDataPath = PATH.resolve(cs.getSystemPath(SystemPath.USER_DATA), '..', '..')
          if (path.includes('~')) {
            rePath = path.replace('~', userDataPath)
          }
          window.$logMsg.info(`[$getActivePath] 获取文件路径成功,path:${decodeURI(rePath)};name:${name}`, 'Vue.prototype')

          resolve([decodeURI(rePath), name])
        }
      })
    })
  })
}

Vue.prototype.$activeDocument = () => {
  window.$logMsg.info('[$activeDocument] 获取activeDocument', 'Vue.prototype')

  return new Promise((resolve, reject) => {
    const cs = new CSInterface()
    cs.evalScript('app.activeDocument', (res) => {
      if (res === 'EvalScript error.') {
        window.$logMsg.error('[$activeDocument] 获取activeDocument失败', 'Vue.prototype')

        resolve(undefined)
      } else {
        window.$logMsg.info('[$activeDocument] 获取activeDocument成功', 'Vue.prototype')

        resolve(res)
      }
    })
  })
}

// 强制保存文档（如果是新建文档，会弹出保存对话框，但这是必要的）
Vue.prototype.$forceSaveDocument = () => {
  window.$logMsg.info('[$forceSaveDocument] 强制保存文档', 'Vue.prototype')

  return new Promise((resolve, reject) => {
    const cs = new CSInterface()

    // 先检查文档是否有路径
    cs.evalScript('app.activeDocument.fullName', (fullName) => {
      if (fullName === 'EvalScript error.' || !fullName || fullName === 'undefined') {
        // 文档没有保存路径，这是新建文档，需要用户指定保存位置
        window.$logMsg.info('[$forceSaveDocument] 新建文档，需要用户指定保存位置', 'Vue.prototype')

        // 对于新建文档，我们必须让用户选择保存位置
        // 这会弹出保存对话框，但这是合理的
        cs.evalScript('app.activeDocument.name', (name) => {
          const userData = cs.getSystemPath(SystemPath.USER_DATA)
          const tempDir = PATH.join(userData, 'PSTmp')
          const filename = `${name}.psd`

          fs.exists(tempDir, (exists) => {
            const callback = () => {
              const targetDir = PATH.join(tempDir, moment().format('YYYYMMDDHHmmss'))
              const savePath = PATH.join(targetDir, filename)
              fs.mkdir(targetDir, (err) => {
                if (err) {
                  reject(err)
                } else {
                  // 使用save()而不是saveAs()来避免对话框
                  cs.evalScript(`
                    try {
                      app.activeDocument.saveAs(new File("${savePath}"));
                      app.activeDocument.fullName.fsName;
                    } catch(e) {
                      "EvalScript error.";
                    }
                  `, (res) => {
                    if (res === 'EvalScript error.') {
                      window.$logMsg.error('[$forceSaveDocument] 保存失败', 'Vue.prototype')
                      reject(new Error('文档保存失败'))
                    } else {
                      window.$logMsg.info('[$forceSaveDocument] 新建文档保存成功', 'Vue.prototype')
                      resolve([savePath, filename])
                    }
                  })
                }
              })
            }

            if (!exists) {
              fs.mkdir(tempDir, callback)
            } else {
              callback()
            }
          })
        })
      } else {
        // 文档已有保存路径，直接保存
        cs.evalScript('app.activeDocument.save()', (res) => {
          if (res === 'EvalScript error.') {
            window.$logMsg.error('[$forceSaveDocument] 保存失败', 'Vue.prototype')
            reject(new Error('文档保存失败'))
          } else {
            window.$logMsg.info('[$forceSaveDocument] 文档保存成功', 'Vue.prototype')
            // 重新获取路径信息
            cs.evalScript('app.activeDocument.fullName', (path) => {
              cs.evalScript('app.activeDocument.name', (name) => {
                const osInfo = cs.getOSInformation()
                if (osInfo.indexOf('Windows') > -1) {
                  const rePath = [decodeURI(PATH.resolve(path).split('/').slice(1).map((v, i) => {
                    if (i === 0 && v !== '~') {
                      return `${v}:`
                    }
                    if (v === '~') {
                      return PATH.resolve(cs.getSystemPath(SystemPath.USER_DATA), '..', '..').slice(1)
                    }
                    return v
                  }).join('/')), name]
                  resolve(rePath)
                } else {
                  let rePath = path
                  const userDataPath = PATH.resolve(cs.getSystemPath(SystemPath.USER_DATA), '..', '..')
                  if (path.includes('~')) {
                    rePath = path.replace('~', userDataPath)
                  }
                  resolve([decodeURI(rePath), name])
                }
              })
            })
          }
        })
      }
    })
  })
}

Vue.prototype.$saveTempDocument = () => {
  window.$logMsg.info('[$saveTempDocument] 保存模板文件', 'Vue.prototype')

  return new Promise((resolve, reject) => {
    const cs = new CSInterface()
    const userData = cs.getSystemPath(SystemPath.USER_DATA)
    cs.evalScript('app.activeDocument.name', (name) => {
      const filename = `${name}.psd`

      const tempDir = PATH.join(userData, 'PSTmp')
      fs.exists(tempDir, (exists) => {
        const callback = () => {
          const targetDir = PATH.join(tempDir, moment().format('YYYYMMDDHHmmss'))
          const savePath = PATH.join(targetDir, filename)
          fs.mkdir(targetDir, (err) => {
            if (err) {
              reject(err)
            } else {
              console.log('🚀 ~ file: csInterface.js:81 ~ callback ~ savePath:', savePath)
              cs.evalScript(`app.activeDocument.saveAs(new File("${savePath}"))`, (res) => {
                if (res === 'EvalScript error.') {
                  window.$logMsg.error('[$saveTempDocument] 保存失败', 'Vue.prototype')

                  reject(new Error('当前没有创建画布'))
                } else {
                  window.$logMsg.info('[$saveTempDocument] 保存成功', 'Vue.prototype')

                  resolve([savePath, filename])
                }
              })
            }
          })
        }

        if (!exists) {
          fs.mkdir(tempDir, callback)
        } else {
          callback()
        }
      })
    })
  })
}

Vue.prototype.$csCompose = () => {
  return new Promise((resolve, reject) => {
    // eslint-disable-next-line no-undef
    const cs = new CSInterface()

    window.$logMsg.info('[$csCompose] 开始准备画布导出', 'Vue.prototype')

    // 检查是否有活动文档
    cs.evalScript('app.documents.length', (docCount) => {
      if (!docCount || docCount === '0') {
        window.$logMsg.error('[$csCompose] 没有打开的文档', 'Vue.prototype')
        reject(new Error('没有打开的文档'))
        return
      }

      // 清理文档元数据（保留原有逻辑）
      cs.evalScript('deleteDocumentAncestorsMetadata()', () => {
        window.$logMsg.info('[$csCompose] deleteDocumentAncestorsMetadata 执行成功', 'Vue.prototype')

        // 由于现在保存整个画布，不再需要特定的动作组
        // 直接返回成功，表示画布准备完成
        window.$logMsg.info('[$csCompose] 画布准备完成', 'Vue.prototype')
        resolve('画布准备成功')
      })
    })
  })
}

Vue.prototype.$csSave = () => {
  return new Promise((resolve, reject) => {
    // eslint-disable-next-line no-undef
    const cs = new CSInterface()
    // eslint-disable-next-line no-undef
    const fileUrl = `${cs.getSystemPath(SystemPath.USER_DATA)}/${MATERIAL_SOURCE_PATHNAME}`
    window.$logMsg.info(`[$csSave] 开始保存整个画布：${fileUrl}`, 'Vue.prototype')

    pathIsExist(fileUrl).then(() => {
      cs.evalScript(`saveSmartObjectAsPSB('${fileUrl}')`, d3 => {
        const reg = new RegExp('.psd')
        if (d3 !== 'failed' && d3 !== 'no-document' && reg.test(d3)) {
          window.$logMsg.info('[$csSave] 整个画布保存成功', 'Vue.prototype')
          resolve(d3)
        } else if (d3 === 'no-document') {
          window.$logMsg.error('[$csSave] 没有打开的文档', 'Vue.prototype')
          reject(new Error('没有打开的文档'))
        } else {
          window.$logMsg.error('[$csSave] 画布保存失败', 'Vue.prototype')
          reject(new Error('画布保存失败'))
        }
      })
    }).catch(() => {
      window.$logMsg.error('[$csSave] 文件夹不存在', 'Vue.prototype')
      reject(new Error('文件夹不存在'))
    })
  })
}
// 进行二次压缩操作操作，当文件大于200M的时候进行
Vue.prototype.$compressConfirm = (fileName) => {
  return new Promise((resolve, reject) => {
    // eslint-disable-next-line no-undef
    const cs = new CSInterface()
    // eslint-disable-next-line no-undef
    const filePath = `${cs.getSystemPath(SystemPath.USER_DATA)}/${MATERIAL_SOURCE_PATHNAME}`
    window.$logMsg.info(`[$compressConfirm] ${filePath}开始二次压缩`, 'Vue.prototype')

    fileIsExist(filePath, fileName).then((d) => {
      const fileSize = d.size
      if (fileSize > 100 * 1024 * 1024) {
        cs.evalScript('psDeepCleanerMetaData()', (d) => {
          window.$logMsg.info(`[$compressConfirm] ${filePath}二次压缩成功`, 'Vue.prototype')

          if (d !== 'failed') {
            resolve(fileName)
          } else {
            resolve()
          }
        })
      } else {
        window.$logMsg.info(`[$compressConfirm] ${filePath}不需要二次压缩`, 'Vue.prototype')

        resolve()
      }
    })
  })
}
