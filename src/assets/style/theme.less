.spectrum-Popover {
  background-color: var(--spectrum-popover-background-color, var(--spectrum-global-color-gray-300));
}

.spectrum-Checkbox-box:before {
  forced-color-adjust: none;
  border-color: var(--spectrum-checkbox-m-box-border-color, var(--spectrum-global-color-gray-600));
  background-color: var(--spectrum-checkbox-m-box-background-color, var(--spectrum-global-color-gray-300));
}

.spectrum-Thumbnail:before {
  box-shadow: none;
}

.spectrum-Divider--sizeM {
  background-color: var(--spectrum-global-color-static-gray-900);
}

.spectrum-Textfield {
  &:hover {
    .spectrum-Textfield-input {
      &::placeholder {
        color: #919191;
      }
    }
  }
}
