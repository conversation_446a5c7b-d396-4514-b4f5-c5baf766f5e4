@import url('./theme.less');

body, html {
  width: 100%;
  height: 100%;
  padding: 0;
  margin: 0;
  font-family: var(--spectrum-alias-body-text-font-family, var(--spectrum-global-font-family-base));
  background-color: var(--spectrum-global-color-gray-300);
}

* {
  box-sizing: border-box;
}

*::before, *::after {
 box-sizing: border-box;
}

#app {
  height: 100%;
  width: 100%;
  font-size: 12px;
  box-sizing: border-box;
}

.page-index {
  display: flex;
  height: 100%;
  text-align: center;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}
.p-list {
  display: flex;
  width: 100%;
  height: 100%;
}
.m-input {
  display: block;
  margin-bottom: 16px;
  padding: 0 20px;
  height: 32px;
  background: transparent;
  border: 2px solid #ffffff;
  border-radius: 32px;
  color: #ffffff;
  &:focus {
    outline: none;
  }
}

.g-button {
  background: #373737;
  border-color: #636363;
  color: #ffffff;
  cursor: pointer;
  &:hover {
    background: #373737;
    border-color: #818181;
    color: #ffffff;
  }
  &:active, &:focus {
    background: #3a3a3a;
    border-color: #818181;
    color: #ffffff;
  }
}
.g-tag {
  background: transparent;
  border-color: #3e3e3e;
  color: #c7c7c7;
  &:hover {
    background: transparent;
    border-color: #818181;
    color: #e3e3e3;
  }
}
.g-select {
  background: #373737;
  border-color: #666666;
  color: #ffffff;
  &:hover {
    background: #3a3a3a;
    border-color: #818181;
    color: #ffffff;
  }
  &.is-focus {
    background: #595959;
    border-color: #378ef0;
    color: #ffffff;
  }
}

.g-input {
  background: #373737;
  color: #ffffff;
  &:focus, &:active {
    background: #3a3a3a;
    border-color: #378ef0;
  }
}

.f-12 {
  font-size: 12px;
}

.f-16 {
  font-size: 16px;
}

.f-18 {
  font-size: 18px;
}

.f-24 {
  font-size: 24px;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

button.btn-default {
  padding: 0 16px;
  background: transparent;
  border-color: #3E3E3F;
  color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    background: transparent;
    border-color: #818181;
    color: #ffffff;
  }
  &:active, &:focus {
    background: #3a3a3a;
    border-color: #818181;
    color: #ffffff;
  }
}

button.btn-primary {
  padding: 0 16px;
  background: #1373E6;
  border-color: #0E56AD;
  color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  &:hover {
    background: #197EFA;
    border-color: #0E56AD;
    color: #ffffff;
  }
  &:active, &:focus {
    background: #1065CC;
    border-color: #0E56AD;
    color: #ffffff;
  }
}

.rotate180{
  transform: rotate(180deg);
}
