/* eslint-disable no-undef */
/**
 * TUS上传工具函数
 * 基于TUS协议实现可恢复的文件上传
 */

const fs = custom_node.fs
const path = require('path')

/**
 * 创建TUS上传实例
 * @param {Object} options 上传配置
 * @param {string} options.filePath - 文件路径
 * @param {string} options.uploadUrl - 上传URL
 * @param {string} options.token - 上传token
 * @param {Function} options.onProgress - 进度回调
 * @param {Function} options.onSuccess - 成功回调
 * @param {Function} options.onError - 错误回调
 * @returns {Object} TUS上传实例
 */
export function createTusUpload(options) {
  const {
    filePath,
    uploadUrl,
    token,
    onProgress,
    onSuccess,
    onError,
    customFileName, // 自定义文件名
    metadata = {}
  } = options

  // 检查文件路径是否有效
  if (!filePath || typeof filePath !== 'string') {
    throw new Error(`无效的文件路径: ${filePath}`)
  }

  // 清理文件路径
  const cleanFilePath = filePath.trim().replace(/^["']|["']$/g, '')
  console.log('TUS上传 - 清理后的文件路径:', cleanFilePath)

  // 检查文件是否存在
  if (!fs.existsSync(cleanFilePath)) {
    console.error('TUS上传 - 文件不存在:', cleanFilePath)
    // 尝试列出目录内容以帮助调试
    const dir = path.dirname(cleanFilePath)
    if (fs.existsSync(dir)) {
      const files = fs.readdirSync(dir)
      console.log('目录内容:', files)
    }
    throw new Error(`文件不存在: ${cleanFilePath}`)
  }

  // 获取文件信息
  const fileStats = fs.statSync(cleanFilePath)
  const fileSize = fileStats.size
  const originalFileName = path.basename(cleanFilePath)
  const fileName = customFileName || originalFileName // 使用自定义文件名或原始文件名

  console.log('TUS上传 - 文件信息:', {
    originalFileName,
    customFileName,
    finalFileName: fileName,
    fileSize,
    filePath: cleanFilePath
  })

  // 创建上传实例
  const upload = {
    filePath: cleanFilePath,
    uploadUrl,
    token,
    fileSize,
    fileName,
    uploadedBytes: 0,
    isAborted: false,
    isCompleted: false,
    // 保存回调函数
    onProgress,
    onSuccess,
    onError,
    
    // 开始上传
    async start() {
      try {
        window.$logMsg?.info?.(`[TUS] 开始上传文件: ${fileName}`, 'TUS上传') || console.log(`[TUS] 开始上传文件: ${fileName}`)

        // 创建上传会话
        const uploadLocation = await this.createUploadSession()

        // 开始分片上传
        await this.uploadFile(uploadLocation)

        this.isCompleted = true
        this.onSuccess && this.onSuccess({
          fileName: this.fileName,
          fileSize: this.fileSize,
          uploadUrl: uploadLocation
        })

      } catch (error) {
        window.$logMsg?.error?.(`[TUS] 上传失败: ${error.message}`, 'TUS上传') || console.error(`[TUS] 上传失败: ${error.message}`)
        this.onError && this.onError(error)
      }
    },

    // 创建上传会话
    async createUploadSession() {
      const headers = {
        'Tus-Resumable': '1.0.0',
        'Upload-Length': this.fileSize.toString(),
        'Upload-Metadata': this.buildMetadata({
          filename: this.fileName,
          ...metadata
        })
      }

      if (this.token) {
        headers.Authorization = this.token
        console.log('[TUS] 创建会话 - 添加Authorization头:', `${this.token.substring(0, 10)}...`)
      } else {
        console.warn('[TUS] 创建会话 - 没有token!')
      }

      console.log('[TUS] 创建会话请求头:', headers)
      console.log('[TUS] 创建会话URL:', this.uploadUrl)

      const response = await fetch(this.uploadUrl, {
        method: 'POST',
        headers
      })

      if (!response.ok) {
        throw new Error(`创建上传会话失败: ${response.status} ${response.statusText}`)
      }

      const location = response.headers.get('Location')
      if (!location) {
        throw new Error('服务器未返回上传位置')
      }

      window.$logMsg?.info?.(`[TUS] 创建上传会话成功: ${location}`, 'TUS上传') || console.log(`[TUS] 创建上传会话成功: ${location}`)
      return location
    },

    // 上传文件
    async uploadFile(uploadLocation) {
      const minChunkSize = 1024 * 1024 * 5 // 服务器要求最小5MB
      const maxChunkSize = 1024 * 1024 * 10 // 最大10MB分片

      // 如果文件小于最小分片大小，一次性上传整个文件
      let chunkSize
      if (this.fileSize <= minChunkSize) {
        chunkSize = this.fileSize
        console.log('[TUS] 文件较小，一次性上传整个文件，大小:', this.fileSize)
      } else {
        chunkSize = Math.min(maxChunkSize, Math.max(minChunkSize, Math.ceil(this.fileSize / 10)))
        console.log('[TUS] 使用分片上传，分片大小:', chunkSize)
      }

      let offset = 0

      while (offset < this.fileSize && !this.isAborted) {
        // 计算当前分片的实际大小
        const remainingSize = this.fileSize - offset
        const currentChunkSize = Math.min(chunkSize, remainingSize)

        const chunk = this.readFileChunk(offset, currentChunkSize)
        await this.uploadChunk(uploadLocation, chunk, offset)

        offset += chunk.length
        this.uploadedBytes = offset

        // 更新进度
        const progress = Math.round((offset / this.fileSize) * 100)
        this.onProgress && this.onProgress(progress, offset, this.fileSize)
      }

      if (this.isAborted) {
        throw new Error('上传被取消')
      }
    },

    // 上传分片
    async uploadChunk(uploadLocation, chunk, offset) {
      const headers = {
        'Tus-Resumable': '1.0.0',
        'Upload-Offset': offset.toString(),
        'Content-Type': 'application/offset+octet-stream'
      }

      if (this.token) {
        headers.Authorization = this.token
        console.log('[TUS] 上传分片 - 添加Authorization头:', `${this.token.substring(0, 10)}...`)
      } else {
        console.warn('[TUS] 上传分片 - 没有token!')
      }

      console.log('[TUS] 上传分片请求头:', headers)
      console.log('[TUS] 上传分片URL:', uploadLocation)
      console.log('[TUS] 上传分片偏移量:', offset, '分片大小:', chunk.length)

      const response = await fetch(uploadLocation, {
        method: 'PATCH',
        headers,
        body: chunk
      })

      if (!response.ok) {
        throw new Error(`上传分片失败: ${response.status} ${response.statusText}`)
      }

      const uploadOffset = parseInt(response.headers.get('Upload-Offset') || '0')
      if (uploadOffset !== offset + chunk.length) {
        throw new Error(`上传偏移量不匹配: 期望 ${offset + chunk.length}, 实际 ${uploadOffset}`)
      }
    },

    // 读取文件分片
    readFileChunk(offset, size) {
      const buffer = Buffer.alloc(Math.min(size, this.fileSize - offset))
      const fd = fs.openSync(this.filePath, 'r')
      try {
        fs.readSync(fd, buffer, 0, buffer.length, offset)
        return buffer
      } finally {
        fs.closeSync(fd)
      }
    },

    // 构建元数据
    buildMetadata(metadata) {
      const pairs = []
      for (const [key, value] of Object.entries(metadata)) {
        const encodedKey = key
        const encodedValue = Buffer.from(value.toString()).toString('base64')
        pairs.push(`${encodedKey} ${encodedValue}`)
      }
      return pairs.join(',')
    },

    // 暂停上传
    pause() {
      // TUS协议本身支持断点续传，这里只需要标记状态
      window.$logMsg?.info?.(`[TUS] 暂停上传: ${fileName}`, 'TUS上传') || console.log(`[TUS] 暂停上传: ${fileName}`)
    },

    // 恢复上传
    async resume() {
      if (this.isCompleted || this.isAborted) {
        return
      }
      
      window.$logMsg?.info?.(`[TUS] 恢复上传: ${fileName}`, 'TUS上传') || console.log(`[TUS] 恢复上传: ${fileName}`)
      // 可以通过HEAD请求获取当前上传进度，然后从断点继续
    },

    // 取消上传
    abort() {
      this.isAborted = true
      window.$logMsg?.info?.(`[TUS] 取消上传: ${fileName}`, 'TUS上传') || console.log(`[TUS] 取消上传: ${fileName}`)
    },

    // 获取上传进度
    getProgress() {
      return {
        uploadedBytes: this.uploadedBytes,
        totalBytes: this.fileSize,
        percentage: Math.round((this.uploadedBytes / this.fileSize) * 100)
      }
    }
  }

  return upload
}

/**
 * 检查服务器TUS支持
 * @param {string} uploadUrl - 上传URL
 * @returns {Promise<Object>} 服务器支持的TUS版本和扩展
 */
export async function checkTusSupport(uploadUrl) {
  try {
    const response = await fetch(uploadUrl, {
      method: 'OPTIONS'
    })

    return {
      version: response.headers.get('Tus-Version'),
      resumable: response.headers.get('Tus-Resumable'),
      extensions: response.headers.get('Tus-Extension')?.split(',') || [],
      maxSize: response.headers.get('Tus-Max-Size')
    }
  } catch (error) {
    window.$logMsg?.error?.(`[TUS] 检查服务器支持失败: ${error.message}`, 'TUS上传') || console.error(`[TUS] 检查服务器支持失败: ${error.message}`)
    throw error
  }
}
