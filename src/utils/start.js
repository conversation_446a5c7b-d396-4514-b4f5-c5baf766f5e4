/* eslint-disable no-undef */
import { VERSION, MATERIAL_SOURCE_PATHNAME, TIMETAMP } from '@/config'
import { logout, getCommonVersion } from '@/api/index.js'
import { deleteFolder } from '@/utils/fs/index.js'
import { myStorage } from '@/utils/index.js'
import app from '@/main'

const csInterface = new CSInterface()
const TIMETAMP_STR = TIMETAMP ? `(${TIMETAMP})` : ''

// 全局右键菜单
const menuContextXML =
  `<Menu>
  <MenuItem Id="refresh" Label="刷新页面" Enabled="true" Checked="false"/>
  <MenuItem Id="clear" Label="清理文件缓存" Enabled="true" Checked="false"/>
  <MenuItem Id="clearFile" Label="清理插件缓存" Enabled="true" Checked="false"/>
  <MenuItem Label="---" />
  <MenuItem Id="logout" Label="退出登录" Enabled="true" Checked="false"/>
  <MenuItem Id="download" Label="检测更新" Enabled="true" Checked="false"/>
  <MenuItem Id="version" Label="版本号：V${VERSION}${TIMETAMP_STR}" Enabled="false" Checked="false"/>
  <MenuItem Id="logger" Label="上报日志" Enabled="true" Checked="false"/>
  </Menu>`

csInterface.setContextMenu(menuContextXML, function (evt) {
  // 刷新页面
  if (evt === 'refresh') {
    location.reload()
  }
  // 清理文件缓存
  if (evt === 'clear') {
    csInterface.evalScript('clearDocMetaData()', function () {
      csInterface.evalScript('psAlert(\'完成缓存清理\')')
    })
  }
  // 清理插件缓存
  if (evt === 'clearFile') {
    const srcPath = `${csInterface.getSystemPath(SystemPath.USER_DATA)}/${MATERIAL_SOURCE_PATHNAME}`
    deleteFolder(srcPath).then(d => {
      app.$CustomToast({
        type: 'success',
        duration: 2,
        content: d.message
      })
    }).catch(e => {
      app.$CustomToast({
        type: 'error',
        duration: 2,
        content: '操作失败'
      })
    })
  }
  // 检测更新
  if (evt === 'download') {
    // csInterface.openURLInDefaultBrowser('http://*************:8888')
    app.$CustomDialog({
      title: '版本通知',
      content: '正在检查新版本...',
      btnText: '取消',
      onBtnClick: () => {
        app.$CustomDialogRemove()
      }
    })
    getCommonVersion().then(d => {
      app.$CustomDialogRemove()
      const { version } = d
      if (version > VERSION) {
        app.$CustomDialog({
          title: '版本通知',
          type: 'versionInfo',
          versionData: d
        })
      } else {
        app.$CustomDialog({
          title: '版本通知',
          content: `当前已是最新版本 V${version}`,
          btnText: '知道了',
          onBtnClick: () => {
            app.$CustomDialogRemove()
          }
        })
      }
    })
  }
  // 退出登陆
  if (evt === 'logout') {
    logout().then(() => {
      myStorage.remove('token')
      myStorage.remove('USERINFO')
      app.$router.push({
        name: 'index'
      })
    })
  }
  // 上报日志
  if (evt === 'logger') {
    window.$uploadLogs()
  }
})

// 插件右上角菜单
const menuFlyoutXML =
  `<Menu>
  <MenuItem Id="refresh" Label="刷新页面" Enabled="true" Checked="false"/>
  <MenuItem Id="clear" Label="清理文件缓存" Enabled="true" Checked="false"/>
  <MenuItem Id="clearFile" Label="清理插件缓存" Enabled="true" Checked="false"/>
  <MenuItem Label="---" />
  <MenuItem Id="logout" Label="退出登录" Enabled="true" Checked="false"/>
  <MenuItem Id="download" Label="检测更新" Enabled="true" Checked="false"/>
  <MenuItem Id="version" Label="版本号：V${VERSION}${TIMETAMP_STR}" Enabled="false" Checked="false"/>
  <MenuItem Id="logger" Label="上报日志" Enabled="true" Checked="false"/>
  </Menu>`

csInterface.setPanelFlyoutMenu(menuFlyoutXML)

csInterface.addEventListener(
  'com.adobe.csxs.events.flyoutMenuClicked',
  function (event) {
    if (event.data.menuId === 'logger') {
      window.$logMsg.info('[全局菜单-上报日志]', 'System Menu')
      window.$uploadLogs()
    }
    if (event.data.menuId === 'refresh') {
      window.$logMsg.info('[全局菜单-刷新页面]', 'System Menu')
      location.reload()
    }
    if (event.data.menuId === 'clear') {
      window.$logMsg.info('[全局菜单-清理文件缓存]', 'System Menu')

      csInterface.evalScript('clearDocMetaData()', function () {
        csInterface.evalScript('psAlert(\'完成缓存清理\')')
      })
    }
    if (event.data.menuId === 'clearFile') {
      window.$logMsg.info('[全局菜单-清理插件缓存]', 'System Menu')

      const srcPath = `${csInterface.getSystemPath(SystemPath.USER_DATA)}/${MATERIAL_SOURCE_PATHNAME}`
      deleteFolder(srcPath).then(d => {
        app.$CustomToast({
          type: 'success',
          duration: 2,
          content: d.message
        })
      }).catch(e => {
        app.$CustomToast({
          type: 'error',
          duration: 2,
          content: '操作失败'
        })
      })
    }
    if (event.data.menuId === 'logout') {
      window.$logMsg.info('[全局菜单-退出登录]', 'System Menu')

      logout().then(() => {
        myStorage.remove('token')
        myStorage.remove('USERINFO')

        app.$router.push({
          name: 'index'
        })
      })
    }
    if (event.data.menuId === 'download') {
      window.$logMsg.info('[全局菜单-检测更新] 正在检查新版本...', 'System Menu')
      app.$CustomDialog({
        title: '版本通知',
        content: '正在检查新版本...',
        btnText: '取消',
        onBtnClick: () => {
          app.$CustomDialogRemove()
          window.$logMsg.info('[全局菜单-检测更新] 取消检查新版本', 'System Menu')
        }
      })
      window.$logMsg.info('[全局菜单-检测更新] 开始获取最新版本号', 'System Menu')

      getCommonVersion().then(d => {
        window.$logMsg.info('[全局菜单-检测更新] 获取最新版本号成功', 'System Menu')

        app.$CustomDialogRemove()
        const { version } = d
        if (version > VERSION) {
          app.$CustomDialog({
            title: '版本通知',
            type: 'versionInfo',
            versionData: d
          })
          window.$logMsg.info(`[全局菜单-检测更新] 版本不是最新，最新版本${version}`, 'System Menu')
        } else {
          app.$CustomDialog({
            title: '版本通知',
            content: `当前已是最新版本 V${version}`,
            btnText: '知道了',
            onBtnClick: () => {
              app.$CustomDialogRemove()
            }
          })
          window.$logMsg.info(`[全局菜单-检测更新] 当前已是最新版本 V${version}`, 'System Menu')
        }
      }).catch(e => {
        window.$logMsg.error('[全局菜单-检测更新] 获取最新版本号失败', 'System Menu')
      })
    }
  }
)
