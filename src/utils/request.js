import { HOST, VERSION } from '@/config'
import app from '@/main.js'
import { myStorage } from '@/utils/index.js'
import axios from 'axios'

const WHITE_LIST = ['/action']
// eslint-disable-next-line no-undef
const cs = new CSInterface()
const osInfo = cs.getOSInformation()
const osSystem = osInfo.indexOf('Mac OS X') > -1 ? 'Mac' : osInfo.indexOf('Windows') > -1 ? 'Windows' : ''
const psVersion = cs.hostEnvironment.appVersion

const pendingRequests = new Map()

const instance = axios.create({
  baseURL: `${HOST}/api/plugin`,
  timeout: 30000
})
const ERROR_MESSAGE = {
  400: '无效的请求！',
  404: '请求路径不存在！',
  500: '服务端异常，请稍候重试！',
  502: '网关配置出错！',
  default: '网络错误!'
}

instance.interceptors.request.use(function (config) {
  const CancelToken = axios.CancelToken
  const source = CancelToken.source()

  // 把请求的CancelToken存储到pendingRequests中
  config.cancelToken = source.token
  // 使用url作为键存储请求，你也可以设置自己的键值规则，如config.url+config.method
  if (config.url.includes('/download')) {
    pendingRequests.set(config.url, source)
  }

  window.$logMsg.info(`开始请求[${config.method}]${config.url}`, 'Java Request')

  config.headers['X-Auth-Token'] = myStorage.get('token')
  config.headers['X-System'] = osSystem
  config.headers['X-PS-Version'] = psVersion
  config.headers['X-Version'] = VERSION
  return config
})

instance.interceptors.response.use(function (response) {
  const reqUrl = response.config.url
  const noErrorRes = WHITE_LIST.includes(reqUrl)
  const data = response.data
  if (data.code === 200) {
    window.$logMsg.info(`请求${reqUrl}成功 ${data.code} ${data.essage || ''}`, 'Java Request')
    return data.result
  } else if (data.code === 401) {
    !noErrorRes && app.$CustomToast({
      type: 'error',
      duration: 2,
      content: data.message
    })
    app.$router.replace({ name: 'index' })
    window.$logMsg.error(`请求${reqUrl}调用失败 ${data.code} ${data.essage || '没有错误信息'}`, 'Java Request')

    return Promise.reject(data)
  } else {
    const { message, code } = data
    !noErrorRes && app.$CustomToast({
      type: 'error',
      duration: 2,
      content: message
    })
    const error = new Error(message)
    error.responseData = { message, code, originData: data }
    window.$logMsg.error(`请求${reqUrl}调用失败 ${code} ${message}`, 'Java Request')

    return Promise.reject(error)
  }
  // return data
}, function (error) {
  if (axios.isCancel(error)) {
    const request = error.message
    pendingRequests.delete(request)
  }

  if (error) {
    const { status } = error
    const message = Reflect.has(ERROR_MESSAGE, status) ? ERROR_MESSAGE[status] : ERROR_MESSAGE.default
    error.responseData = { message, code: status, originData: error.responseData }
  }
  app.$CustomToast({
    type: 'error',
    duration: 2,
    content: error.responseData.message
  })
  window.$logMsg.error(`请求调用失败 ${error.responseData.code} ${error.responseData.message}`, 'Java Request')
  return Promise.reject(error)
})

export function cancelRequest(url) {
  // 根据url取消对应请求
  if (pendingRequests.has(url)) {
    const cancelTokenSource = pendingRequests.get(url)
    cancelTokenSource.cancel(url)
  }
}

export default instance
