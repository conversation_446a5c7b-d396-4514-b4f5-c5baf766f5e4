const dayjs = require('dayjs')
const relativeTime = require('dayjs/plugin/relativeTime')
require('dayjs/locale/zh-cn')
dayjs.extend(relativeTime)
dayjs.locale('zh-cn')
/**
 * 格式化文件大小的JS方法 - 文件的大小,传入的是一个bytes为单位的参数
 * @param {Number} filesize
 * @returns
 */
export const renderSize = filesize => {
  const value = filesize
  if (value === null || value === '' || value === 0) {
    return '0 Bytes'
  }
  // eslint-disable-next-line no-array-constructor
  var unitArr = ['B',
    'KB',
    'MB',
    'GB',
    'TB',
    'PB',
    'EB',
    'ZB',
    'YB']

  var index = 0
  var srcsize = parseFloat(value)
  index = Math.floor(Math.log(srcsize) / Math.log(1024))
  var size = srcsize / Math.pow(1024, index)
  size = size.toFixed(2) // 保留的小数位数
  return size + unitArr[index]
}

export const fromNow = time => {
  const initTime = parseInt(time)
  const dayDiffStr = dayjs(initTime).fromNow()
  return dayDiffStr.split('').filter(i => i !== ' ').join('')
}

export const formatDate = (date, l = 'YYYY-MM-DD') => {
  return dayjs(date).format(l)
}

/**
 * 判断数组是否相等的方法-忽略顺序
 * @param {Array} arr1 数组1
 * @param {Array} arr2 数组2
 * @returns
 */
export const judgeResultFun = (arr1, arr2) => {
  let flag = true
  if (arr1.length !== arr2.length) {
    flag = false
  } else {
    arr1.forEach(item => {
      if (arr2.indexOf(item) === -1) {
        flag = false
      }
    })
  }
  return flag
}

export const myStorage = {
  /**
   * set方法
   * @param {*} key key名称
   * @param {*} value value值
   */
  set(key, value) {
    localStorage.setItem(key, JSON.stringify(value))
  },
  /**
   * get方法
   * @param {Sting} key key名称
   * @returns value值
   */
  get(key) {
    try {
      const value = localStorage.getItem(key)
      if (value === null || value === undefined || value === '') {
        return null
      }
      return JSON.parse(localStorage.getItem(key))
    } catch (err) {
      return null
    }
  },
  /**
   * 清除
   * @param {String} key key值
   */
  remove(key) {
    localStorage.removeItem(key)
  }
}
/**
 * 获取url对应参数
 * @param {String} variable key名称
 * @param {String} url url地址
 * @returns value值
 */
export const getQueryVariable = (variable, url) => {
  const query = url.split('jwt?')[1]
  const vars = query.split('&')
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=')
    if (pair[0] === variable) {
      return pair[1]
    }
  }
  return false
}

export const _throttle = (fn, delay) => {
  let timer = null
  return function () {
    const context = this
    const args = arguments
    if (!timer) {
      timer = setTimeout(() => {
        fn.apply(context, args)
        timer = null
      }, delay)
    }
  }
}

export const _debounce = (fn, delay) => {
  let timer = null
  return function () {
    const context = this
    const args = arguments
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(context, args)
    }, delay)
  }
}

// 判断参数是否是其中之一
export function oneOf(value, validList) {
  for (let i = 0; i < validList.length; i++) {
    if (value === validList[i]) {
      return true
    }
  }
  return false
}

// 获取文件信息
export const getFileInfoByUrl = url => {
  return new Promise((resolve, reject) => {
    const image = new Image()
    image.onload = () => {
      resolve({
        Width: image.width,
        Height: image.height
      })
    }
    image.onerror = (e) => {
      reject(e)
    }
    image.src = url
  })
}
