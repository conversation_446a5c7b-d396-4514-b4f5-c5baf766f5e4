import random from 'string-random'
import md5 from 'blueimp-md5'
import JSEncrypt from 'jsencrypt'
import { PUBKEY } from '@/config'
// 域帐户加密的几个方法
const getMd5Str = (loginWay, password, account) => {
  const toMd5 = loginWay === 1 ? password : md5(password)
  const time = parseInt(`${Date.now() / (1000 * 60)}`)
  const rStr = random(8)
  const userName = account
  const code = ''
  return `${userName},${rStr}${toMd5},${code},${time}`
}
export default (account, password) => {
  // 枚举值，登录方式：0：邮箱&密码；1：手机号&密码
  const data = getMd5Str(0, password, account)
  const encrypt = new JSEncrypt()
  encrypt.setPublicKey(PUBKEY)
  return encrypt.encrypt(data)
}
