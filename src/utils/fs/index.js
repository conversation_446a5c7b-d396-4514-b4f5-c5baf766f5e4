/* eslint-disable no-undef */
import { MATERIAL_SOURCE_PATHNAME, HOST } from '@/config/index.js'

const path = require('path')
const cs = new CSInterface()
const request = custom_node.request
const fs = custom_node.fs
const psd = custom_node.psd

export const getPSDThumbnail = (filePath) => {
  if (!filePath) {
    return Promise.reject(new Error('请打开一个文件!'))
  }
  const pngPath = filePath.replace('.psd', '.png')
  let size = 0
  return new Promise((resolve, reject) => {
    psd.open(filePath).then((psd) => {
      size = psd.file.data.length
      return psd.image.saveAsPng(pngPath)
    }).then(() => {
      resolve([path.dirname(pngPath), path.basename(pngPath), size])
    }).catch(() => {
      reject(new Error('解析文件错误!'))
    })
  })
}

export const isLocalStorageFile = (path) => {

}

const startDownloadTask = (url, filePath, fileName, fileSize = null, processCb = null, judgeSize = true) => {
  let processNum = 0
  window.$logMsg.info(`[startDownloadTask] 开始下载：${url}`, 'FS')

  return new Promise((resolve, reject) => {
    const process = fs.createWriteStream(`${filePath}/${fileName}`)

    request.get(url, { timeout: 1000 * 60 * 30 })
      .on('error', (e) => {
        window.$logMsg.error(`[startDownloadTask] 下载出错：${e.message}`, 'FS')
        reject(e)
      })
      .on('data', data => {
        processNum += data.length
        const percentage = processNum && fileSize ? ((processNum / fileSize).toFixed(2) * 100) : 0
        processCb && processCb(parseInt(String(percentage)))
        window.$logMsg.info(`[startDownloadTask] 下载进度：${percentage}%`, 'FS')
      })
      .pipe(process)

    process.on('finish', () => {
      window.$logMsg.info(`[startDownloadTask] 下载完成：${filePath}/${fileName}`, 'FS')
      fileIsExist(filePath, fileName)
        .then(d => {
          if (!judgeSize) {
            window.$logMsg.info(`[startDownloadTask] 无需判断文件大小：${fileName}`, 'FS')
            resolve({ success: true })
            return
          }
          if (d.size === fileSize) {
            window.$logMsg.info(`[startDownloadTask] 文件大小正确：${fileName}`, 'FS')
            resolve({ success: true })
          } else {
            window.$logMsg.error(`[startDownloadTask] 文件大小不匹配，删除文件：${fileName}`, 'FS')
            deleteFile(filePath, fileName)
            reject(new Error('下载失败'))
          }
        })
        .catch(e => {
          window.$logMsg.error(`[startDownloadTask] 文件检查出错：${e.message}`, 'FS')
          reject(e)
        })
        .finally(() => {
          processNum = 0
        })
    })

    process.on('error', err => {
      window.$logMsg.error(`[startDownloadTask] 文件写入出错：${err.message}`, 'FS')
      processNum = 0
      reject(err)
    })
  })
}

/**
 * 下载文件
 * @param {String} url 下载地址
 * @param {Object} item 文件详情
 * @param {processCb} item 进度条
 * @param {saveFilePath} item 用户选择的路径
 * @param processCb
 * @param saveFileInfo
 * @param judgeSize
 */
export const downloadFile = (url, item, processCb, saveFileInfo, judgeSize = true) => {
  window.$logMsg.info('[downloadFile] 开始下载文件', 'FS')
  // 测试用的数据
  const filePath = (saveFileInfo && saveFileInfo.filePath) || path.join(
    cs.getSystemPath(SystemPath.USER_DATA),
    MATERIAL_SOURCE_PATHNAME
  )

  const fileName = (saveFileInfo && saveFileInfo.fileName) || `${item.tagSearch ? item.tagSearch.split(' ').join('·') : ''}${item.id}${item.name || ''}.${item.ext}`

  return new Promise((resolve, reject) => {
    pathIsExist(filePath)
      .then(d => {
        window.$logMsg.info('[downloadFile] 文件存在，开始下载', 'FS')

        startDownloadTask(url, filePath, fileName, item.size, processCb, judgeSize)
          .then(d => {
            // console.log(1, d, item)
            window.$logMsg.info('[downloadFile] 下载文件成功', 'FS')

            resolve(d)
          })
          .catch(e => {
            window.$logMsg.error('[downloadFile] 下载文件失败：' + e.message, 'FS')
            reject(e)
          })
      })
      .catch(e => {
        window.$logMsg.error('[downloadFile] 下载文件失败：' + e.message, 'FS')
        reject(e)
      })
  })
}

/**
 * 删除文件
 * @param {String} filePath 文件路径
 * @param {String} fileName 文件名称
 * @returns
 */
export const deleteFile = (filePath = path.join(
  cs.getSystemPath(SystemPath.USER_DATA),
  MATERIAL_SOURCE_PATHNAME
), fileName) => {
  return new Promise((resolve, reject) => {
    window.$logMsg.info('[deleteFile] 开始删除文件', 'FS')

    fs.unlink(`${filePath}/${fileName}`, err => {
      if (err) {
        window.$logMsg.error('[deleteFile] 删除文件失败：' + err.message, 'FS')
        reject(err)
      } else {
        window.$logMsg.info('[deleteFile] 删除文件成功', 'FS')
        resolve({
          success: true,
          message: '删除完成'
        })
      }
    })
  })
}

/**
 * 判断文件是否存在
 * @param {Stting} filePath 文件路径
 * @param {String} fileName 文件名
 * @returns
 */
export const fileIsExist = (filePath, fileName) => {
  return new Promise((resolve, reject) => {
    fs.stat(`${filePath}/${fileName}`, (err, status) => {
      if (err) {
        reject(err)
      } else {
        resolve(status)
      }
    })
  })
}

/**
 * 判断当前文件夹是否存在-不存在的话直接创建
 * @param {*} path
 * @returns
 */
export const pathIsExist = path => {
  return new Promise((resolve, reject) => {
    try {
      fs.readdir(path, (err, files) => {
        if (err) {
          fs.mkdirSync(path)
          resolve({
            success: true,
            message: '当前目录已创建'
          })
        } else {
          resolve({
            success: true,
            message: '当前目录存在'
          })
        }
      })
    } catch (e) {
      reject(e)
    }
  })
}

/**
 * 删除文件夹
 * @param {String} path 资源文件路径
 * @returns Promise
 */
export const deleteFolder = (filePath) => {
  return new Promise((resolve, reject) => {
    try {
      let files = []
      if (fs.existsSync(filePath)) {
        files = fs.readdirSync(filePath)
        files.forEach((file, index) => {
          const curPath = `${filePath}/${file}`
          if (fs.statSync(curPath).isDirectory()) {
            deleteFolder(curPath)
          } else {
            fs.unlinkSync(curPath)
          }
        })
        fs.rmdirSync(filePath)
      }
      resolve({
        success: true,
        message: '数据资源已全部清空'
      })
    } catch (e) {
      reject(e)
    }
  })
}

/**
 * 文件上传
 * @param {String} filePath 文件路径
 * @param {string} fileName 文件名
 */
export const uploadFile = (filePath, fileName, url) => {
  window.$logMsg.info('[uploadFile] 开始上传文件', 'FS')

  return new Promise((resolve, reject) => {
    fileIsExist(filePath, fileName).then((d) => {
      window.$logMsg.info('[uploadFile] 文件判断存在，开始请求【request请求】' + url, 'FS')

      const formData = {
        file: fs.createReadStream(`${filePath}/${fileName}`)
      }
      request.post({
        timeout: 1000 * 60 * 5,
        url,
        formData
      }, function (error, response, body) {
        window.$logMsg.info(`[uploadFile] ${error} ${response.statusCode}`, 'FS')

        if (response && response.statusCode === 200) {
          window.$logMsg.info('[uploadFile] 文件请求返回成功，开始删除本地文件', 'FS')

          fs.unlink(`${filePath}/${fileName}`, err => {
            if (err) {
              window.$logMsg.error('[uploadFile] 文件删除失败', 'FS')
              reject(new Error('文件删除失败'))
            } else {
              try {
                const bodyData = JSON.parse(body)
                window.$logMsg.info('[uploadFile] 文件删除成功，上传程序执行完成', 'FS')
                resolve(bodyData)
              } catch (error) {
                window.$logMsg.error('[uploadFile] 文件删除失败:' + error.message, 'FS')
                reject(new Error(error.message))
              }
            }
          })
        } else {
          window.$logMsg.error('[uploadFile] 文件请求返回失败，开始删除本地文件', 'FS')

          fs.unlink(`${filePath}/${fileName}`, err => {
            if (err) {
              window.$logMsg.error('[uploadFile] 文件删除失败', 'FS')
              reject(new Error('文件删除失败'))
            } else {
              window.$logMsg.error('[uploadFile] 文件解析失败,请重新上传', 'FS')
              reject(new Error('文件解析失败,请重新上传'))
            }
          })
        }
      })
    }).catch(e => {
      window.$logMsg.error('[uploadFile] 上传文件失败:文件不存在', 'FS')
      reject(new Error('文件不存在'))
    })
  })
}

/**
 * 上传smartobject保存后的png和psd
 * @param {String} fileName 文件名称（不带后缀）
 * @returns 上传后返回的url地址
 */
export const uploadSmartObjectPngAndPsd = (fileName) => {
  const filePath = `${cs.getSystemPath(SystemPath.USER_DATA)}/${MATERIAL_SOURCE_PATHNAME}`
  return new Promise((resolve, reject) => {
    const png = uploadFile(filePath, `${fileName}.png`, `${HOST}/api/plugin/storage/uploadFile`)
    const psd = uploadFile(filePath, `${fileName}.psd`, `${HOST}/api/plugin/storage/uploadFile`)
    Promise.all([png, psd]).then(d => {
      resolve(d)
    }).catch(e => {
      reject(e)
    })
  })
}
/**
 * 上传psd
 * @param {String} fileName 文件名称（不带后缀）
 * @returns 上传后返回的数据
 */
export const uploadSmartObjectPsd = (fileName) => {
  window.$logMsg.info('[uploadSmartObjectPsd] 开始上传psd', 'FS')

  const filePath = `${cs.getSystemPath(SystemPath.USER_DATA)}/${MATERIAL_SOURCE_PATHNAME}`
  return new Promise((resolve, reject) => {
    uploadFile(filePath, fileName, `${HOST}/tool_api/convert/ps`).then(d => {
      window.$logMsg.info('[uploadSmartObjectPsd] 上传psd成功', 'FS')
      resolve(d)
    }).catch(e => {
      window.$logMsg.error('[uploadSmartObjectPsd] 上传psd失败：' + e.message, 'FS')
      reject(e)
    })
  })
}

export const uploadThumbnail = (path, name) => {
  return new Promise((resolve, reject) => {
    uploadFile(path, name, `${HOST}/api/plugin/storage/uploadFile`).then(res => {
      if (res.code !== 200) {
        reject(new Error(res.message))
      }

      resolve(res)
    })
  })
}

// 保存文件到本地
export const saveFileLocal = (fileName, fileType) => {
  // 弹出文件保存对话框（ 标题；默认保存路径；文件类型；默认文件名）
  const { data: chooseFilePath } = window.cep.fs.showSaveDialogEx('保存为', '', fileType, fileName)

  if (chooseFilePath) {
    const match = chooseFilePath.match(/^(.*[\\/])([^\\/]+?)[\\/]?$/)
    if (match) {
      const filePath = match[1]
      const fileName = match[2]
      console.log('Directory:', filePath.slice(0, -1))
      console.log('Filename:', fileName)

      return {
        fileName,
        filePath: filePath.slice(0, -1)
      }
    }
  }

  return ''
}
