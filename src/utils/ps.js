/* eslint-disable space-before-function-paren */
import path from 'path'

export async function getCurrentCloudPanPSD(instance) {
  let filename, filepath

  // 检查是否有活动文档
  if (await instance.$activeDocument() === undefined) {
    return Promise.reject(new Error('请打开文件再进行保存'))
  }

  // 先尝试保存文档，确保文档是最新状态
  window.$logMsg.info('开始保存当前文档', 'ps.js')
  try {
    await instance.$forceSaveDocument()
    window.$logMsg.info('文档保存成功', 'ps.js')
  } catch (saveError) {
    window.$logMsg.error('文档保存失败: ' + saveError.message, 'ps.js')
    return Promise.reject(new Error('文档保存失败，请手动保存文档后重试'))
  }

  // 保存成功后获取文档路径
  try {
    const result = await instance.$getActivePath()
    filepath = result[0]
    filename = result[1]
  } catch (e) {
    window.$logMsg.error('获取文档路径失败: ' + e.message, 'ps.js')
    return Promise.reject(new Error('获取文档路径失败'))
  }

  const ext = (filename||'').split('.')[1] // 文件后缀名

  if (filepath.includes('cloudpan/sync_path')) {
    // const group = filepath.split('/').reverse()[1]
    const group = instance.$route.params.gid
    let _filename = filename.split('.')[0].split('-')
    _filename = _filename.slice(0, _filename.length - 2).join('-')
    window.$logMsg.info(`[getCurrentCloudPanPSD] 获取文件路径成功,filepath:${filepath};filename:${_filename}`, 'ps.js')

    return {
      filename: _filename,
      group,
      sourcePath: filepath,
      ext
    }
  }

  return {
    filename: filename.split('.')[0],
    sourcePath: filepath,
    ext
  }
}

// eslint-disable-next-line camelcase
export function genUploadFileBody(sid, gid, filename, filePath, url, is_saving) {
  return {
    gid,
    files: [
      {
        sid: sid,
        path: filePath,
        file_name: `${filename}.${/(?:\.([^.]+))?$/.exec(path.basename(filePath))[1]}`,
        preview_url: url,
        is_saving
      }
    ]
  }
}
