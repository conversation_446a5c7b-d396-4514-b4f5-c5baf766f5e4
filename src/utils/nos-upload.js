import { getUploadToken } from '@/api/index.js'
import Uploader from '@/assets/js/up-js-sdk/nos-js-sdk'

export default (formData, onProgressCb = null, option = {
  trunkSize: 4 * 1024 * 1024
}) => {
  const fileName = formData.name
  return new Promise((resolve, reject) => {
    getUploadToken({
      fileName
    }).then(d => {
      const params = {
        bucketName: d.bucket,
        objectName: d.key,
        token: d.token,
        trunkSize: option.trunkSize
      }
      const uploader = new Uploader({
        onProgress: ({ progress }) => {
          const val = +parseFloat(progress).toFixed(0)
          onProgressCb && onProgressCb(val)
        },
        status: (status) => {
          console.log(status)
        }
      })
      uploader.addFile(formData)
      uploader.upload(
        params,
        data => {
          resolve({
            url: d.url,
            ...data
          })
        },
        (e) => {
          reject(e)
        }
      )
    }).catch(e => {
      reject(e)
    })
  })
}
