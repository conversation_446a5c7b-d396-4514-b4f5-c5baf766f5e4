import pkg from './../../package.json'
import { formatDate } from '@/utils'

const {
    VUE_APP_HOST,
    VUE_APP_SOURCE_PATHNAME,
    VUE_APP_ENV,
    VUE_APP_PUBLIC_KEY,
    VUE_APP_TIMETAMP,
    VUE_APP_MUSE_CLIENT_ID,
    VUE_APP_MUSE_CLIENT_SECRET,
    VUE_APP_MUSE_IV_KEY,
    VUE_APP_MUSE_THIRD_SYSTEM,
    VUE_APP_MUSE_BASE_URL,
    VUE_APP_MUSE_AUTH_URL
} = process.env

export const VERSION = pkg.version
export const HOST = VUE_APP_HOST
export const SOURCE_PATHNAME = VUE_APP_SOURCE_PATHNAME
export const MATERIAL_SOURCE_PATHNAME = `${VUE_APP_SOURCE_PATHNAME}/material`
export const CLOUD_SOURCE_PATHNAME = `${VUE_APP_SOURCE_PATHNAME}/cloudpan`
export const LOG_SOURCE_PATHNAME = `${VUE_APP_SOURCE_PATHNAME}/Logs`
export const APP_ENV = VUE_APP_ENV
export const PUBKEY = VUE_APP_PUBLIC_KEY
export const TIMETAMP = VUE_APP_TIMETAMP || formatDate(new Date(), 'YY.MM.DD.HH')
export const MUSE_CLIENT_ID = VUE_APP_MUSE_CLIENT_ID
export const MUSE_CLIENT_SECRET = VUE_APP_MUSE_CLIENT_SECRET
export const MUSE_IV_KEY = VUE_APP_MUSE_IV_KEY
export const MUSE_THIRD_SYSTEM = VUE_APP_MUSE_THIRD_SYSTEM
export const MUSE_BASE_URL = VUE_APP_MUSE_BASE_URL
export const MUSE_AUTH_URL = VUE_APP_MUSE_AUTH_URL