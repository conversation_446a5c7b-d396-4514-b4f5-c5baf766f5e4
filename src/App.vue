<template>
  <div id="app">
    <div class="body" @paste="handlePaste">
      <ScanLinkModal v-if="showScanLinkModal" v-model="showScanLinkModal" :fileId="sharedLinkFileId" />
      <keep-alive>
        <router-view v-if="$route.meta.keepAlive" :key="$route.fullPath" />
      </keep-alive>
      <router-view v-if="!$route.meta.keepAlive" :key="$route.fullPath" />
    </div>
  </div>
</template>

<script>
import { getCommonVersion } from '@/api/index.js'
import { VERSION } from '@/config'
import ScanLinkModal from '@/pages/components/scan-link-modal/index.vue'
import watermark from 'watermark-dom'

export default {
  name: 'App',
  components: {
    ScanLinkModal
  },
  data() {
    return {
      showScanLinkModal: false,
      sharedLinkFileId: undefined
    }
  },
  watch: {
    '$route'(v) {
      this.$logMsg.info(`---------切换页面${this.$route.name}---------`, 'APP')
    }
  },
  created() {
    this.$logMsg.info(`---------进入页面${this.$route.name}---------`, 'APP')
    this.handleUpdateOpen()
    const { name, email } = this.$bus.profile || { name: '', email: '' }
    const emailPrefix = email.split('@')[0]
    watermark.init({
      watermark_txt: `${name} ${emailPrefix}`,
      watermark_color: '#000000',
      watermark_fontsize: '12px',
      watermark_alpha: 0.06
    })
  },
  methods: {
    async handlePaste(e) {
      const data = e.clipboardData.getData('Text')
      if (data) {
        const params = new URLSearchParams(data.split('?')[1])
        const fileId = params.get('fileId')
        if (fileId) {
          this.sharedLinkFileId = fileId
          this.showScanLinkModal = true
        }
      }
    },
    handleUpdateOpen() {
      this.$logMsg.info('[handleUpdateOpen]进入页面，开始检测版本号', 'APP')
      const versionData = window.localStorage.getItem('versionData') ? JSON.parse(window.localStorage.getItem('versionData')) : null
      // 不更新的弹出框频率为三天一次
      if (versionData && versionData.version >= VERSION && versionData.time > new Date().getTime()) {
        this.$logMsg.info('[handleUpdateOpen]检测版本号结束，是最新版本号', 'APP')
        return false
      } else {
        this.$logMsg.info('[handleUpdateOpen]开始获取版本号', 'APP')
        getCommonVersion().then(d => {
          const { version, forceUpdate } = d
          this.$logMsg.info(`[handleUpdateOpen]获取版本号成功：${version} VS ${VERSION}`, 'APP')
          if (version > VERSION) {
            this.$logMsg.info('[handleUpdateOpen] 弹出版本通知弹框', 'APP')

            this.$CustomDialog({
              title: '版本通知',
              type: 'versionInfo',
              closabled: !forceUpdate,
              versionData: d
            })

            window.localStorage.setItem('versionData', JSON.stringify({
              version,
              time: new Date().getTime() + 3 * 60 * 60 * 1000 * 24
            }))
          } else {
            this.$logMsg.info('[handleUpdateOpen] 无需更新版本', 'APP')
          }
        }).catch((e) => {
          this.$logMsg.error('[handleUpdateOpen]获取版本号异常：' + e.message, 'APP')
        })
      }
    }
  }
  // mounted () {
  //   this.$nextTick(() => {
  //     document.body.onfocus = () => {
  //       console.log('focus')
  //     }
  //   })
  // }
}
</script>
<style>
.body {
  height: 100%;
  width: 100%;
}

/* Scrollbar to match Adobe */
::-webkit-scrollbar {
  width: 10px;
  background: #4A4A4A;
}

::-webkit-scrollbar-thumb {
  background: #696969;
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: #696969;
}
</style>
