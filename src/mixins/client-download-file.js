import { DOWN_OPERATE_ENUM } from '@/bridge/const'
import { downloadFile, postTouchFile } from '@/bridge/api'

export default {
  methods: {
    async clientDownloadMixin({
      item,
      operateCode = DOWN_OPERATE_ENUM.NEW_WINDOW_OPEN,
      callback,
      filePath
    }) {
      try {
        const { groupId, fileKey, name, url, permission, ext } = item

        this.$logMsg.info(
          `[clientDownloadMixin] ${operateCode === DOWN_OPERATE_ENUM.NEW_WINDOW_OPEN ? '在新窗口打开' : '当前窗口置入'}`,
          'mixin'
        )

        if (permission === 'READ_ONLY') {
          this.$logMsg.error(
            '[clientDownloadMixin] 无权限编辑该文件',
            'mixin'
          )

          return this.$CustomToast({
            type: 'warning',
            duration: 2,
            content: '无权限编辑该文件'
          })
        }

        const currentGid = groupId

        const data = {
          gid: currentGid,
          file_path: filePath,
          files: [
            {
              file_key: fileKey,
              file_name: ext ? `${name}.${ext}` : name,
              operate_code: operateCode, // 下载之后的操作类型
              url // nos的链接
            }
          ]
        }

        // 更新时间戳
        await postTouchFile(currentGid, fileKey)
        // 下载
        await downloadFile(currentGid, data)
        callback && callback()
      } catch (error) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: error.message
        })
        this.$logMsg.error(
          `[clientDownloadMixin]${error.message}`,
          'mixin'
        )
      }
    }
  }
}
