/* eslint-disable */

// 素材文件置入到PS画板中
import { MATERIAL_SOURCE_PATHNAME } from '@/config/index.js'
import { getResourceDownUrl } from '@/api/index.js'
import { downloadFile, deleteFile } from '@/utils/fs/index.js'

export default {
  methods: {
    handelJavaDownload(item, sourceUrl, fileName, judgeSize = true) {
      this.$logMsg.info('[handelJavaDownload] 下载源文件', 'mixin')
      if (!sourceUrl) {
        this.$bus.removeDownloadFile(item.id)

        alert('置入失败，没有找到原始文件')
        this.$logMsg.info('[handelJavaDownload] 置入失败，没有找到原始文件', 'mixin')
      }
      downloadFile(sourceUrl, item, (process) => {
        this.$bus.updateDownloadFile(item.id, process)
      }, {fileName: fileName}, judgeSize)
        .then(result => {
          if (result.success) {
            this.$logMsg.info('[handelJavaDownload] 客户端下载源文件成功，开始置入', 'mixin')
            this.handelStartPut(item, sourceUrl, true, fileName)
          }
        })
        .catch(e => {
          alert(e)
          this.$logMsg.error('[handelStartPut] 置入失败：' + e.message, 'mixin')
        })
        .finally(() => {
          this.$bus.removeDownloadFile(item.id)
        })
    },
    handelStartPut(item, sourceUrl, isAlreadyDownload, fileName, judgeSize = true) {
      let fileUrl
      if (fileName)
        fileUrl = `${this.$csInterface.getSystemPath(SystemPath.USER_DATA)}/${MATERIAL_SOURCE_PATHNAME}/${fileName}`
      else
        fileUrl = `${this.$csInterface.getSystemPath(SystemPath.USER_DATA)}/${MATERIAL_SOURCE_PATHNAME}/${item.tagSearch ? item.tagSearch.split(' ').join('·') : ''}${item.id}${item.name || ''}.${item.ext}`

      this.$logMsg.info(`[handelStartPut] fileName: ${fileName}, fileUrl: ${fileUrl}`, 'mixin')

      this.$bus.updateDownloadFile(item.id, 0)
      this.$csInterface.evalScript(`openDocument('${fileUrl}')`, result => {
        // 文件不存在的时候
        if (result === 'no-exist') {
          this.$logMsg.info('[handelStartPut] 文件不存在，开始下载源文件', 'mixin')
          if (!isAlreadyDownload) { // 防止死循环一直在下载
            this.handelJavaDownload(item, sourceUrl, fileName, judgeSize)
          } else {
            alert('置入失败，下载文件失败')
            this.$logMsg.error('[handelStartPut] 置入失败，下载文件失败', 'mixin')
          }
          // 文件破损的时候
        } else if (result === 'error') {
          const delFileName = fileName || `${item.tagSearch ? item.tagSearch.split(' ').join('·') : ''}${item.id}${item.name || ''}.${item.ext}`
          this.$logMsg.info(`[handelStartPut] delFileName: ${delFileName}`, 'mixin')
          deleteFile(undefined, delFileName)
            .finally(() => {
              this.$bus.removeDownloadFile(item.id)
              alert('置入失败，请重新点击置入')
              this.$logMsg.error('[handelStartPut] 置入失败，请重新点击置入', 'mixin')
            })
          // 没有文档的时候
        } else if (result === 'EvalScript error.') {
          this.$bus.removeDownloadFile(item.id)
          alert('置入失败，请检查是否已建立画布')
          this.$logMsg.error('[handelStartPut] 置入失败，请检查是否已建立画布', 'mixin')
        } else {
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content: '下载置入成功'
          })
          this.$logMsg.info('[handelStartPut] 置入成功', 'mixin')

          this.$bus.removeDownloadFile(item.id)
        }
      })
    },
    async handelFilePut(item, fileName) {
      let resourceDownUrl = ''
      this.$logMsg.info('[handelFilePut] 点击下载，开始置入', 'mixin')
      try {
        resourceDownUrl = await getResourceDownUrl(item.id)
        this.$logMsg.info('[handelFilePut] 获取文件原始链接成功', 'mixin')
      } catch (error) {
      }
      this.handelStartPut(item, resourceDownUrl, false, fileName)
    }
  }
}
