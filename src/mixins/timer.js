import * as uuid from 'uuid'

export default {
  data () {
    return {
      timers: {},
    }
  },
  methods: {
    clearAllTimers () {
      this.$logMsg.info('clearAllTimers','timer mixin')
      
      Object.values(this.timers).forEach(timer => {
        if (timer) {
          clearInterval(timer)
        }
      })
    },
    clearTimer (key) {
      clearInterval(this.timers[key])
    },
    createTimer (func, time = 1000) {
      const key = uuid.v4()
      const timer = setInterval(() => {
        func && func(key)
      }, time)
      this.timers[key] = timer
    }
  },
  beforeDestroy () {
    this.clearAllTimers()
  }
}