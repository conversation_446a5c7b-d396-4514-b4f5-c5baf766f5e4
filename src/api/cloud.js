import request from '@/utils/request.js'

// 小组详情
export const getGroupDetail = (id) => {
  return request.get(`/drive/group/detail/${id}`)
}
// 获取组内文件列表
export const getDriveFileList = (params) => {
  return request.get('/drive/file/list', { params })
}
// 获取作品详情方法
export const getDriveFileDetail = (id) => {
  return request.get(`/drive/file/detail/${id}`)
}
// 获取作品详情方法2
export const getDriveFileDetailByName = (params) => {
  return request.get('/drive/file/findByName', { params })
}
// 修改文件名称
export const updateFileName = (data) => {
  return request.put('/drive/file/update', data)
}
// 历史版本文件列表
export const getDriveFileHistroyList = (params) => {
  return request.get('/drive/file/history', { params })
}
// 历史版本恢复
export const putDriveFileRecover = (data) => {
  return request.put('/drive/file/recover', data)
}
// 文件操作-删除文件
export const delDriveFile = (id) => {
  return request.delete(`/drive/file/delete/${id}`)
}
// 重命名检测
export const checkFileNames = (data) => {
  return request.post('/drive/file/checkNames', data)
}

// ========================================协作者相关=========================================================
// 协作者列表
export const getCollaborationList = (params) => {
  return request.get('/drive/file/collaborator', { params })
}
// 搜索协作者
export const getSearchedCollaborationList = (params) => {
  return request.get('/drive/file/searchUser', { params })
}
// 协作者-添加协作者
export const addCollaboration = (data) => {
  return request.post('/drive/file/addCollaborator', data)
}
// 协作者-编辑协作权限
export const updateCollaboration = (data) => {
  return request.put('/drive/file/updateCollaborator', data)
}
// 协作者-移出小组
export const delCollaboration = (id) => {
  return request.delete(`/drive/file/deleteCollaborator/${id}`)
}
// 文件分享-获取分享信息及初始化
export const getShareInfo = (params) => {
  return request.get('/drive/file/getShareInfo', { params })
}
// 文件分享-更新分享信息
export const updateShareInfo = (data) => {
  return request.post('/drive/file/updateShareInfo', data)
}

// ========================================文件上传相关=========================================================
// 上传保存-文件上传初始化
export const getUploadInit = (data) => {
  return request.post('/drive/file/uploadInit', data)
}

// others
// 星标素材
export const starResource = (data) => {
  return request.post('/resource/star', data)
}
