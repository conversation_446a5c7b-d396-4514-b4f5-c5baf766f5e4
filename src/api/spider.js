import request from '@/utils/request.js'

// 下拉列表
export const getDropList = (id) => {
  // return request.get('/api/plugin/gameui/dropList')
  return request.get('/gameui/dropList')
}

// 游戏搜索
export const searchGameList = (data) => {
  return request.post('/gameui/search', data)
}

// 游戏截图搜索
export const searchGameImageList = (data) => {
  return request.post('/gameui/imageList', data)
}

/**
 1、下拉列表：
 GET /api/plugin/gameui/dropList

 2、游戏搜索：
 POST /api/plugin/gameui/search
 请求参数和素材搜索一样；

 响应参数：
 id
 title：标题
 projectId：项目ID
 originCreateTime 创建时间
 coverUrl：封面图片URL
 device：平台
 categoryName：游戏类型
 style：游戏风格
 era： 上线年份
 tags：标签列表
 likeCount：喜欢次数
 viewCount：查看次数
 imageCount：截图数量

 3、游戏截图搜索
 POST /api/plugin/gameui/imageList
 传参：
 gameId 游戏的id

 响应参数：
 同素材库返回字段



 */
