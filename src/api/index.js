import request from '@/utils/request.js'
import encryptData from '@/utils/encryptData'

// 登录获取token
export const login = ({ account, password }) => {
  const data = {
    account,
    encyData: encryptData(account, password)
  }
  return request.post('/auth/login', data)
}
// 登录轮询是否成功
export const loginResult = ({ deviceId }) => {
  return request.get(`/auth/loginV2/${deviceId}`)
}
// 获取用户信息
export const getUserInfo = () => {
  return request.get('/auth/uibt')
}
// 获取版本信息
export const getCommonVersion = () => request.get('/common/version')
// 退出登录
export const logout = () => {
  return request.get('/auth/logout')
}
// 获取素材
export const getResourceSearch = (data) => {
  return request.post('/resource/search', data)
}
// 插件-素材详情
export const getResourceDetail = (id) => {
  return request.get(`/resource/detail/${id}`)
}

// 插件-下拉框(标签组、分类)
export const getDropList = (params) => {
  return request.get('/resource/dropList', { params })
}

// 获取下拉框标签
export const getResourceTags = () => {
  return request.get('/resource/tags')
}

// 插件-层级下拉框
export const getResourceCategories = () => {
  return request.get('/resource/categories')
}

// 获取相似图表
export const getRecommendationList = (data) => {
  return request.post('/resource/recommendation', data)
}

// 搜索联想
export const getSuggestList = (params) => {
  return request({
    method: 'get',
    url: '/v2/resource/suggest',
    params
  })
  // return request.get('/resource/suggest', params)
}

// 插件-下载
export const getResourceDownUrl = (id) => {
  return request({
    method: 'get',
    url: `/resource/download/${id}`
  })
}

// 插件-下载
export const deleteResource = (id) => {
  return request({
    method: 'delete',
    url: '/resource/delete',
    data: { id }
  })
}

// 插件-修改素材工时
export const updateResourceWorkTime = async (data) => {
  return request.post('/admin/resource/batchEditWorkTime', data)
}

// 插件-修改素材标签
export const updateResourceTag = async (data) => {
  return request.post('/admin/resource/batchEdit', data)
}

// 数据埋点
export const addActionLog = (data) => {
  return request.post('/action', data)
}

/**
 * =============================================上传页面===========================================================
 */
  // 上传文件-获取token
export const getUploadToken = (params) => {
    return request.get('/storage/jssdk/token', { params })
  }
// 插件-项目列表
export const getResourceProjectList = () => {
  return request.get('/resource/projectList')
}
// 预览图-psd、psb文件转换png
export const ps2png = (data) => {
  // 在生产环境中使用完整的URL，开发环境使用代理
  const isDev = process.env.NODE_ENV === 'development'
  const baseURL ='https://gui.netease.com/tool_api'
  return request.post('/convert/ps2png', data, {
    baseURL: baseURL
  })
}
// 插件-标签列表
export const getResourceTagList = (params) => {
  return request.get('/resource/tagList', { params })
}
// 插件-上传素材
export const postResourceUpload = (data) => {
  return request.post('/v2/resource/upload', data)
}
// 插件-网盘文件转存到素材
export const postDriveFileSaveToResource = (data) => {
  return request.post('/drive/file/saveToResource', data)
}
/**
 * ============== 网盘 ==============
 */
  // 小组-获取小组
export const getGroupList = async (pageNum = 1, isStarred = null) => {
    if (isStarred !== null) {
      return request.get(`/drive/group/list?pageNum=${pageNum}&isStarred=${isStarred}`)
    } else {
      return request.get(`/drive/group/list?pageNum=${pageNum}`)
    }
  }

// 获取项目组列表
export const getProjectGroupList = async () => {
  return request.get('/drive/group/list/projects')
}

// 小组-创建小组
export const createGroup = async (data) => {
  return request.post('/drive/group/add', data)
}

// 修改小组名称
export const renameGroup = async (id, name) => {
  return request.put('/drive/group/rename', { id: id, name: name })
}

// 解散小组
export const deleteGroup = async id => {
  return request.delete('/drive/group/delete/' + id)
}

// 退出小组
export const quitGroup = async (groupId, email) => {
  return request.put('/drive/group/quit', { groupId: groupId, email: email })
}

// 转让管理员
export const transferOwner = async (groupId, userId) => {
  return request.put('/drive/group/transferOwner', { groupId: groupId, userId: userId })
}

// 获取小组成员列表
export const memberList = async (groupId, pageNum) => {
  return request.get(`/drive/group/memberList?groupId=${groupId}&pageNum=${pageNum}&pageSize=500`)
}

// 添加小组成员
export const addMember = async (groupId, email) => {
  return request.post('/drive/group/addMember', { groupId: groupId, email: email })
}

// 移除小组成员
export const removeMember = async (groupId, userId) => {
  return request.put('/drive/group/removeMember', { groupId: groupId, userId: userId })
}

// 搜索 POPO 用户
export const searchPopoUsers = async ({ groupId, keyword }) => {
  return request.get(`/drive/group/searchUser?groupId=${groupId}&keyword=${keyword}`)
}

// 设置/取消 星标
export const setStar = async (groupId, isStarred) => {
  return request.put('/drive/group/star', { groupId: groupId, isStarred: isStarred })
}

// 根据类型搜索
export const search = async (type, keyword = '', pageNum = 1, pageSize = 20) => {
  const params = {
    type,
    keyword,
    pageNum,
    pageSize
  }
  return request.get('/drive/search', { params })
}

// 搜索页用户获取更多
export const searchUserMore = async (userId, pageNum = 1) => {
  return request.get(`/drive/file/listForUserSearch?pageNum=${pageNum}&userId=${userId}`)
}

// 以图搜图
export const searchImgByImg = (params) => {
  return request.post('/resource/searchImage', params)
}
