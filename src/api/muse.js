import axios from 'axios'
import app from '@/main.js'
import {
  MUSE_AUTH_URL,
  MUSE_BASE_URL,
  MUSE_CLIENT_ID,
  MUSE_CLIENT_SECRET,
  MUSE_IV_KEY,
  MUSE_THIRD_SYSTEM
} from '@/config'
import CryptoJS from 'crypto-js'

export const MUSE_ASSET = 'original'


if (!MUSE_BASE_URL) {
  window.$logMsg.error('没找到MUSE_BASE_URL，请检查配置', 'MUSE')
  window.$CustomToast({
    type: 'error',
    duration: 2,
    content: 'MUSE_BASE_URL配置错误'
  })
}

if (!MUSE_AUTH_URL) {
  window.$logMsg.error('没找到MUSE_AUTH_URL，请检查配置', 'MUSE')
  window.$CustomToast({
    type: 'error',
    duration: 2,
    content: 'MUSE_AUTH_URL配置错误'
  })
}

if (!MUSE_CLIENT_ID) {
  window.$logMsg.error('没找到MUSE ID，请检查配置', 'MUSE')
  window.$CustomToast({
    type: 'error',
    duration: 2,
    content: 'MUSE ID配置错误'
  })
}

if (!MUSE_CLIENT_SECRET) {
  window.$logMsg.error('没找到MUSE SECRET，请检查配置', 'MUSE')
  window.$CustomToast({
    type: 'error',
    duration: 2,
    content: 'MUSE SECRET配置错误'
  })
}

if (!MUSE_IV_KEY) {
  window.$logMsg.error('没找到MUSE IV_KEY，请检查配置', 'MUSE')
  window.$CustomToast({
    type: 'error',
    duration: 2,
    content: 'MUSE IV_KEY配置错误'
  })
}

if (!MUSE_THIRD_SYSTEM) {
  window.$logMsg.error('没找到MUSE THIRD_SYSTEM，请检查配置', 'MUSE')
  window.$CustomToast({
    type: 'error',
    duration: 2,
    content: 'MUSE THIRD_SYSTEM配置错误'
  })
}

const museInstance = axios.create({
  baseURL: MUSE_BASE_URL,  // 设置默认的baseURL
  timeout: 30000
})

// 错误信息配置
const ERROR_MESSAGE = {
  400: '无效的请求！',
  404: '请求路径不存在！',
  500: '服务端异常，请稍候重试！',
  502: '网关配置出错！',
  default: '网络错误!'
}

// AES加密邮箱
function aesEncryptWithIv(iv, userKey, email) {
  const sha256Hash = CryptoJS.SHA256(userKey).toString(CryptoJS.enc.Hex)
  const key = CryptoJS.enc.Utf8.parse(sha256Hash.slice(0, 16)) // 取前8字节（16个Hex字符）
  const ivParsed = CryptoJS.enc.Utf8.parse(iv) // IV 直接按 UTF-8 处理
  const encrypted = CryptoJS.AES.encrypt(email, key, {
    iv: ivParsed,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext) // 统一 Base64 编码
}

// 请求拦截器
museInstance.interceptors.request.use(
  async function (config) {
    // 需要携带token
    if (config.needToken) {
      try {
        const token = app.$bus.museToken
        config.headers.Authorization = `Bearer ${token}`
      } catch (error) {
        window.$logMsg.error('获取token失败', 'MUSE')
        return Promise.reject(error)
      }
    }
    // 邮箱加密
    if (config.needEmail) {
      const email = aesEncryptWithIv(
        MUSE_IV_KEY,
        MUSE_THIRD_SYSTEM,
        app.$bus.profile?.email
      )
      config.headers.email = email
    }
    return config
  },
  function (error) {
    return Promise.reject(error)
  }
)

// 响应拦截器
museInstance.interceptors.response.use(
  function (response) {
    return response.data
  },
  async function (error) {
    if ([401, 403].includes(error.response?.status)) {
      try {
        app.$bus.museToken = ''
        window.localStorage.removeItem('museToken')
        await app.$bus.fetchMuseToken()
        const config = error.config
        return museInstance(config)
      } catch (e) {
        return Promise.reject(e)
      }
    }

    if (error) {
      const { status } = error
      const message = Reflect.has(ERROR_MESSAGE, status) ? ERROR_MESSAGE[status] : ERROR_MESSAGE.default
      error.responseData = { message, code: status, originData: error.responseData }
    }
    app.$CustomToast({
      type: 'error',
      duration: 2,
      content: error.responseData?.message || '请求失败'
    })
    return Promise.reject(error)
  }
)

// 获取 access_token
export function getMuseToken() {
  if (!MUSE_CLIENT_ID || !MUSE_CLIENT_SECRET) {
    return Promise.reject(new Error('未找到MUSE ID、SECRET，请检查配置'))
  }
  const token = Buffer.from(`${MUSE_CLIENT_ID}:${MUSE_CLIENT_SECRET}`).toString('base64')
  return museInstance({
    url: '/oauth2/token',
    baseURL: MUSE_AUTH_URL,
    method: 'POST',
    params: {
      grant_type: 'client_credentials',
      scope: 'all'
    },
    headers: {
      Authorization: `Basic ${token}`
    }
  })
}

// 获取仓库列表
export function getMuseRepositories(params) {
  return museInstance({
    url: '/muse-service/api/organization/organization_config',
    method: 'GET',
    params,
    needEmail: true,
    needToken: true
  })
}

// 获取目录列表
export function getFolderTree(organization) {
  return museInstance({
    url: `/muse-service/api/category/trees/${organization}/${MUSE_ASSET}`,
    method: 'GET',
    needEmail: true,
    needToken: true
  })
}

// 获取资源列表
export function getAssetList(organization, data) {
  return museInstance({
    url: `/muse-service/api/open/v1/${organization}/${MUSE_ASSET}/list`,
    method: 'POST',
    data,
    needEmail: true,
    needToken: true
  })
}

// 下载素材
export function downloadAsset(data) {
  return museInstance({
    url: `/muse-service/api/download/open/download`,
    method: 'POST',
    data,
    needEmail: true,
    needToken: true
  })
}

// 搜索 - 是否有素材
export function searchAggs(data) {
  return museInstance({
    url: `/muse-service/api/search/aggs`,
    method: 'POST',
    data,
    needEmail: true,
    needToken: true
  })
}

export function searchAssets(data) {
  return museInstance({
    url: `/muse-service/api/search/list`,
    method: 'POST',
    data,
    needEmail: true,
    needToken: true
  })
}

// 获取上传地址
export function getUploadUrl(data) {
  return museInstance({
    url: `/muse-service/api/attachment/${data.organization}/${MUSE_ASSET}/getPermissionToken`,
    method: 'POST',
    needEmail: true,
    needToken: true
  })
}

// 发布素材
export function publishAsset(data) {
  return museInstance({
    url: `/muse-service/api/open/v1/task/${data.organization}/${MUSE_ASSET}/receive`,
    method: 'POST',
    data,
    needEmail: true,
    needToken: true
  })
}

// 删除资源
export function deleteAsset(data) {
  return museInstance({
    url: `/muse-service/api/open/v1/asset/${data.organization}/${MUSE_ASSET}/${data.id}/delete`,
    method: 'DELETE',
    data,
    needEmail: true,
    needToken: true
  })
}

// 获取详情
export function getAssetDetail(data) {
  return museInstance({
    url: `/muse-service/api/open/v1/redirect/page/detail`,
    method: 'POST',
    data,
    needEmail: true,
    needToken: true
  })
}
