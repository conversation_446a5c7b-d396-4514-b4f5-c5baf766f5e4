import app from '@/main'
import { DOWN_SOURCE_TYPE_ENUM, DOWN_OPERATE_ENUM } from './const'
import { getDriveFileDetail } from '@/api/cloud.js'

// eslint-disable-next-line no-undef
const cs = new CSInterface()

// 下载最新版本文件download
const download = data => {
  if (data.command === 'finished') {
    window.$logMsg.info('[download] 文件下载完成' + JSON.stringify(data), 'bridge')

    if (data.data.path) {
      const osInfo = cs.getOSInformation()
      const filePath = osInfo.indexOf('Mac OS X') > -1 ? data.data.path : osInfo.indexOf('Windows') > -1 ? data.data.path.replace(/\\/g, '/') : ''

      window.$logMsg.info('[download] 文件下载完成：' + filePath, 'bridge')

      // 置入操作
      if (data.data.operate_code === DOWN_OPERATE_ENUM.INPUT) {
        cs.evalScript(`openDocument('${filePath}')`, result => {
          if (result === 'EvalScript error.') {
            window.$logMsg.error('[download] 当前没有打开的窗口' + filePath, 'bridge')

            app.$CustomToast({
              type: 'error',
              duration: 2,
              content: '当前没有打开的窗口！'
            })
          } else {
            app.$CustomToast({
              type: 'success',
              duration: 2,
              content: '置入成功'
            })
          }
        })
      } else { // 新窗口打开
        cs.evalScript(`app.load(new File("${filePath}"))`, res => {
          if (res !== 'EvalScript error.') {
            window.$logMsg.info('[download] 文件在PS已打开！', 'bridge')

            // app.$CustomToast({
            //   type: 'success',
            //   duration: 2,
            //   content: '文件在PS已打开！'
            // })

            // 当发现本地文件与网盘中的文件不一致时，弹框提醒用户上传
            if (data.data.event_code === DOWN_SOURCE_TYPE_ENUM.LOCAL_DIFF) {
              app.$CustomDialogRemove()
              app.$CustomDialog({
                title: '提示',
                type: 'PSOpenTip',
                width: 420,
                customParams: { name: data.data.local_name },
                onBtnClick: async () => {
                  const { sid } = data.data
                  const {
                    id,
                    groupName,
                    size,
                    name,
                    groupId,
                    isSharedWithMe
                  } = await getDriveFileDetail(sid)

                  // 文件上传至网盘
                  app.$uploadFileAction(app, {
                    id,
                    groupId,
                    groupName,
                    size,
                    fileName: name,
                    sourcePath: filePath,
                    isSharedWithMe
                  })
                  app.$CustomDialogRemove()
                }
              })
            }
          } else {
            window.$logMsg.error('[download] 请先在PS中新建一个画板' + filePath, 'bridge')

            app.$CustomToast({
              type: 'error',
              duration: 2,
              content: '请先在PS中新建一个画板！'
            })
          }
        })

      }
    }
  }
  app && app.$bus && app.$bus.downloadProcess(data)
}
// 文件上传upload
const upload = data => {
  window.$logMsg.info('[upload] 文件上传', 'bridge')

  app && app.$bus && app.$bus.uploadProcess(data)
}
// 清理插件缓存auto-dective/diskfull
const autoDective = data => {
  window.$logMsg.info('[autoDective] 清理插件缓存', 'bridge')
  data.command === 'diskfull' && console.log('diskfull')
}

export default (data) => {
  data.service === 'download' && download(data)
  data.service === 'upload' && upload(data)
  data.service === 'auto-dective' && autoDective(data)
}
