import { HOST } from '@/config/index.js'
import app from '@/main'
import { myStorage } from '@/utils/index.js'
import axios from 'axios'

const ERROR_MESSAGE = {
  400: 'WS:无效的请求！',
  404: 'WS:请求路径不存在！',
  500: 'WS:服务端异常，请稍候重试！',
  502: 'WS:网关配置出错！',
  default: 'WS:连接失败，请稍候重试!'
}

axios.interceptors.request.use(function (config) {
  const data = config.data || config.params
  if (!data.TOKEN) { // 安全考虑不打印 TOKEN
    window.$logMsg.info(`开始请求[${config.method}]${config.url},请求参数：${JSON.stringify(config.data || config.params || {})}`, 'WS Request')
  }
  return config
})

axios.interceptors.response.use(
  function (response) {
    const data = response.data
    const reqUrl = response.config.url
    console.log('response', response)

    if (data.code === 200) {
      window.$logMsg.info(`ws请求成功 ${reqUrl}`, 'WS Request')
      return response
    } else {
      const message = data.message || 'ws请求失败，请稍候重试!'
      app.$CustomToast({
        type: 'error',
        duration: 2,
        content: message
      })

      window.$logMsg.error(`[ws请求失败]${reqUrl} ${data.code} ${message}`, 'WS Request')
      return Promise.reject(response)
    }
  },
  function (error) {
    if (error) {
      const { status, responseData } = error
      console.log('response', status, responseData)

      const message = ERROR_MESSAGE[status] || ERROR_MESSAGE.default
      error.responseData = {
        message,
        code: status,
        originData: responseData
      }
      window.$logMsg.error(`[ws连接失败] ${status || ''} ${message}`, 'WS Request')
    }

    app.$CustomToast({
      type: 'error',
      duration: 2,
      content: error.responseData.message
    })

    return Promise.reject(error)
  }
)

const prefix = `http://127.0.0.1:12315`

const postRequest = (url, data, headers) => {
  return axios.post(`${prefix}${url}`, data, { headers })
}

const getRequest = (url, data, headers) => {
  return axios.get(`${prefix}${url}`, { params: data }, { headers })
}

const putRequest = (url, data, headers) => {
  return axios.put(`${prefix}${url}`, data, { headers })
}

// 鉴权接口
export const isPermission = () => {
  // 如果是外部员工，不需要拉起客户端
  if (app && app.$bus && app.$bus.profile && app.$bus.profile.type === 'EXTERNAL') {
    window.$logMsg.info('外部员工，不需要拉起客户端', 'WS Request')
    return
  }
  return postRequest('/utils/config', {
    TOKEN: myStorage.get('token'),
    SERVER_HOST: HOST,
    PSD_FILE_TYPES: ['.psd', '.psb', '.jpg', '.jpeg', '.png', '.gif', '.crw', '.tiff', '.ai', 'bmp'],
    PSD_MAX_SIZE: 20 * 2 ** 30, // 分片大小
    PSD_TIMEOUT: 600
  })
}
export const caniuse = () => {
  return axios.get(`http://127.0.0.1:${window.$ClientPort}/ping`)
}
// 更新最新时间戳 touch
export const postTouchFile = (gid, fileKey) => {
  return postRequest('/file/ps/touch', { fileKey }, { gid })
}
// =========================下载================================
// 下载最新版本文件download
export const downloadFile = (gid, data) => {
  return postRequest('/file/ps/downloads', data, { gid })
}
// 获取正在下载的所有文件列表
export const getDownloadFilesList = (gid, data) => {
  return getRequest('/file/ps/downloads', data, { gid })
}
// =========================上传================================
// 获取正在上传的所有文件列表
export const getUploadFilesList = (gid, data) => {
  return getRequest('/file/ps/uploads', data, { gid })
}
// 文件上传upload
export const uploadFile = (gid, data) => {
  return postRequest('/file/ps/uploads', data, { gid })
}
// =========================文件版本================================
// 获取本地盘的文件版本情况
export const getFilesState = (gid, data) => {
  return postRequest('/file/ps/list', data, { gid })
}
export const getFileInfo = (data) => {
  return postRequest('/file/ps/info', data)
}
export const delFile = (data) => {
  return putRequest('/file/ps/list', data)
}
