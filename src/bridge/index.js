/* eslint-disable no-undef */
import parseData from './res-parse'
import { isPermission } from './api'

const fs = custom_node.fs
const childProcess = custom_node.cp
const path = custom_node.path
const cs = new CSInterface()
const prjPath = cs.getSystemPath(SystemPath.EXTENSION)
const { CLOUD_SOURCE_PATHNAME } = require('@/config/index.js')

const dataPath = path.join(
  cs.getSystemPath(SystemPath.USER_DATA),
  CLOUD_SOURCE_PATHNAME
)

const osInfo = cs.getOSInformation()

const exePath = path.join(
  prjPath,
  osInfo.indexOf('Mac OS X') > -1
    ? 'exe/ps-uploader-mac/ps-uploader'
    : osInfo.indexOf('Windows') > -1
      ? 'exe/ps-uploader-win/ps-uploader.exe'
      : 'exe/ps-uploader-mac/ps-uploader'
)

const isWindows = osInfo.indexOf('Windows') > -1

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

const readPort = () => {
  return new Promise((resolve, reject) => {
    fs.readFile(
      path.join(dataPath, '.psLoaderPort.txt'),
      'utf-8',
      (err, data) => {
        if (err) {
          window.$logMsg.error(`[readPort]异常 ${err.message}`, 'WS')
          window.$ClientPort = '12315'
          resolve('12315')
        } else {
          window.$logMsg.info(`[readPort] 读取端口号成功`, 'WS')
          window.$ClientPort = data
          resolve(data)
        }
      }
    )
  })
}

class Bridge {
  constructor() {
    this.ls = null
    this.port = ''
    this.socket = null
    this.initFinshed = false
  }

  async initServer() {
    this.initFinshed = false
    try {
      const port = await readPort()
      window.$logMsg.info('[initWS]获取端口号成功', 'WS')
      this.port = port

      await this.initWS()
      this.initFinshed = true
      window.$logMsg.info('[initWS] 成功', 'WS')

      return Promise.resolve()
    } catch (e) {
      window.$logMsg.error(`[initWS]连接异常`, 'WS')
      return Promise.reject(new Error(e))
    }
  }

  // 进程启动
  async createProcess() {
    try {
      await this.initExec()
      await delay(1000)
      await this.initServer()
      return Promise.resolve()
    } catch (e) {
      return Promise.reject(new Error(e))
    }
  }

  initWS() {
    return new Promise((resolve, reject) => {
      this.socket = new WebSocket(`ws://localhost:${this.port}/ws`)
      this.socket.onopen = (event) => {
        console.log('客户端链接上服务器', event, new Date().getTime())
        window.$logMsg.info('1. 客户端链接上服务器', 'WS')
        this.socket.send(JSON.stringify({ 11: '22' }))
        isPermission()
        window.$logMsg.info('权限校验完成', 'WS')
        resolve('success')
      }
      this.socket.onclose = (event) => {
        window.$logMsg.error(`连接关闭`, 'WS')
        if (this.initFinshed) this.initServer()
        reject(event.code)
      }
      this.socket.onmessage = (event) => {
        window.$logMsg.info(`onmessage事件 ${event.data}`, 'WS')
        parseData(JSON.parse(event.data))
      }
    })
  }

  initExec() {
    window.$logMsg.info('启动子进程', 'WS')
    return new Promise((resolve, reject) => {
      try {
        // 设置可执行权限（macOS）
        if (!isWindows) {
          childProcess.execSync(`chmod +x "${exePath}"`)
        }

        this.ls = childProcess.spawn(exePath, ['--path', dataPath], {
          detached: true,
          stdio: 'pipe'
        })

        if (!isWindows) this.ls.unref()

        this.ls.stdout.on('data', (data) => {
          window.$logMsg.info(`child process stdout, ${data}`, 'WS')
          resolve('success')
        })

        this.ls.stderr.on('data', (data) => {
          window.$logMsg.error(`child process stderr, ${data}`, 'WS')
          reject(data)
        })

        this.ls.on('close', (code) => {
          window.$logMsg.error(`child process exited with code ${code}`, 'WS')
          reject(code)
        })
      } catch (error) {
        window.$logMsg.error(`进程启动执行异常，请确认文件权限问题，${error.message}`, 'WS')
        reject(error)
      }
    })
  }

  clear() {
    this.ls = null
    this.port = ''
    this.socket = null
    window.$logMsg.info('[clear] success', 'WS')
  }
}

export default async () => {
  window.$logMsg.info('Bridge初始化', 'WS')
  const d = new Bridge()

  try {
    await d.initServer()
    return d
  } catch (error) {
  }

  // 初始化失败，需要拉起进程
  try {
    await d.createProcess()
  } catch (error) {
  }

  return d
}
