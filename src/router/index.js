import Vue from 'vue'
import app from '@/main.js'
import VueRouter from 'vue-router'
import { myStorage } from '@/utils/index.js'
import Bridge from '@/bridge'

Vue.use(VueRouter)

const routes = [
  {
    path: '/index',
    name: 'index',
    component: () => import('@/pages/index'),
    meta: {
      keepAlive: false,
      noNeedLogin: true // 不需要登录
    }
  },
  {
    path: '/list',
    name: 'list',
    component: () => import('@/pages/list'),
    meta: {
      type: 'm',
      keepAlive: true,
      scrollTop: 0
    }
  },
  {
    path: '/detail/:id',
    name: 'detail',
    component: () => import('@/pages/detail/index.vue'),
    meta: {
      type: 'm',
      keepAlive: false
    }
  },
  {
    path: '/upload',
    name: 'upload',
    component: () => import('@/pages/upload'),
    meta: {
      type: 'm',
      keepAlive: false
    }
  },
  {
    path: '/groups',
    name: 'groups',
    component: () => import('@/pages/cloud/groups'),
    meta: {
      type: 's',
      keepAlive: false
    }
  },
  {
    path: '/project-groups',
    name: 'projectGroups',
    component: () => import('@/pages/cloud/project-groups.vue'),
    meta: {
      type: 's',
      keepAlive: false
    }
  },
  {
    path: '/group/:gid',
    name: 'group',
    component: () => import('@/pages/cloud/group'),
    meta: {
      type: 's',
      keepAlive: false
    }
  },
  {
    path: '/games',
    name: 'games',
    component: () => import('@/pages/game/games.vue'),
    meta: {
      type: 'g',
      keepAlive: true,
      scrollTop: 0
    }
  },
  {
    path: '/game/:id',
    name: 'game',
    component: () => import('@/pages/game/game.vue'),
    meta: {
      type: 'g',
      keepAlive: false,
      scrollTop: 0
    }
  },
  {
    path: '/game-search',
    name: 'gameSearch',
    component: () => import('@/pages/game/search.vue'),
    meta: {
      type: 'g',
      keepAlive: false,
      scrollTop: 0
    }
  },
  {
    path: '/muse',
    name: 'muse',
    component: () => import('@/pages/muse/index.vue'),
    meta: {
      type: 'muse',
      keepAlive: true,
      scrollTop: 0
    }
  },
  {
    path: '/muse/search',
    name: 'museSearch',
    component: () => import('@/pages/muse/search.vue'),
    meta: {
      type: 'muse',
      keepAlive: false,
      scrollTop: 0
    }
  },
  {
    path: '/search/:kw',
    name: 'search',
    component: () => import('@/pages/cloud/search.vue'),
    meta: {
      keepAlive: false
    }
  },
  {
    path: 'search-img',
    name: 'searchImg',
    component: () => import('@/pages/search-img.vue'),
    meta: {
      type: 'm',
      keepAlive: false
    }
  },
  {
    path: '*',
    redirect: {
      name: 'list'
    }
  }
]

const router = new VueRouter({
  // mode: 'history',
  // base: process.env.BASE_URL,
  routes
})

router.beforeEach(async (to, from, next) => {
  const { noNeedLogin } = to.meta
  const detailContent = app && app.$bus ? app.$bus.detail : null
  const isLogin = !!myStorage.get('USERINFO')

  // 客户端初始化解决异步时间差问题（刷新场景以及初次初始化）
  if (!window.$ClientPort) {
    const { initFinshed } = await Bridge()
    app.$bus.changeIsWSInit(initFinshed)
  }

  if (!noNeedLogin && !isLogin) { // 需登录然未登录
    next({
      name: 'index'
    })
  } else if (to.name === 'detail' && !detailContent) { // 针对详情页面刷新操作场景控制
    next({
      name: 'list'
    })
  } else {
    next()
  }
})

export default router
