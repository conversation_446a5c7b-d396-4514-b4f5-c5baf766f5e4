<template>
  <div class="footer">
    <GButton
      :disabled="isDownLoading"
      class="btn"
      type="primary"
      @click="handlePut()"
    >{{
        processNum
          ? `下载中 ${processNum}%`
          : '在当前窗口置入'
      }}
    </GButton>
  </div>
</template>

<script>
import { DOWN_OPERATE_ENUM } from '@/bridge/const'
import FilePutMixin from '@/mixins/filePut'
import { downloadAsset } from '@/api/muse'

export default {
  name: 'FilePreviewFooter',
  mixins: [FilePutMixin],
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    DOWN_OPERATE_ENUM() {
      return DOWN_OPERATE_ENUM
    },
    processNum() {
      return this.$bus.downloadFiles[this.item.id] || 0
    }
  },
  data() {
    return {
      isDownLoading: false
    }
  },
  methods: {
    async handlePut() {
      try {
        this.isDownLoading = true
        const { code, data } = await downloadAsset([
          {
            ref_id: this.item.id,
            asset_type: this.item.asset_type,
            organization: this.item.organization,
            reason: 'Learn'
          }
        ])
        if (code) {
          this.$CustomToast({
            type: 'error',
            duration: 2,
            content: `下载失败，${data}`
          })
          return
        }
        const fileName = data.downloads[0].files[0].name
        const url = data.downloads[0].files[0].url
        this.handelStartPut(this.item, url, false, fileName, false)  // mixin方法
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: `下载置入失败，${e.message}`
        })
      } finally {
        this.isDownLoading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;

  .btn {
    width: 228px;
    height: 28px;
  }
}
</style>