<!-- 多级select上传位置选择组件 -->
<template>
  <div class="upload-location-selector">
    <div class="section-label">上传位置</div>

    <!-- 选择器容器 - 纵向布局 -->
    <div class="selectors-container">
      <!-- 项目选择 -->
      <div class="selector-section project-section">
        <div class="select-item">
          <div class="select-label">
            <svg class="label-icon" width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path
                d="M2 2.5C2 2.22386 2.22386 2 2.5 2H4.08579C4.351 2 4.60536 2.10536 4.79289 2.29289L5.70711 3.20711C5.89464 3.39464 6.149 3.5 6.41421 3.5H9.5C9.77614 3.5 10 3.72386 10 4V9C10 9.27614 9.77614 9.5 9.5 9.5H2.5C2.22386 9.5 2 9.27614 2 9V2.5Z"
                fill="#4A90E2" />
            </svg>
            项目
          </div>
          <div class="select-wrapper">
            <multi-select
              v-model="selectedRepositoryId"
              :options="repositoryList"
              hasValue
              label="选择项目"
              class="custom-select"
            />
          </div>
        </div>
      </div>

      <!-- 多级文件夹选择 -->
      <div class="selector-section folder-section" v-if="levelSelects.length > 0">
        <div class="folder-selectors">
          <div
            v-for="(levelData, index) in levelSelects"
            :key="index"
            class="select-item folder-select-item"
            :class="{ 'has-next': index < levelSelects.length - 1 }"
          >
            <div class="select-label">
              <svg class="label-icon" width="12" height="12" viewBox="0 0 12 12" fill="none">
                <path
                  d="M2 2.5C2 2.22386 2.22386 2 2.5 2H4.08579C4.351 2 4.60536 2.10536 4.79289 2.29289L5.70711 3.20711C5.89464 3.39464 6.149 3.5 6.41421 3.5H9.5C9.77614 3.5 10 3.72386 10 4V9C10 9.27614 9.77614 9.5 9.5 9.5H2.5C2.22386 9.5 2 9.27614 2 9V2.5Z"
                  fill="#666" />
              </svg>
              {{ getLevelLabel(index) }}
            </div>
            <div class="select-wrapper">
              <multi-select
                v-model="levelData.selected"
                :options="levelData.options"
                hasValue
                :label="getLevelPlaceholder(index)"
                class="custom-select folder-select"
                @change="(value) => handleLevelChange(index, value)"
              />
            </div>
            <!-- 连接线 -->
            <div class="connector" v-if="index < levelSelects.length - 1">
              <svg width="16" height="2" viewBox="0 0 16 2" fill="none">
                <path d="M0 1H16" stroke="#666" stroke-width="1" stroke-dasharray="2 2" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 当前选择路径显示 -->
    <div class="current-path" v-if="currentPath">
      <div class="path-label">当前路径</div>
      <div class="path-display">
        <svg class="folder-icon" width="14" height="14" viewBox="0 0 14 14" fill="none">
          <path
            d="M2 3C2 2.44772 2.44772 2 3 2H5.58579C5.851 2 6.10536 2.10536 6.29289 2.29289L7.70711 3.70711C7.89464 3.89464 8.149 4 8.41421 4H11C11.5523 4 12 4.44772 12 5V10C12 10.5523 11.5523 11 11 11H3C2.44772 11 2 10.5523 2 10V3Z"
            fill="#4A90E2" />
        </svg>
        <span class="path-text">{{ currentPath }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { getMuseRepositories, getFolderTree } from '@/api/muse'

export default {
  name: 'UploadLocationSelector',
  props: {
    // 初始选中的节点
    initialNode: {
      type: Object,
      default: null
    },
    // 初始选中的仓库代码
    initialRepositoryCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      selectedRepositoryId: '',
      selectedRepositoryCode: '',
      folderTree: [],
      levelSelects: [], // 多级选择器数据
      selectedPath: [], // 选中的路径节点
      loading: false
    }
  },
  computed: {
    // 从$bus中获取项目列表
    repositoryList() {
      return this.$bus.museRepositoryList || []
    },

    selectedRepositoryName() {
      const repo = this.repositoryList.find(r => r.id === this.selectedRepositoryId)
      return repo ? repo.name : ''
    },

    // 当前选择的完整路径
    currentPath() {
      if (!this.selectedRepositoryName) return ''

      const pathParts = [this.selectedRepositoryName]

      // 添加选中的文件夹路径
      this.selectedPath.forEach(node => {
        if (node && node.name) {
          pathParts.push(node.name)
        }
      })

      return pathParts.join('/')
    },

    // 当前选中的节点
    selectedNode() {
      if (this.selectedPath.length === 0) {
        // 如果没有选择文件夹，返回根节点
        return {
          name: '全部素材',
          id: null,
          isRoot: true
        }
      }

      // 返回最后选中的节点
      return this.selectedPath[this.selectedPath.length - 1]
    }
  },
  watch: {
    // 监听项目列表变化，初始化选择
    repositoryList: {
      handler(newList) {
        if (newList && newList.length > 0 && !this.selectedRepositoryId) {
          this.initializeRepository()
        }
      },
      immediate: true
    },

    // 监听项目ID变化，重新加载文件夹树
    selectedRepositoryId: {
      handler(newRepositoryId) {
        if (newRepositoryId) {
          this.handleRepositoryChange()
        }
      },
      immediate: false
    },

    selectedNode: {
      handler(newNode) {
        this.$emit('locationChange', {
          node: newNode,
          repositoryCode: this.selectedRepositoryCode,
          repositoryId: this.selectedRepositoryId,
          repositoryName: this.selectedRepositoryName,
          path: this.currentPath
        })
      },
      deep: true
    },

    selectedPath: {
      handler() {
        // 当路径变化时，触发事件
        this.$emit('locationChange', {
          node: this.selectedNode,
          repositoryCode: this.selectedRepositoryCode,
          repositoryId: this.selectedRepositoryId,
          repositoryName: this.selectedRepositoryName,
          path: this.currentPath
        })
      },
      deep: true
    }
  },
  mounted() {
    // 如果项目列表已经存在，立即初始化
    if (this.repositoryList.length > 0) {
      this.initializeRepository()
    }
  },
  methods: {
    // 初始化项目选择
    async initializeRepository() {
      console.log('初始化项目选择，项目列表:', this.repositoryList)

      // 如果有初始仓库代码，优先使用
      if (this.initialRepositoryCode) {
        const repo = this.repositoryList.find(r => r.code === this.initialRepositoryCode)
        if (repo) {
          this.selectedRepositoryId = repo.id
          this.selectedRepositoryCode = repo.code
          await this.loadFolderTree()
          if (this.initialNode) {
            this.selectedNode = this.initialNode
          }
          return
        }
      }
      // 否则默认选择第一个项目
      if (this.repositoryList.length > 0) {
        const firstRepo = this.repositoryList[0]
        this.selectedRepositoryId = firstRepo.id
        this.selectedRepositoryCode = firstRepo.code
        await this.loadFolderTree()
      }
    },
    async handleRepositoryChange() {
      if (!this.selectedRepositoryId) return
      const selectedRepo = this.repositoryList.find(r => r.id === this.selectedRepositoryId)
      if (selectedRepo) {
        // 更新仓库代码
        this.selectedRepositoryCode = selectedRepo.code
        // 重置选中的路径和级联选择器
        this.selectedPath = []
        this.levelSelects = []
        // 重新加载对应项目的文件夹树
        await this.loadFolderTree()
      }
    },

    async loadFolderTree() {
      if (!this.selectedRepositoryCode) return

      try {
        this.loading = true
        const { data } = await getFolderTree(this.selectedRepositoryCode)
        const originalTree = data?.[this.selectedRepositoryCode]

        this.folderTree = originalTree || []

        // 初始化第一级选择器
        this.initializeLevelSelects()

      } catch (error) {
        console.error('加载文件夹树失败:', error)
        // this.$message?.error?.('加载文件夹树失败')
      } finally {
        this.loading = false
      }
    },

    // 初始化级联选择器
    initializeLevelSelects() {
      this.levelSelects = []
      this.selectedPath = []

      // 如果有子文件夹，创建第一级选择器
      if (this.folderTree.length > 0) {
        this.levelSelects.push({
          options: [
            { name: '全部素材', id: null, isRoot: true },
            ...this.folderTree
          ],
          selected: null // 默认选中根节点
        })
      }
    },



    // 处理级联选择变化
    handleLevelChange(levelIndex, selectedValue) {
      console.log('文件夹选择变化:', levelIndex, selectedValue)

      const currentLevel = this.levelSelects[levelIndex]
      if (!currentLevel) return

      // 找到选中的节点
      const selectedNode = currentLevel.options.find(option =>
        (option.id || option.name) === selectedValue
      )

      if (!selectedNode) return

      console.log('选中的文件夹节点:', selectedNode)

      // 更新选中路径（截取到当前级别）
      this.selectedPath = this.selectedPath.slice(0, levelIndex)

      if (!selectedNode.isRoot) {
        this.selectedPath.push(selectedNode)
      }

      // 移除后续级别的选择器
      this.levelSelects = this.levelSelects.slice(0, levelIndex + 1)

      // 如果选中的节点有子节点，添加下一级选择器
      if (selectedNode.children && selectedNode.children.length > 0) {
        this.levelSelects.push({
          options: selectedNode.children,
          selected: ''
        })
        console.log('添加下一级选择器，子节点数量:', selectedNode.children.length)
      }
    },

    // 获取级别标签
    getLevelLabel(index) {
      if (index === 0) return '文件夹'
      return `子文件夹 ${index}`
    },

    // 获取级别占位符
    getLevelPlaceholder(index) {
      if (index === 0) return '请选择文件夹'
      return '请选择子文件夹'
    }
  }
}
</script>

<style lang="less" scoped>
.upload-location-selector {
  padding-bottom: 60px;

  .section-label {
    font-size: 14px;
    font-weight: 500;
    color: #fff;
    margin-bottom: 12px;
  }

  .selectors-container {
    display: flex;
    flex-direction: column;
    gap: 16px;
    margin-bottom: 20px;
  }

  .selector-section {
    width: 100%;
    position: relative;

    &.project-section {
      // 项目选择区域样式，设置更高的z-index
      z-index: 10;
    }

    &.folder-section {
      border-top: 1px solid rgba(255, 255, 255, 0.15);
      padding-top: 16px;
      position: relative;
      z-index: 5; // 文件夹选择器的z-index较低

      &::before {
        content: '';
        position: absolute;
        top: -1px;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.15) 20%, rgba(255, 255, 255, 0.15) 80%, transparent);
      }
    }
  }

  .folder-selectors {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    align-items: flex-start; // 改为顶部对齐，配合margin使用
  }

  .select-item {
    flex: 1;
    min-width: 200px;
    position: relative;

    &.folder-select-item {
      flex: 0 1 auto;
      min-width: 180px;
      margin-bottom: 16px; // 文件夹选择器之间的垂直间距

      &:last-child {
        margin-bottom: 0; // 最后一个不需要下边距
      }

      // 如果有连接线，增加额外的右边距
      &.has-next {
        margin-right: 32px;
        margin-bottom: 20px; // 有连接线时稍微增加下边距
      }
    }

    .select-label {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 12px;
      color: #ccc;
      margin-bottom: 8px;
      font-weight: 500;

      .label-icon {
        opacity: 0.8;
      }
    }

    .select-wrapper {
      position: relative;

      .custom-select {
        width: 100%;
        transition: all 0.3s ease;

        &.folder-select {
          border-color: rgba(255, 255, 255, 0.15);

          &:hover {
            border-color: rgba(74, 144, 226, 0.5);
            box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);
          }
        }

        &:focus-within {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(74, 144, 226, 0.2);
        }
      }
    }

    // 项目选择器的下拉菜单应该有最高的z-index
    &:not(.folder-select-item) .select-wrapper {
      z-index: 15;

      :deep(.u-multi-select) {
        z-index: 15;

        .spectrum-Popover {
          z-index: 100 !important;
        }
      }
    }

    // 文件夹选择器的下拉菜单z-index较低
    &.folder-select-item .select-wrapper {
      z-index: 8;

      :deep(.u-multi-select) {
        z-index: 8;

        .spectrum-Popover {
          z-index: 50 !important;
        }
      }

      .location-select {
        width: 100%;
        padding: 10px 36px 10px 14px;
        background: rgba(255, 255, 255, 0.08);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        color: #fff;
        font-size: 14px;
        appearance: none;
        cursor: pointer;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: #4A90E2;
          box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.15);
          background: rgba(255, 255, 255, 0.12);
        }

        &:hover {
          border-color: rgba(255, 255, 255, 0.3);
          background: rgba(255, 255, 255, 0.1);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        option {
          background: #2a2a2a;
          color: #fff;
          padding: 8px;
        }
      }

      .dropdown-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #999;
        transition: color 0.2s ease;
      }
    }

    .connector {
      position: absolute;
      right: -24px;
      top: 50%;
      transform: translateY(-50%);
      z-index: 1;

      svg {
        opacity: 0.6;
      }
    }

    &.has-next {
      margin-right: 32px;
    }
  }

  .current-path {
    .path-label {
      font-size: 12px;
      color: #ccc;
      margin-bottom: 6px;
    }

    .path-display {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: rgba(74, 144, 226, 0.1);
      border: 1px solid rgba(74, 144, 226, 0.3);
      border-radius: 6px;

      .folder-icon {
        flex-shrink: 0;
        opacity: 0.8;
      }

      .path-text {
        flex: 1;
        font-size: 13px;
        color: #4A90E2;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

// 全局样式，确保项目选择器的下拉菜单在最上层
.upload-location-selector .project-section .u-multi-select .spectrum-Popover {
  z-index: 999 !important;
}

// 文件夹选择器的下拉菜单z-index稍低
.upload-location-selector .folder-section .u-multi-select .spectrum-Popover {
  z-index: 100 !important;
}
</style>
