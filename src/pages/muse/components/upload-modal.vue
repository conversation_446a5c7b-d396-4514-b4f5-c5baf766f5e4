<template>
  <Modal :show="visible" @change="handleVisibleChange" width="600px" height="80vh">
    <template #content>
      <div class="upload-modal-content">
        <!-- 标题栏 -->
        <div class="modal-header">
          <h3 class="modal-title">上传文件</h3>
          <button class="close-btn" @click="handleClose">
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none">
              <path d="M11 1L1 11M1 1L11 11" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
            </svg>
          </button>
        </div>

        <!-- 内容区域 -->
        <div class="modal-body">
          <!-- 文件预览区域 -->
          <div class="preview-section">
            <div class="section-header">
              <div class="section-label">文件预览</div>
              <div class="preview-actions">
                <button
                  v-if="mode === 'save'"
                  class="action-btn refresh-btn"
                  @click="generatePreview"
                  :disabled="isLoadingPreview"
                  title="重新读取画布"
                >
                  <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                    <path d="M1 4v6h6M23 20v-6h-6" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                          stroke-linejoin="round" />
                    <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" stroke="currentColor"
                          stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                  </svg>
                </button>
              </div>
            </div>
            <div class="preview-container">
              <!-- 有预览图时显示 -->
              <div v-if="previewImage" class="preview-image-wrapper">
                <!-- save模式：显示画布预览图，不可点击 -->
                <div v-if="mode === 'save'" class="preview-image-container">
                  <img :src="previewImage" alt="预览图" class="preview-image" />
                </div>

                <!-- upload模式：显示可点击的预览图，用于自主上传 -->
                <div v-else class="preview-image-container clickable" @click="handlePreviewImageClick">
                  <img :src="previewImage" alt="预览图" class="preview-image" />
                  <div class="preview-overlay">
                    <div class="preview-info">
                      <span class="preview-hint">点击更换文件</span>
                    </div>
                  </div>
                </div>
              </div>
              <!-- PSD文件加载中 -->
              <div v-else-if="isLoadingPreview" class="preview-loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在读取PSD文件内容...</div>
              </div>
              <!-- 预览失败 -->
              <div v-else-if="previewError" class="preview-error">
                <svg class="error-icon" viewBox="0 0 24 24" fill="none">
                  <path d="M12 9v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div class="error-text">{{ previewError }}</div>
              </div>
              <!-- 默认状态 -->
              <div v-else class="preview-placeholder">
                <svg class="placeholder-icon" viewBox="0 0 24 24" fill="none">
                  <path
                    d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                </svg>
                <div class="placeholder-text">
                  {{ mode === 'save' ? '等待读取画布' : '点击选择文件' }}
                </div>
                <div class="placeholder-hint">
                  {{ mode === 'save' ? '自动读取当前画布' : '支持JPG、PNG、WEBP、GIF、PSD、PSB格式' }}
                </div>
                <!-- 在upload模式下，提供上传文件的选项 -->
                <div v-if="mode === 'upload'" class="placeholder-actions">
                  <el-upload
                    class="preview-uploader"
                    :auto-upload="false"
                    :show-file-list="false"
                    :on-change="handlePreviewFileChange"
                  >
                    <button class="action-btn upload-btn" title="上传文件">
                      <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                        <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M17 8l-5-5-5 5M12 3v12" stroke="currentColor"
                              stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                      </svg>
                      上传文件
                    </button>
                  </el-upload>
                </div>
              </div>
            </div>
          </div>

          <!-- 文件名称 -->
          <div class="filename-section">
            <div class="section-label">文件名称</div>
            <div class="input-wrapper">
              <gInput
                v-model="fileName"
                class="filename-input"
                placeholder="请输入文件名称"
                @input="validateFileName"
              />
              <div v-if="fileNameError" class="error-message">{{ fileNameError }}</div>
            </div>
          </div>

          <!-- 上传位置选择器 -->
          <UploadLocationSelector
            :initialNode="curNode"
            :initialRepositoryCode="repositoryCode"
            @locationChange="handleLocationChange"
          />
        </div>

        <!-- 底部区域 -->
        <div class="modal-footer">
          <!-- 上传进度条 -->
          <div v-if="uploading && uploadProgress > 0" class="upload-progress">
            <div class="progress-info">
              <span class="progress-label">上传进度</span>
              <span class="progress-text">{{ uploadProgress }}%</span>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: uploadProgress + '%' }"></div>
            </div>
          </div>

          <!-- 按钮区域 -->
          <div class="button-group">
            <GButton
              @click="handleClose"
              :disabled="uploading"
            >
              取消
            </GButton>

            <GButton
              type="primary"
              :loading="uploading"
              :disabled="!canUpload"
              @click="handleUpload"
              style="margin-left: 10px"
            >
              <span v-if="uploading" class="button-spinner"></span>
              {{ getUploadButtonText() }}
            </GButton>
          </div>
          
          <!-- 上传提示信息 -->
          <div v-if="!canUpload && !uploading" class="upload-hint">
            <div v-if="!hasFileToUpload()" class="hint-text">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path d="M12 9v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              {{ mode === 'save' ? '请先打开PS文件' : '请先选择要上传的文件' }}
            </div>
            <div v-else-if="!fileName.trim()" class="hint-text">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path d="M12 9v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              请输入文件名称
            </div>
            <div v-else-if="!selectedLocation" class="hint-text">
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                <path d="M12 9v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              请选择上传位置
            </div>
          </div>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script>
import UploadLocationSelector from './upload-location-selector.vue'
import { getPSDThumbnail, uploadSmartObjectPsd } from '@/utils/fs/index.js'
import { MATERIAL_SOURCE_PATHNAME } from '@/config/index.js'
import { getUploadUrl, publishAsset } from '@/api/muse'
import { createTusUpload } from '@/utils/tus-upload'
import { ps2png } from '@/api'
import PSPreview from '@/assets/img/psd.png'

export default {
  name: 'UploadModal',
  components: {
    UploadLocationSelector
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    mode: {
      type: String,
      default: 'upload'
    },
    // 打开弹窗后是否自动弹出文件选择器（仅 upload 模式有效）
    autoOpenFilePicker: {
      type: Boolean,
      default: false
    },
    // 直接传入要上传的文件（File-like 对象），会在弹窗打开后立即作为预览并用于上传
    initialFile: {
      type: [Object, null],
      default: null
    },
    curNode: {
      type: Object,
      default: () => ({})
    },
    repositoryCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      fileName: '',
      userEditedFileName: false,
      fileNameError: '',
      previewImage: '',
      uploading: false,
      selectedLocation: null,
      // 控制仅自动打开一次文件选择器
      hasAutoOpenedPicker: false,
      // PSD预览相关状态
      isLoadingPreview: false,
      previewError: null,
      currentPSDPath: null,
      // TUS上传相关状态
      uploadProgress: 0,
      uploadStatus: '', // 'uploading', 'success', 'error', 'paused'
      tusUpload: null, // TUS上传实例
      previewTusUpload: null, // 预览图TUS上传实例
      uploadUrl: '',
      uploadToken: '',
      uploadUer: '',
      // 预览图来源标识
      previewSource: '', // 'canvas' | 'upload'
      uploadedPreviewFile: null, // 用户上传的预览图文件
      convertedPreviewPath: null, // PSD转换后的预览图路径（本地文件）
      convertedPreviewUrl: null // ps2png API转换后的预览图URL（在线文件）
    }
  },
  computed: {
    canUpload() {
      // 基本验证
      const basicValidation = this.fileName.trim() && !this.fileNameError && !this.uploading && this.selectedLocation
      
      if (!basicValidation) {
        return false
      }
      
      // 检查是否有文件可以上传
      if (!this.hasFileToUpload()) {
        return false
      }
      
      // save模式需要当前PSD路径
      if (this.mode === 'save' && !this.currentPSDPath) {
        return false
      }
      
      return true
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.resetForm()
        this.loadCurrentDocumentPreview()
        // upload 模式：优先使用 initialFile，否则按需自动弹出文件选择器
        this.$nextTick(() => {
          if (this.mode === 'upload') {
            if (this.initialFile) {
              const fileObj = this.initialFile
              if (fileObj && (fileObj.raw || fileObj.name)) {
                const fileLike = fileObj.raw ? fileObj : { raw: fileObj }
                this.handlePreviewFileChange(fileLike, [])
                return
              }
            }
            if (this.autoOpenFilePicker && !this.hasAutoOpenedPicker && !this.previewImage) {
              this.hasAutoOpenedPicker = true
              setTimeout(() => this.handlePreviewImageClick(), 50)
            }
          }
        })
      }
    }
  },
  mounted() {
    // 组件挂载时尝试加载当前文档预览
    if (this.visible) {
      this.loadCurrentDocumentPreview()
    }
  },
  methods: {
    // 为URL追加时间戳，避免浏览器缓存
    cacheBustUrl(url) {
      if (!url || typeof url !== 'string') return url
      const hasQuery = url.includes('?')
      const sep = hasQuery ? '&' : '?'
      return `${url}${sep}_=${Date.now()}`
    },
    handleVisibleChange(visible) {
      this.$emit('changeVisible', visible)
    },
    handleClose() {
      this.$emit('changeVisible', false)
    },
    resetForm() {
      this.fileName = ''
      this.userEditedFileName = false
      this.fileNameError = ''
      this.previewImage = ''
      this.uploading = false
      this.selectedLocation = null
      this.isLoadingPreview = false
      this.previewError = null
      this.currentPSDPath = null
      this.hasAutoOpenedPicker = false
      // 重置TUS上传状态
      this.uploadProgress = 0
      this.uploadStatus = ''
      this.uploadUrl = ''
      this.uploadToken = ''
      this.uploadUer = ''
      // 如果有正在进行的上传，取消它
      if (this.tusUpload) {
        this.tusUpload.abort()
        this.tusUpload = null
      }
      if (this.previewTusUpload) {
        this.previewTusUpload.abort()
        this.previewTusUpload = null
      }
      // 重置预览图相关状态
      this.previewSource = ''
      this.uploadedPreviewFile = null
      this.convertedPreviewPath = null
      this.convertedPreviewUrl = null

      // 在upload模式下，清空预览错误，因为不需要自动加载画布
      if (this.mode === 'upload') {
        this.previewError = null
      }
    },
    // 加载当前文档预览
    async loadCurrentDocumentPreview() {
      // 只在save模式下自动加载PS画布预览
      if (this.mode !== 'save') {
        return
      }

      this.$logMsg.info('[UploadModal] 开始加载PS画布预览', '预览')

      // 开始加载预览
      this.isLoadingPreview = true
      this.previewError = null
      // 先清空，避免同URL不触发刷新
      this.previewImage = ''

      try {
        // 触发合成并保存到临时目录（与 upload.vue 一致）
        await this.$csCompose()
        const savedName = await this.$csSave()

        // 构造临时保存的绝对路径，作为后续上传与本地预览的依据
        // eslint-disable-next-line no-undef
        const cs = new CSInterface()
        // eslint-disable-next-line no-undef
        const tempDir = `${cs.getSystemPath(SystemPath.USER_DATA)}/${MATERIAL_SOURCE_PATHNAME}`
        const absSavedPath = `${tempDir}/${savedName}`

        // 设置 currentPSDPath 为绝对路径，后续 TUS 上传使用该路径
        this.currentPSDPath = absSavedPath
        // 同时设置用于显示的文件名（移除扩展名）
        if (!this.userEditedFileName) {
          this.fileName = savedName.replace(/\.(psd|psb|jpg|jpeg|png|webp|gif)$/i, '')
        }

        // 优先通过服务器生成缩略图（与 upload.vue 保持一致）
        let serverPreviewDone = false
        try {
          // 避免 uploadSmartObjectPsd 删除源文件：拷贝一份用于生成缩略图
          // eslint-disable-next-line no-undef
          const fs = custom_node.fs
          const path = require('path')
          const copyName = `preview_${Date.now()}_${savedName}`
          const copyAbsPath = path.join(tempDir, copyName)
          fs.copyFileSync(absSavedPath, copyAbsPath)

          const imgdata = await uploadSmartObjectPsd(copyName)
          const serverThumb = imgdata?.thumbnailUrl || imgdata?.data?.thumbnailUrl || imgdata?.data?.cover || imgdata?.cover || imgdata?.url
          if (serverThumb) {
            this.previewImage = this.cacheBustUrl(serverThumb)
            this.previewSource = 'canvas'
            serverPreviewDone = true
            this.$logMsg.info('[UploadModal] 服务器缩略图生成成功', '预览')
          }
        } catch (e) {
          this.$logMsg.warn(`[UploadModal] 服务器预览生成失败，回退本地：${e.message}`, '预览')
        }

        // 回退：基于最新保存的 absSavedPath 进行本地生成，确保预览为最新画布
        if (!serverPreviewDone) {
          if (savedName.toLowerCase().endsWith('.psd') || savedName.toLowerCase().endsWith('.psb')) {
            const [previewDir, previewFileName] = await getPSDThumbnail(absSavedPath)
            const previewPath = `${previewDir}/${previewFileName}`
            this.previewImage = this.cacheBustUrl(this.generateFileUrl(previewPath))
          } else {
            this.previewImage = this.cacheBustUrl(this.generateFileUrl(absSavedPath))
          }
          this.previewSource = 'canvas'
        }
      } catch (error) {
        this.$logMsg.error(`[UploadModal] 加载PS画布预览失败: ${error.message}`, '预览')
        this.previewError = error.message || '无法读取当前画布'
        this.$CustomToast({ type: 'error', duration: 2, content: this.previewError })
      } finally {
        this.isLoadingPreview = false
      }
    },
    // 重新生成预览图（读取画布）
    generatePreview() {
      this.loadCurrentDocumentPreview()
    },
    // 处理预览图点击（upload模式）
    handlePreviewImageClick() {
      if (this.mode !== 'upload') {
        return
      }

      // 创建隐藏的文件输入框
      const fileInput = document.createElement('input')
      fileInput.type = 'file'
      fileInput.accept = 'image/*,.psd,.psb'
      fileInput.style.display = 'none'

      fileInput.onchange = (event) => {
        const file = event.target.files[0]
        if (file) {
          this.handlePreviewFileChange({ raw: file }, [])
        }
        // 清理DOM
        document.body.removeChild(fileInput)
      }

      // 添加到DOM并触发点击
      document.body.appendChild(fileInput)
      fileInput.click()
    },
    // 删除预览图
    removePreviewImage() {
      if (this.previewSource === 'upload') {
        this.previewImage = ''
        this.uploadedPreviewFile = null
        this.convertedPreviewPath = null
        this.convertedPreviewUrl = null
        this.previewSource = ''

        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '预览图已删除'
        })
      }
    },
    // 处理预览图文件选择（el-upload的on-change事件）
    handlePreviewFileChange(file, fileList) {
      if (!file || !file.raw) {
        this.$CustomToast({
          type: 'error',
          content: '文件读取失败，请重试'
        })
        return
      }
      // 使用file.raw获取原始文件对象
      const rawFile = file.raw
      // 验证文件类型
      if (!this.isValidPreviewFile(rawFile)) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '请选择支持的文件格式：JPG、PNG、WEBP、GIF、PSD、PSB'
        })
        return
      }
      // 验证文件大小（根据文件类型设置不同限制）
      const maxSize = this.getMaxFileSize(rawFile)
      if (rawFile.size > maxSize) {
        const maxSizeMB = Math.round(maxSize / (1024 * 1024))
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: `文件大小不能超过${maxSizeMB}MB`
        })
        return
      }
      // 保存文件引用
      this.uploadedPreviewFile = rawFile

      // 在upload模式下，自动设置文件名（如果用户还没有输入）
      if (this.mode === 'upload' && !this.fileName.trim()) {
        const fileName = rawFile.name
        this.fileName = fileName.replace(/\.(psd|psb|jpg|jpeg|png|webp|gif)$/i, '')
      }

      // 创建预览URL
      this.generateFilePreview(rawFile)
    },
    // 验证预览文件类型
    isValidPreviewFile(file) {
      const fileName = file.name.toLowerCase()
      const fileType = file.type.toLowerCase()
      // 支持的文件扩展名
      const validExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.gif', '.psd', '.psb']
      const hasValidExtension = validExtensions.some(ext => fileName.endsWith(ext))
      // 对于PSD/PSB文件，主要依赖扩展名判断（因为MIME类型不统一）
      if (fileName.endsWith('.psd') || fileName.endsWith('.psb')) {
        return true
      }
      // 支持的MIME类型（主要用于图片文件）
      const validMimeTypes = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/webp',
        'image/gif',
        'application/octet-stream', // 通用二进制文件类型
        'image/vnd.adobe.photoshop', // 标准PSD MIME类型
        'image/x-photoshop', // 另一种PSD MIME类型
        ''  // 有些系统可能返回空的MIME类型
      ]
      const hasValidMimeType = validMimeTypes.includes(fileType)
      return hasValidExtension && (hasValidMimeType || fileType.startsWith('image/'))
    },

    // 根据文件类型获取最大文件大小
    getMaxFileSize(file) {
      const fileName = file.name.toLowerCase()

      // PSD/PSB文件限制为15MB
      if (fileName.endsWith('.psd') || fileName.endsWith('.psb')) {
        return 20 * 1024 * 1024
      }

      // 普通图片文件限制为20MB
      return 10 * 1024 * 1024
    },

    // 生成跨平台兼容的文件URL
    generateFileUrl(filePath) {
      // eslint-disable-next-line no-undef
      const cs = new CSInterface()
      const osInfo = cs.getOSInformation()
      const isWindows = osInfo.indexOf('Windows') > -1

      if (isWindows) {
        // Windows: 确保路径使用正斜杠，并添加file:///前缀
        const normalizedPath = filePath.replace(/\\/g, '/')
        // 如果路径已经有file://前缀，先移除再重新添加
        const cleanPath = normalizedPath.replace(/^file:\/\/\/?/, '')
        return `file:///${cleanPath}`
      } else {
        // macOS: 添加file://前缀
        const cleanPath = filePath.replace(/^file:\/\/\/?/, '')
        return `file://${cleanPath}`
      }
    },

    // 生成文件预览
    generateFilePreview(file) {
      const fileName = file.name.toLowerCase()

      // 对于PSD/PSB文件，尝试转换为PNG预览
      if (fileName.endsWith('.psd') || fileName.endsWith('.psb')) {
        this.convertPsdToPng(file)
        return
      }

      // 对于图片文件，生成图片预览
      const reader = new FileReader()
      reader.onload = (e) => {
        this.previewImage = e.target.result
        this.previewSource = 'upload'
        this.previewError = null

        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '预览图上传成功'
        })
      }
      reader.onerror = () => {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '读取图片文件失败'
        })
      }
      reader.readAsDataURL(file)
    },

    // 转换PSD/PSB文件为PNG预览图
    async convertPsdToPng(file) {
      try {
        // 显示转换中状态
        this.isLoadingPreview = true
        this.previewError = null
        this.previewImage = ''
        this.$CustomToast({
          type: 'info',
          duration: 2,
          content: 'PSD文件转换中，请稍候...'
        })

        // 如果文件大于10MB，直接使用内置PS预览图，不走API转换
        if (file.size > 10 * 1024 * 1024) {
          this.$logMsg.info('[PSD转换] 文件大于10MB，使用内置PS预览图，跳过转换', 'PSD转换')
          this.previewImage = PSPreview
          this.previewSource = 'upload'
          this.previewError = null
          this.$CustomToast({
            type: 'info',
            duration: 2,
            content: '文件较大，已使用默认预览图'
          })
          return
        }

        // 方法1: 尝试使用ps2png API
        try {
          this.$logMsg.info('[PSD转换] 尝试使用ps2png API', 'PSD转换')
          await this.convertPsdWithApi(file)
          return // 成功则直接返回
        } catch (apiError) {
          this.$logMsg.warn(`[PSD转换] ps2png API失败: ${apiError.message}`, 'PSD转换')
        }

        // 方法2: 回退到本地PSD库处理
        this.$logMsg.info('[PSD转换] 回退到本地PSD库处理', 'PSD转换')
        await this.convertPsdWithLocalLib(file)

      } catch (error) {
        this.$logMsg.error(`[PSD转换] 所有转换方法都失败: ${error.message}`, 'PSD转换')
        // 转换失败，显示文件信息预览
        this.showFileInfoPreview(file)
        this.$CustomToast({
          type: 'warning',
          duration: 3,
          content: 'PSD转换失败，显示文件信息'
        })
      } finally {
        this.isLoadingPreview = false
      }
    },

    // 使用ps2png API转换PSD
    async convertPsdWithApi(file) {
      // 第一步：先上传PSD文件获得在线URL
      this.$logMsg.info('[PSD转换] 开始上传PSD文件获取在线URL', 'PSD转换')
      const psdUrl = await this.uploadFileForPs2png(file)

      // 第二步：调用ps2png API进行转换
      const apiParams = {
        fileName: file.name,  // 带后缀的文件名
        url: psdUrl,         // PSD在线链接
        size: file.size      // number类型的文件大小
      }

      this.$logMsg.info(`[PSD转换] 调用ps2png API，参数: ${JSON.stringify(apiParams)}`, 'PSD转换')
      const response = await ps2png(apiParams)

      this.$logMsg.info(`[PSD转换] ps2png API响应: ${JSON.stringify(response)}`, 'PSD转换')

      // 处理API响应
      let imageUrl = null
      if (response && response.data) {
        imageUrl = response.data.url || response.data.data?.url || response.data.result?.url || response.data
      } else if (response && response.url) {
        imageUrl = response.url
      }

      if (imageUrl && typeof imageUrl === 'string') {
        this.previewImage = imageUrl
        this.previewSource = 'upload'
        this.previewError = null

        // 仅用于界面预览，不参与上传
        this.convertedPreviewUrl = imageUrl

        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: 'PSD文件转换成功'
        })
        this.$logMsg.info(`[PSD转换] ps2png API转换成功，图片URL: ${imageUrl}`, 'PSD转换')
      } else {
        this.$logMsg.error(`[PSD转换] API返回数据格式错误: ${JSON.stringify(response)}`, 'PSD转换')
        throw new Error('API返回数据格式错误')
      }
    },

    // 上传文件用于ps2png转换
    async uploadFileForPs2png(file) {
      try {
        this.$logMsg.info('[PSD转换] 使用nos-upload上传文件获取公开URL', 'PSD转换')

        // 使用nos-upload上传文件，它会返回公开访问的URL
        const nosUpload = (await import('@/utils/nos-upload.js')).default

        const uploadResult = await nosUpload(file, (progress) => {
          this.$logMsg.info(`[PSD转换] 上传进度: ${progress}%`, 'PSD转换')
        })

        this.$logMsg.info(`[PSD转换] 文件上传成功，URL: ${uploadResult.url}`, 'PSD转换')
        return uploadResult.url

      } catch (error) {
        this.$logMsg.error(`[PSD转换] 上传文件失败: ${error.message}`, 'PSD转换')
        throw new Error(`无法上传PSD文件获取公开URL: ${error.message}`)
      }
    },

    // 使用本地PSD库转换
    async convertPsdWithLocalLib(file) {
      // 将File对象保存为临时文件
      const tempFileName = `temp_${Date.now()}_${file.name}`
      const tempFilePath = await this.saveFileToTemp(file, tempFileName)

      this.$logMsg.info(`[PSD转换] 临时文件保存成功: ${tempFilePath}`, 'PSD转换')

      // 使用getPSDThumbnail处理临时PSD文件
      const [previewDir, previewFileName] = await getPSDThumbnail(tempFilePath)
      const previewPath = `${previewDir}/${previewFileName}`

      this.$logMsg.info(`[PSD转换] PNG生成成功: ${previewPath}`, 'PSD转换')

      // 显示预览图
      this.previewImage = this.generateFileUrl(previewPath)
      this.previewSource = 'upload'
      this.previewError = null
      // 保存转换后的预览图路径，用于后续上传
      this.convertedPreviewPath = previewPath
      // 清理临时PSD文件
      this.cleanupTempFile(tempFilePath)

      this.$CustomToast({
        type: 'success',
        duration: 2,
        content: 'PSD文件转换成功'
      })
      this.$logMsg.info('[PSD转换] 本地库转换成功', 'PSD转换')
    },

    // 显示文件信息预览（用于PSD/PSB等无法直接预览的文件）
    showFileInfoPreview(file) {
      // 创建一个文件信息预览
      const canvas = document.createElement('canvas')
      const ctx = canvas.getContext('2d')
      canvas.width = 400
      canvas.height = 300

      // 设置背景
      ctx.fillStyle = '#2a2a2a'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      // 绘制文件图标区域
      ctx.fillStyle = '#4a4a4a'
      ctx.fillRect(50, 50, 300, 200)

      // 绘制文件类型图标
      ctx.fillStyle = '#666'
      ctx.font = 'bold 48px Arial'
      ctx.textAlign = 'center'
      const fileExt = file.name.split('.').pop().toUpperCase()
      ctx.fillText(fileExt, canvas.width / 2, 140)

      // 绘制文件信息
      ctx.fillStyle = '#ccc'
      ctx.font = '14px Arial'
      ctx.textAlign = 'center'

      // 文件名
      const displayName = file.name.length > 30 ? file.name.substring(0, 27) + '...' : file.name
      ctx.fillText(displayName, canvas.width / 2, 180)

      // 文件大小
      const fileSize = this.formatFileSize(file.size)
      ctx.fillText(fileSize, canvas.width / 2, 200)

      // 转换为DataURL
      this.previewImage = canvas.toDataURL()
      this.previewSource = 'upload'
      this.previewError = null

      this.$CustomToast({
        type: 'success',
        duration: 2,
        content: `${fileExt}文件上传成功`
      })
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },
    // 获取当前活动文档路径
    async getCurrentDocumentPath() {
      return new Promise((resolve, reject) => {
        try {
          // eslint-disable-next-line no-undef
          const cs = new CSInterface()
          // 先检查是否有活动文档
          cs.evalScript('app.documents.length', (docCount) => {
            if (!docCount || docCount === '0') {
              reject(new Error('没有打开的文档，请先在Photoshop中打开一个PSD文件'))
              return
            }
            // 获取当前活动文档路径
            cs.evalScript('app.activeDocument.fullName.fsName', (result) => {
              if (result && result !== 'undefined' && result !== 'null') {
                // 清理路径中的引号
                const cleanPath = result.replace(/^["']|["']$/g, '')
                resolve(cleanPath)
              } else {
                // 尝试获取文档名称作为备用信息
                cs.evalScript('app.activeDocument.name', (docName) => {
                  reject(new Error(`当前文档 "${docName}" 可能未保存，请先保存文档`))
                })
              }
            })
          })
        } catch (error) {
          reject(error)
        }
      })
    },
    // 检查是否为PSD文件
    isPSDFile(filePath) {
      const ext = filePath.split('.').pop()?.toLowerCase()
      return ext === 'psd' || ext === 'psb'
    },
    // 从文件路径提取文件名
    getFileNameFromPath(filePath) {
      return filePath.split(/[\\/]/).pop() || ''
    },

    // 获取上传按钮文本
    getUploadButtonText() {
      if (!this.uploading) {
        return '开始上传'
      }
      switch (this.uploadStatus) {
        case 'uploading':
          return `上传中... ${this.uploadProgress}%`
        case 'processing':
          return '处理中...'
        case 'success':
          return '上传成功'
        case 'error':
          return '上传失败'
        default:
          return '上传中...'
      }
    },
    handleLocationChange(locationData) {
      console.log('📍 位置选择变化:', locationData)
      this.selectedLocation = locationData
    },
    validateFileName() {
      // 标记用户已手动编辑
      this.userEditedFileName = true
      const name = this.fileName.trim()
      if (!name) {
        this.fileNameError = '文件名不能为空'
        return
      }
      if (name.length > 100) {
        this.fileNameError = '文件名不能超过100个字符'
        return
      }
      // 检查非法字符
      const invalidChars = /[<>:"/\\|?*]/
      if (invalidChars.test(name)) {
        this.fileNameError = '文件名包含非法字符'
        return
      }
      this.fileNameError = ''
    },

    async handleUpload() {
      if (!this.canUpload) return
      
      // 检查是否有文件可以上传
      if (!this.hasFileToUpload()) {
        this.$CustomToast({
          type: 'error',
          duration: 3,
          content: '没有可上传的文件，请先选择文件或确保画布已打开'
        })
        return
      }
      
      this.uploading = true
      this.uploadStatus = 'uploading'
      this.uploadProgress = 0
      try {
        // 1. 获取上传URL和token
        this.$logMsg.info('[UploadModal] 开始获取上传地址和token', '上传')
        /* eslint-disable camelcase */
        const { data: { upload_url, token, user } } = await getUploadUrl({
          organization: this.selectedLocation.repositoryCode
        })
        if (!upload_url || !token) {
          throw new Error('无法获取上传地址或token')
        }
        this.uploadUer = user
        this.uploadUrl = upload_url
        this.uploadToken = token
        // 2. 上传文件
        this.$logMsg.info('[UploadModal] 开始上传文件', '上传')
        this.uploadStatus = 'uploading'

        let mainUploadResult
        let previewUploadResult

        // 判断上传什么文件
        if (this.previewSource === 'upload' && (this.uploadedPreviewFile || this.convertedPreviewUrl)) {
          // 用户上传了文件，上传用户的文件作为主文件
          this.$logMsg.info('[UploadModal] 上传用户上传的文件作为主文件', '上传')
          mainUploadResult = await this.uploadUserFile()
        } else if (this.currentPSDPath) {
          // 有PS画布文件，上传PS画布中的文件
          this.$logMsg.info('[UploadModal] 上传PS画布中的文件', '上传')
          mainUploadResult = await this.startTusUpload()
        } else {
          throw new Error('没有可上传的文件')
        }

        // 3. 发布
        this.$logMsg.info('[UploadModal] 开始发布素材', '上传')
        this.uploadStatus = 'processing'
        await this.publishUploadedAsset(mainUploadResult, previewUploadResult)
        // 5. 上传成功
        this.uploadStatus = 'success'
        // 上传到主站 需要审核
        if (this.selectedLocation.repositoryCode === 'muse') {
          this.$CustomToast({
            type: 'warning',
            duration: 5,
            content: '待管理员审核通过，作品将自动发布'
          })
        } else {
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content: '文件上传成功'
          })
        }
        setTimeout(() => {
          this.$emit('uploadSuccess', {
            name: this.fileName,
            location: this.selectedLocation
          })
          this.handleClose()
        }, 2000)
      } catch (error) {
        this.uploadStatus = 'error'
        this.$logMsg.error(`[UploadModal] 上传失败: ${error.message}`, '上传')
        this.$CustomToast({
          type: 'error',
          duration: 3,
          content: `上传失败: ${error.message}`
        })
      } finally {
        setTimeout(() => {
          this.uploading = false
        }, 2000)
      }
    },
    // 检查是否有文件可以上传
    hasFileToUpload() {
      // 检查用户上传的文件
      if (this.previewSource === 'upload' && (this.uploadedPreviewFile || this.convertedPreviewUrl)) {
        return true
      }
      
      // 检查PS画布文件
      if (this.currentPSDPath) {
        return true
      }
      
      return false
    },
    
    // 开始TUS上传
    async startTusUpload() {
      return new Promise((resolve, reject) => {
        try {
          const path = require('path')
          const ext = (path.extname(this.currentPSDPath) || '.psd').replace(/^\./, '')
          const customName = `${this.fileName}.${ext}`
          this.tusUpload = createTusUpload({
            filePath: this.currentPSDPath,
            uploadUrl: this.uploadUrl,
            token: this.uploadToken,
            customFileName: customName, // 使用自定义名称+原扩展名
            metadata: {
              filename: customName,
              organization: this.selectedLocation.repositoryCode,
              category: this.selectedLocation.categoryId || ''
            },
            onProgress: (progress, uploadedBytes, totalBytes) => {
              this.uploadProgress = progress
              this.$logMsg?.info?.(`[TUS] 上传进度: ${progress}% (${uploadedBytes}/${totalBytes})`, 'TUS上传') || console.log(`[TUS] 上传进度: ${progress}%`)
            },
            onSuccess: (result) => {
              this.$logMsg.info('[TUS] 文件上传成功', 'TUS上传')
              // 返回上传结果，包含文件信息
              resolve({
                fileName: result.fileName,
                fileSize: result.fileSize,
                uploadUrl: result.uploadUrl,
                originalPath: this.currentPSDPath
              })
            },
            onError: (error) => {
              this.$logMsg.error(`[TUS] 文件上传失败: ${error.message}`, 'TUS上传')
              reject(error)
            }
          })
          // 开始上传
          this.tusUpload.start()
        } catch (error) {
          reject(error)
        }
      })
    },

    // 上传用户上传的文件作为主文件（不涉及预览参数）
    async uploadUserFile() {
      try {
        if (this.uploadedPreviewFile) {
          this.$logMsg.info('[UploadModal] 上传用户上传的文件', '上传')

          // 保存文件到临时位置
          const tempFileName = `${Date.now()}_${this.uploadedPreviewFile.name}`
          const tempFilePath = await this.saveFileToTemp(this.uploadedPreviewFile, tempFileName)

          // 使用TUS上传
          return new Promise((resolve, reject) => {
            const path = require('path')
            const ext = (path.extname(this.uploadedPreviewFile.name) || '').replace(/^\./, '')
            const customName = ext ? `${this.fileName}.${ext}` : this.fileName
            const upload = createTusUpload({
              filePath: tempFilePath,
              uploadUrl: this.uploadUrl,
              token: this.uploadToken,
              customFileName: customName,
              onProgress: (progress, uploadedBytes, totalBytes) => {
                this.uploadProgress = progress
                this.$logMsg.info(`[UploadModal] 用户文件上传进度: ${progress}%`, '上传')
              },
              onSuccess: (result) => {
                this.$logMsg.info('[UploadModal] 用户文件上传成功', '上传')
                resolve({
                  fileName: result.fileName,
                  fileSize: result.fileSize,
                  uploadUrl: result.uploadUrl
                })
              },
              onError: (error) => {
                this.$logMsg.error(`[UploadModal] 用户文件上传失败: ${error.message}`, '上传')
                reject(error)
              }
            })
            upload.start()
          })
        }

        throw new Error('没有可上传的用户文件')
      } catch (error) {
        this.$logMsg.error(`[UploadModal] 上传用户文件失败: ${error.message}`, '上传')
        throw error
      }
    },
    // 将File对象保存为临时文件
    async saveFileToTemp(file, fileName) {
      return new Promise((resolve, reject) => {
        try {
          // eslint-disable-next-line no-undef
          const fs = custom_node.fs
          const path = require('path')
          const os = require('os')
          // 创建临时目录
          const tempDir = path.join(os.tmpdir(), 'muse-preview-upload')
          if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true })
          }
          // 生成临时文件路径
          const tempFilePath = path.join(tempDir, fileName)
          // 读取File对象内容
          const reader = new FileReader()
          reader.onload = (e) => {
            try {
              // 将ArrayBuffer转换为Buffer
              const buffer = Buffer.from(e.target.result)
              // 写入临时文件
              fs.writeFileSync(tempFilePath, buffer)
              this.$logMsg.info(`[TUS] 临时文件已保存: ${tempFilePath}`, 'TUS上传')
              resolve(tempFilePath)
            } catch (writeError) {
              this.$logMsg.error(`[TUS] 保存临时文件失败: ${writeError.message}`, 'TUS上传')
              reject(writeError)
            }
          }
          reader.onerror = () => {
            const error = new Error('读取文件失败')
            this.$logMsg.error(`[TUS] 读取文件失败`, 'TUS上传')
            reject(error)
          }
          reader.readAsArrayBuffer(file)
        } catch (error) {
          reject(error)
        }
      })
    },
    // 清理临时文件
    cleanupTempFile(filePath) {
      try {
        // eslint-disable-next-line no-undef
        const fs = custom_node.fs
        if (fs.existsSync(filePath)) {
          fs.unlinkSync(filePath)
          this.$logMsg.info(`[TUS] 临时文件已清理: ${filePath}`, 'TUS上传')
        }
      } catch (error) {
        this.$logMsg.warn(`[TUS] 清理临时文件失败: ${error.message}`, 'TUS上传')
      }
    },
    // 发布上传的素材
    async publishUploadedAsset(uploadResult, previewUploadResult = null) {
      console.log('📤 发布素材数据:', { uploadResult, previewUploadResult })
      console.log('📍 当前选择的位置:', this.selectedLocation)

      // 安全处理categoryName
      let categoryName = ''
      if (this.selectedLocation.path) {
        const pathParts = this.selectedLocation.path.split('/')
        console.log('📂 路径分割结果:', pathParts)

        // 移除第一个元素（项目名），剩下的就是文件夹路径
        const folderParts = pathParts.slice(1)
        console.log('📂 文件夹路径部分:', folderParts)

        categoryName = folderParts.join(',')
      }
      console.log('📂 最终categoryName:', categoryName)

      const publishData = {
        organization: this.selectedLocation.repositoryCode,
        image_type: 'single',
        categoryName: categoryName,
        attachment_task_list: [
          {
            folder: false,
            name: uploadResult.fileName,
            url: uploadResult.uploadUrl
          }
        ],
        // 通用信息
        common: {
          copyright: true,
          business_type: true,
          new_author: this.uploadUer,
          type: 'common',
          proj_visible: true
        },
        name_multil: {
          zh_CN: this.fileName
        }
      }
      this.$logMsg.info('[UploadModal] 调用发布接口', '上传')

      const result = await publishAsset(publishData)
      if (result.code) {
        this.$logMsg.error('[UploadModal] 发布接口返回错误:', result)
        throw new Error(result.msg)
      } else {
        this.$logMsg.info('[UploadModal] 素材发布成功', '上传')
        return result
      }
    }
  }
}
</script>

<style lang="less" scoped>
.upload-modal-content {
  background: #535353;
  border-radius: 10px;
  overflow: hidden;
  height: 100%;
  display: flex;
  flex-direction: column;

  .modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #666;

    .modal-title {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #fff;
    }

    .close-btn {
      background: none;
      border: none;
      color: #999;
      cursor: pointer;
      padding: 4px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        color: #fff;
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }

  .modal-body {
    padding: 20px 24px;
    flex: 1;
    overflow-y: auto;

    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }

    .section-label {
      font-size: 14px;
      font-weight: 500;
      color: #fff;
      margin-bottom: 8px;
    }

    // 预览区域
    .preview-section {
      margin-bottom: 20px;

      .section-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .section-label {
          font-size: 14px;
          font-weight: 500;
          color: #fff;
        }

        .preview-actions {
          display: flex;
          gap: 8px;

          .action-btn {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 6px 12px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            color: #ccc;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover:not(:disabled) {
              background: rgba(255, 255, 255, 0.15);
              border-color: rgba(255, 255, 255, 0.3);
              color: #fff;
            }

            &:disabled {
              opacity: 0.5;
              cursor: not-allowed;
            }

            svg {
              flex-shrink: 0;
            }
          }

          .upload-btn {
            &:hover:not(:disabled) {
              border-color: #4A90E2;
              color: #4A90E2;
            }
          }
        }
      }

      .preview-uploader {
        display: inline-block;

        :deep(.el-upload) {
          display: inline-block;
        }
      }

      .preview-container {
        width: 100%;
        height: 200px;
        border: 2px dashed #666;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.02);
        text-align: center;
        color: #999;
        margin: 8px 0 0;
        font-size: 14px;
        position: relative;
        overflow: hidden;

        // 预览图片
        .preview-image-wrapper {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          position: relative;

          .preview-image-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            .preview-image {
              max-width: 100%;
              max-height: 100%;
              object-fit: contain;
              border-radius: 4px;
            }

            .preview-overlay {
              position: absolute;
              top: 8px;
              right: 8px;
              display: flex;
              flex-direction: column;
              align-items: flex-end;
              gap: 4px;

              .preview-info {
                display: flex;
                flex-direction: column;
                align-items: flex-end;
                gap: 2px;

                .preview-source {
                  background: rgba(0, 0, 0, 0.7);
                  color: #fff;
                  padding: 4px 8px;
                  border-radius: 4px;
                  font-size: 11px;
                  font-weight: 500;
                }

                .preview-hint {
                  background: rgba(0, 0, 0, 0.7);
                  color: #fff;
                  padding: 4px 8px;
                  border-radius: 4px;
                  font-size: 11px;
                  font-weight: 500;
                  white-space: nowrap;
                }
              }

              .remove-preview-btn {
                background: rgba(255, 0, 0, 0.8);
                border: none;
                border-radius: 50%;
                width: 24px;
                height: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #fff;
                cursor: pointer;
                transition: all 0.2s ease;

                &:hover {
                  background: rgba(255, 0, 0, 1);
                  transform: scale(1.1);
                }
              }
            }
          }

          .clickable {
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              opacity: 0.9;
              transform: scale(1.02);
            }
          }
        }

        // 加载状态
        .preview-loading {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .loading-spinner {
            width: 32px;
            height: 32px;
            border: 3px solid #666;
            border-top: 3px solid #1473e6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 12px;
          }

          .loading-text {
            color: #ccc;
            font-size: 13px;
          }
        }

        // 错误状态
        .preview-error {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .error-icon {
            width: 32px;
            height: 32px;
            color: #e74c3c;
            margin-bottom: 8px;
          }

          .error-text {
            color: #e74c3c;
            font-size: 13px;
            text-align: center;
          }
        }

        // 占位符状态
        .preview-placeholder {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .placeholder-icon {
            width: 40px;
            height: 40px;
            color: #666;
            margin-bottom: 8px;
          }

          .placeholder-text {
            color: #999;
            font-size: 14px;
            margin-bottom: 4px;
          }

          .placeholder-hint {
            color: #666;
            font-size: 12px;
            margin-bottom: 12px;
          }

          .placeholder-actions {
            display: flex;
            gap: 8px;

            .action-btn {
              display: flex;
              align-items: center;
              gap: 6px;
              padding: 6px 12px;
              border-radius: 4px;
              font-size: 12px;
              cursor: pointer;
              transition: all 0.2s ease;
              border: 1px solid;

              svg {
                width: 12px;
                height: 12px;
              }
            }

            .upload-btn {
              background: rgba(74, 144, 226, 0.1);
              border-color: rgba(74, 144, 226, 0.3);
              color: #4A90E2;

              &:hover {
                background: rgba(74, 144, 226, 0.2);
                border-color: rgba(74, 144, 226, 0.5);
              }
            }
          }
        }
      }
    }


    // 文件名称
    .filename-section {
      margin-bottom: 20px;

      .input-wrapper {
        position: relative;

        .filename-input {
          width: 100%;
          height: 38px;
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid #666;
          border-radius: 6px;
          color: #fff;
          font-size: 14px;
          transition: all 0.2s ease;

          &::placeholder {
            color: #999;
          }

          &:focus {
            outline: none;
            border-color: #4A90E2;
            box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
          }

          &.error {
            border-color: #e74c3c;
          }
        }

        .error-message {
          position: absolute;
          top: 100%;
          left: 0;
          margin-top: 4px;
          font-size: 12px;
          color: #e74c3c;
        }
      }
    }


  }

  .modal-footer {
    padding: 20px 24px;
    border-top: 1px solid #666;
    background: rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    gap: 16px;

    .upload-progress {
      display: flex;
      flex-direction: column;
      gap: 8px;

      .progress-info {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .progress-label {
          color: #ccc;
          font-size: 13px;
          font-weight: 500;
        }

        .progress-text {
          color: #4A90E2;
          font-size: 13px;
          font-weight: 600;
        }
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        overflow: hidden;
        box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.2);

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #1473e6, #4A90E2);
          border-radius: 4px;
          transition: width 0.3s ease;
          box-shadow: 0 1px 3px rgba(74, 144, 226, 0.3);
        }
      }
    }

    .button-group {
      display: flex;
      gap: 12px;
      justify-content: flex-end;
      align-items: center;
      margin-top: 10px;
    }
    
    .upload-hint {
      margin-top: 8px;
      
      .hint-text {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #ffa500;
        font-size: 12px;
        
        svg {
          color: #ffa500;
          flex-shrink: 0;
        }
      }
    }
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>