<template>
  <div ref="thumbnail" class="thumbnail" @mouseover.capture="handleHover">
    <div
      v-if="scale <= 40 && !isHoverMore"
      :class="{
        'preview-image--bottom': !overflowTop,
        'preview-image--top': overflowTop,
        'preview-image--right': overflowRight,
        'preview-image--left': !overflowRight,
      }"
      :style="{ width: item.height > item.width ? '200px' : '500px' }"
      class="preview-image"
    >
      <ImgViewer
        ref="ImgViewer"
        :alt="item.name"
        :src="item.thumbnailUrl"
        style="width: 100%; height: auto;"
      />
      <div class="detail-info">
        <div class="name">{{ item.name }}</div>
        <div class="info">
          <span>{{ (item.width && item.height) ? `${item.width} \&times; ${item.height} px` : '*' }}</span>
          <span>{{ item.created_at ? dayjs(item.created_at * 1000).format('YYYY-MM-DD') : '-' }}</span>
        </div>
      </div>
    </div>

    <div class="spectrum-Thumbnail" style="padding: 0" @click="handleItemClick(item)">
      <ImgViewer
        :alt="item.name"
        :src="item.thumbnailUrl"
        class="spectrum-Thumbnail-image"
      />
    </div>

    <img :src="getImg(item)" alt class="u-tag" height="14" />

    <div v-if="scale > 40" class="u-options mode">
      <div class="imgInfo">
        <div class="name">{{ item.name }}</div>
        <div class="size">
          <span>{{ (item.width && item.height) ? `${item.width} \&times; ${item.height} px` : '*' }}</span>
          <span style="margin-left: 10px;">{{ item.updateTime?.split(' ')[0] || '-' }}</span>
        </div>
      </div>
      <div class="action-buttons">
        <div class="actionBtn downloadBtn" @click.stop="handlePut(item)">下载</div>
        <Tooltip content="复制APC链接" placement="top" theme="dark">
          <div class="actionBtn copyBtn" @click.stop="handleCopyApcLink(item)">
            <svg viewBox="0 0 14 14" fill="none">
              <path
                d="M9.33 2.33H10.5C11.1904 2.33 11.67 2.80964 11.67 3.5V10.5C11.67 11.1904 11.1904 11.67 10.5 11.67H3.5C2.80964 11.67 2.33 11.1904 2.33 10.5V9.33"
                stroke="currentColor" stroke-width="1.2" stroke-linecap="round" />
              <path
                d="M7 2.33H3.5C2.80964 2.33 2.33 2.80964 2.33 3.5V7C2.33 7.69036 2.80964 8.17 3.5 8.17H7C7.69036 8.17 8.17 7.69036 8.17 7V3.5C8.17 2.80964 7.69036 2.33 7 2.33Z"
                stroke="currentColor" stroke-width="1.2" />
            </svg>
          </div>
        </Tooltip>
      </div>
    </div>

    <div v-if="loadingView" class="u-options-loading">
      <div class="progress">
        <div :style="{ width: `${processNum}%` }" class="u-options-progress" />
        <div class="u-options-text">{{ processNum }}%</div>
      </div>
    </div>

    <!-- More菜单按钮 -->
    <div class="more-menu-container">
      <div
        class="more-button"
        @mouseenter="showMoreMenu"
        @mouseleave="hideMoreMenu"
      >
        <svg class="more-icon" viewBox="0 0 16 16" fill="none">
          <circle cx="8" cy="3" r="1.5" fill="currentColor" />
          <circle cx="8" cy="8" r="1.5" fill="currentColor" />
          <circle cx="8" cy="13" r="1.5" fill="currentColor" />
        </svg>
      </div>

      <!-- 下拉菜单 -->
      <div
        v-if="moreMenuVisible"
        class="more-menu"
        @mouseenter="showMoreMenu"
        @mouseleave="hideMoreMenu"
      >
        <div class="menu-item" @click="handleCopyApcLinkFromMenu">
          <svg class="menu-icon" viewBox="0 0 16 16" fill="none">
            <path d="M10.5 3H12a1.5 1.5 0 0 1 1.5 1.5v8A1.5 1.5 0 0 1 12 14H4a1.5 1.5 0 0 1-1.5-1.5V11"
                  stroke="currentColor" stroke-width="1.5" stroke-linecap="round" />
            <path
              d="M8.5 3H4A1.5 1.5 0 0 0 2.5 4.5v4A1.5 1.5 0 0 0 4 10h4.5A1.5 1.5 0 0 0 10 8.5v-4A1.5 1.5 0 0 0 8.5 3Z"
              stroke="currentColor" stroke-width="1.5" />
          </svg>
          复制APC链接
        </div>

        <div class="menu-divider"></div>

        <div class="menu-item" @click="handleDelete">
          <img src="@/assets/img/delete.png" :width="14" alt="">
          <span style="margin-left: 5px">删除</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import FilePutMixin from '@/mixins/filePut'
import FilePreview from '@/pages/components/file-preview/index.vue'
import FilePreviewFooter from './filePreviewFooter.vue'
import { deleteAsset, downloadAsset, getAssetDetail } from '@/api/muse'
import Tooltip from '@/components/basic/tooltip/index.vue'
import copy from 'copy-to-clipboard'
import dayjs from 'dayjs'

export default {
  mixins: [FilePutMixin],
  components: {
    FilePreview,
    FilePreviewFooter,
    Tooltip
  },
  props: {
    item: {
      type: Object,
      default() {
        return {}
      }
    },
    scale: {
      type: Number,
      default: 30
    },
    isSearch: {
      type: Boolean,
      default: false
    },
    repositoryCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      windowHeight: window.innerHeight,
      windowWidth: window.innerWidth,
      overflowRight: false,
      overflowTop: false,
      loading: false,
      moreMenuVisible: false,
      menuHideTimer: null,
      isHoverMore: false,
      handleResize: null
    }
  },
  computed: {
    processNum() {
      return this.$bus.downloadFiles[this.item.id] || 0
    },
    loadingView() {
      return Object.prototype.hasOwnProperty.call(
        this.$bus.downloadFiles,
        this.item.id
      )
    },
    dataList() {
      return this.$bus.museAssetsList
    },
    searchList() {
      return this.$bus.museSearchList
    },
    list() {
      if (this.isSearch) return this.searchList
      return this.dataList
    }
  },
  created() {
    this.handleResize = () => {
      this.windowWidth = window.innerWidth
      this.windowHeight = window.innerHeight
      this.overflowTop = false
      this.overflowRight = false
    }
    window.addEventListener('resize', this.handleResize)
  },

  beforeDestroy() {
    // 清理定时器
    if (this.menuHideTimer) {
      clearTimeout(this.menuHideTimer)
    }
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    dayjs,
    handleHover() {
      // 安全检查：确保refs存在且已挂载
      if (!this.$refs.ImgViewer || !this.$refs.ImgViewer.$el || !this.$refs.thumbnail) {
        console.warn('handleHover: refs not ready yet')
        return
      }

      try {
        const { width: prewW, height: prewH } = this.$refs.ImgViewer.$el.getBoundingClientRect()
        const { top: boxT, left: boxL } = this.$refs.thumbnail.getBoundingClientRect()
        this.overflowRight = boxL + prewW > this.windowWidth - 10 // 10为滚轮宽度
        this.overflowTop = boxT - 125 > prewH // 125为导航高度
      } catch (error) {
        console.warn('handleHover error:', error)
      }
    },

    handleItemClick(item) {
      this.$bus.setMuseAssetDetail(item)
      this.$CustomDialog({
        width: 'calc(100% - 80px)',
        style: {
          height: 'calc(100% - 160px)',
          margin: '0 0 16px'
        },
        custom: true,
        contRender: (h, handleClose) => {
          return h(FilePreview, {
            props: {
              item: {
                ...item,
                cover: item.thumbnailUrl
              }
            },
            on: {
              turnLast: (id) => {
                const index = this.list.findIndex(i => i.id === id)
                if (index < 0) {
                  this.$CustomToast({
                    type: 'error',
                    duration: 2,
                    content: '网络出问题了，请刷新页面'
                  })
                  return
                }
                if (index === 0) {
                  this.$CustomToast({
                    type: 'warning',
                    duration: 2,
                    content: '已经是第一个了'
                  })
                  return
                }
                if (index > 0) {
                  this.$CustomToast({
                    type: 'success',
                    duration: 2,
                    content: '加载中，请稍后'
                  })

                  const newItem = this.list[index - 1]
                  handleClose()
                  this.handleItemClick(newItem)
                }
              },
              turnNext: (id) => {
                const index = this.list.findIndex(i => i.id === id)
                if (index < 0) {
                  this.$CustomToast({
                    type: 'error',
                    duration: 2,
                    content: '网络出问题了，请刷新页面'
                  })
                  return
                }

                if (index === this.list.length - 1) {
                  this.$CustomToast({
                    type: 'warning',
                    duration: 2,
                    content: '已经是最后一个了'
                  })
                  return
                }

                if (index >= 0) {
                  this.$CustomToast({
                    type: 'success',
                    duration: 2,
                    content: '加载中，请稍后'
                  })
                  const newItem = this.list[index + 1]
                  handleClose()
                  this.handleItemClick(newItem)
                }
              }
            },
            scopedSlots: {
              footer: () => h(FilePreviewFooter, {
                props: {
                  item: {
                    ...item,
                    cover: item.thumbnailUrl
                  }
                }
              })
            }
          })
        }
      })
    },
    getImg(item) {
      const allExt = [
        'AI',
        'EPS',
        'GIF',
        'JPG',
        'MP4',
        'PNG',
        'PSB',
        'PSD',
        'SVG',
        'WEBP'
      ]
      const ext = item.first_file_ext?.toUpperCase()
      if (!allExt.includes(ext)) return require('@/assets/img/format/JPG.png')
      return require(`@/assets/img/format/${ext}.png`)
    },
    async handlePut(item) {
      const { code, data } = await downloadAsset([
        {
          ref_id: item.id,
          asset_type: item.asset_type,
          organization: item.organization,
          reason: 'Learn'
        }
      ])
      if (code) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: `下载失败，${data}`
        })
        return
      }
      const fileName = data.downloads[0].files[0].name
      const url = data.downloads[0].files[0].url
      try {
        this.handelStartPut(item, url, false, fileName, false)  // mixin方法
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: `下载置入失败，${e.message}`
        })
      }
    },
    async handleCopyApcLink(item) {
      try {
        // 生成APC链接
        const { data: apcLink } = await getAssetDetail({
          type: 'asset_detail',
          organization: this.repositoryCode,
          assetType: 'original',
          assetId: item.id
        })
        copy(apcLink)
        // 复制到剪贴板
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: 'APC链接已复制到剪贴板'
        })
      } catch (error) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: `复制失败: ${error.message}`
        })
      }
    },
    showMoreMenu() {
      this.isHoverMore = true
      // 清除隐藏定时器
      if (this.menuHideTimer) {
        clearTimeout(this.menuHideTimer)
        this.menuHideTimer = null
      }
      this.moreMenuVisible = true
    },

    hideMoreMenu() {
      // 延迟隐藏，给用户时间移动到菜单上
      this.menuHideTimer = setTimeout(() => {
        this.moreMenuVisible = false
        this.isHoverMore = false
      }, 150)
    },
    handleDelete() {
      this.moreMenuVisible = false
      // 显示确认删除弹窗
      this.$CustomDialog({
        title: '确认删除',
        content: `确定要删除素材 "${this.item.name}" 吗？删除后无法恢复。`,
        btnText: '确认删除',
        closabled: true,
        width: 320,
        onBtnClick: async () => {
          try {
            await deleteAsset({
              id: this.item.id,
              organization: this.repositoryCode
            })
            this.$CustomToast({
              type: 'success',
              duration: 2,
              content: '素材删除成功'
            })
            // 触发父组件刷新列表
            this.$emit('deleted', this.item.id)
            this.$CustomDialogRemove()
          } catch (error) {
            this.$CustomToast({
              type: 'error',
              duration: 2,
              content: `删除失败: ${error.message}`
            })
          }
        }
      })
    },
    handleCopyApcLinkFromMenu() {
      this.moreMenuVisible = false
      this.handleCopyApcLink(this.item)
    }
  }
}
</script>

<style lang="less" scoped>
.thumbnail {
  position: relative;
  width: 100%;
  height: 100%;

  &:hover {
    .preview-image {
      opacity: 1;
      visibility: visible;
    }

    .u-options {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .cancel-text {
      background-color: red !important;
      color: #fff !important;
      z-index: 9;
    }
  }

  .u-tag {
    position: absolute;
    left: 4px;
    top: 4px;
    z-index: 1;
  }

  .u-options {
    display: none;
    position: absolute;
    width: 100%;
    height: 20px;
    bottom: 0;
    color: #fff;
    background: #1473e6;
    z-index: 2;
    border-radius: 0 0 4px 4px;

    &:not(.mode) {
      // 小尺寸模式的按钮布局
      align-items: center;

      .option-btn {
        flex: 1;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(255, 255, 255, 0.1);
        }

        &.download-btn {
          font-size: 12px;
          border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        &.copy-btn {
          width: 28px;
          flex: none;
          padding: 6px;

          svg {
            width: 100%;
            height: 100%;
            opacity: 0.9;
            display: block;
          }

          &:hover svg {
            opacity: 1;
          }
        }
      }
    }
  }

  .mode {
    display: none;
    justify-content: space-between !important;
    height: 45px;
    padding: 7px;
    cursor: default !important;
    background: rgba(0, 0, 0, 0.6);

    .imgInfo {
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      align-items: flex-start;
      align-content: flex-start;

      .name {
        width: 200px;
        flex: 0.8;
        font-size: 12px;
        font-weight: 600;
        text-align: start;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #FFFFFF;
      }

      .size {
        font-size: 11px;
        color: #868686;
      }
    }

    .action-buttons {
      display: flex;
      gap: 6px;
      align-items: center;

      .actionBtn {
        height: 24px;
        border-radius: 4px;
        cursor: pointer;
        border: 1px solid #0E56AD;
        background-color: #1473e6;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-color: #0E56AD;
          transform: translateY(-1px);
        }

        &.copyBtn {
          width: 28px;
          padding: 3px;
          margin-left: 5px;

          svg {
            width: 100%;
            height: 100%;
            opacity: 0.9;
            display: block;
          }

          &:hover svg {
            opacity: 1;
          }
        }

        &.downloadBtn {
          width: 40px;
          font-size: 12px;
          text-align: center;
          line-height: 24px;
        }
      }
    }
  }

  .u-options-loading {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 20px;
    padding: 0 4px;
    color: #333;
    line-height: 20px;
    z-index: 2;
    border-radius: 0 0 4px 4px;
    //overflow: hidden;

    .progress {
      position: absolute;
      left: 0;
      width: 100%;
      height: 100%;
      background: #ffffff !important;

      .u-options-progress {
        position: absolute;
        left: 0;
        bottom: 0;
        height: 20px;
        background: #1473e6;
      }

      .u-options-text {
        position: absolute;
        right: 4px;
        bottom: 0;
        height: 20px;
        color: #333;
      }
    }

    .cancel-text {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 20%;
      height: 20px;
      text-align: center;
      cursor: pointer;
      z-index: 3;
      color: red;
      background-color: transparent;

      &:hover {
        font-weight: bold;
      }
    }
  }

  .spectrum-Thumbnail {
    width: 100%;
    height: 100%;
    background-color: #5a5a5a;
    background-image: none;
    border-radius: 4px;
    padding: 14px;

    &::before {
      box-shadow: none;
    }

    &-image {
      object-fit: cover;
      object-position: center;
      z-index: 0;
    }
  }

  .preview-image {
    display: block;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
    position: absolute;
    z-index: 99999;
    border-radius: 5px;
    background-color: #5a5a5a;
    overflow: hidden;
    border: 1px solid #636363;
    box-shadow: 4px 4px 16px #00000060;
    will-change: opacity, transform;
    transform: translateZ(0);
    backface-visibility: hidden;

    &--left {
      left: 0;
    }

    &--right {
      right: 0;
    }

    &--top {
      bottom: 100%;
      margin-bottom: 4px;
    }

    &--bottom {
      top: 100%;
      margin-top: 4px;
    }

    &--overflow {
      height: auto;
    }

    .detail-info {
      background: #4d4d4d;
      width: 100%;
      padding: 7px;
      color: #868686;
      font-size: 12px;

      .name {
        font-weight: bold;
        word-break: break-all;
        color: #fff;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}

// More菜单样式
.more-menu-container {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;

  .more-button {
    width: 28px;
    height: 28px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    

    &:hover {
      background: rgba(0, 0, 0, 0.8);
      transform: scale(1.1);
    }

    .more-icon {
      width: 16px;
      height: 16px;
      color: #fff;
    }
  }

  .more-menu {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 4px;
    background: #2a2a2a;
    border: 1px solid #444;
    border-radius: 8px;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    padding: 4px 0;
    min-width: 140px;
    z-index: 9999;
    font-size: 13px;
    animation: menuFadeIn 0.15s ease-out;
    will-change: opacity, transform;
    transform: translateZ(0);

    .menu-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      color: #fff;
      cursor: pointer;
      transition: background-color 0.2s ease;

      &:hover {
        background-color: rgba(74, 144, 226, 0.2);
      }

      .menu-icon {
        width: 14px;
        height: 14px;
        color: #ccc;
        flex-shrink: 0;
      }
    }

    .menu-divider {
      height: 1px;
      background: #444;
      margin: 4px 0;
    }
  }
}

// 只有在hover卡片时才显示more按钮
.thumbnail:hover .more-menu-container .more-button {
  opacity: 1;
}

@keyframes menuFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
