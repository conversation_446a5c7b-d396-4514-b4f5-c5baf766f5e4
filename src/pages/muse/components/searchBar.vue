<template>
  <div class="searchBox">
    <div class="searchInput">
      <div :class="{'spectrum-Textfield--quiet': false}" class="u-search spectrum-Textfield" style="width:100%;">
        <span
          aria-hidden="true"
          class="spectrum-Icon spectrum-Icon--sizeM spectrum-Textfield-icon"
          focusable="false"
          style="z-index: 10; cursor: pointer;"
        >
          <IconFont :size="14" icon="search" style="color: white" />
        </span>
        <input
          v-model="keyword"
          :class="{'spectrum-SearchWithin-input': false}"
          autocomplete="off"
          class="spectrum-Textfield-input spectrum-Search-input input"
          name="search"
          placeholder="搜索你想要的素材"
          type="search"
          @keyup.enter="search"
        >
      </div>
      <button
        v-show="keyword"
        class="spectrum-ClearButton spectrum-Search-clearButton"
        style="z-index: 31;margin-right: 6px"
        type="reset"
        @click="keyword=''"
      >
        <IconFont :size="16" icon="empty" style="color: white" />
      </button>
    </div>
    <GButton
      class="searchBtn"
      style="color: #fff"
      ghost
      @click="search"
    >
      搜索素材
    </GButton>

    <div class="buttons">
      <template v-if="showUploadBtn">
        <template v-if="uploadPermission">
          <GButton
            :disabled="!uploadPermission"
            class="searchBtn"
            type="primary"
            @click="$emit('onSave')"
          >
            <span class="spectrum-ActionButton-label">保存上传(apc关单)</span>
            <div class="line">&nbsp;</div>
            <span
              class="down-arrow-icon "
              @mouseenter="handleOnHover(true)"
              @mouseleave="handleOnLeave"
              @click.stop="() => {}"
            >
          <IconFont icon="down-arrow" size="18" class="f-8" />
        </span>
          </GButton>
        </template>

        <template v-else>
          <Tooltip placement="top-end">
            <template #content>
              <div style="padding: 8px 12px">暂无上传权限</div>
            </template>
            <GButton
              :disabled="!uploadPermission"
              class="searchBtn"
              type="primary"
              @click="$emit('onSave')"
            >
              <span class="spectrum-ActionButton-label">保存上传(apc关单)</span>
              <div class="line">&nbsp;</div>
              <span
                class="down-arrow-icon "
                @mouseenter="handleOnHover(true)"
                @mouseleave="handleOnLeave"
                @click.stop="() => {}"
              >
                <IconFont icon="down-arrow" size="18" class="f-8" />
                </span>
            </GButton>
          </Tooltip>
        </template>
      </template>

      <div
        v-if="iconHover"
        @mouseenter="handleOnHover(true)"
        @mouseleave="handleOnLeave"
        style="width: 220px;position: absolute;top: 20px;right: 10px;padding-top: 10px;background-color: transparent;"
      >
        <context-menu
          :show="true"
          :position="{ x: 0, y: 25 }"
          :menuBtns="[
            {
              title: '本地上传(apc关单)',
              eventName: 'upload',
              className: 'upload-btn'
            },
          ]"
          @click="$emit('onUpload')"
          @close="iconHover = false"
          style="width: 100%;position: absolute;text-align: center;background: #999"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MuseSearchBar',
  props: {
    showUploadBtn: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      keyword: this.$bus.museKeyword,
      iconHover: false
    }
  },
  computed: {
    uploadPermission() {
      const savedState = this.$bus.getMuseSiderState()
      const curRepositoryId = savedState.repositoryId
      const list = this.$bus.museRepositoryList
      const curRepository = list.find(item => item.id === curRepositoryId)
      return !!curRepository?.permit?.write
    }
  },
  methods: {
    search() {
      this.$bus.setMuseKeyword(this.keyword)
      this.$router.push({
        name: 'museSearch',
        params: {
          repositoryCode: this.repositoryCode
        }
      })
    },
    handleOnHover(bool) {
      if (bool && this.timeout) {
        clearTimeout(this.timeout)
      }
      this.iconHover = bool
    },
    handleOnLeave() {
      this.timeout = setTimeout(() => this.handleOnHover(false), 500)
    }
  }
}
</script>

<style lang="less" scoped>
.searchBox {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;

  .searchInput {
    flex: 1;
    height: 32px;
    position: relative;

    .input {
      border-radius: 4px;
      border: 1px solid #636363;
      background-color: #474747;
    }
  }

  .searchBtn {
    margin-left: 8px;
  }
}

.buttons {
  width: fit-content;
  margin-left: 10px;

  .line {
    width: 1px;
    height: 60%;
    margin: 0 8px;
    border-radius: 10px;
    background-color: #ffffff46;
  }

  .down-arrow-icon {
    display: inline-block;
    width: fit-content;
  }
}

:deep(.upload-btn) {
  padding: 4px;
  border: 1px solid #666;
  border-radius: 4px;
  cursor: pointer;
  background: #aaa;
}
</style>