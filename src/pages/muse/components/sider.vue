<template>
  <div :style="{ width: width + 'px' }" class="container">
    <div class="selectContainer">
      <multi-select
        v-model="repositoryId"
        :isSearchWith="true"
        :options="repositoryList"
        class="select"
        hasValue
        label="所属项目"
        style="width: 100%"
      />
    </div>

    <el-tree
      :data="folderTree"
      :default-expand-all="true"
      :expand-on-click-node="false"
      :indent="3"
      :props="{
        children: 'children',
        label: 'name',
      }"
      class="tree"
      @node-click="handleNodeClick"
    >
      <template #default="{ data }">
        <div class="tree-node">
          <div :class="{ 'active': curNodeId === data.id }" class="node-content">
            <img v-if="curNodeId === data.id" :height="15" :width="15" alt="" src="@/assets/img/group-active.png" />
            <img v-else :height="15" :width="15" alt="" src="@/assets/img/group.png" />
            <span class="node-text">{{ data.name }}</span>
          </div>
        </div>
      </template>
    </el-tree>

    <!-- 添加拖拽条 -->
    <div
      class="resizer"
      @mousedown="startResize"
    >
      <img alt="" src="@/assets/img/drag.png" />
    </div>
  </div>
</template>

<script>
import { getFolderTree, getMuseRepositories } from '@/api/muse'

export default {
  name: 'MuseSider',
  components: {},
  data() {
    return {
      width: 230, // 初始宽度
      repositoryId: '',
      repositoryCode: '',
      curNodeId: '',
      repositoryList: [],
      data: [],
      folderTree: []
    }
  },
  watch: {
    repositoryId(newValue, oldValue) {
      const code = this.repositoryList.find(item => item.id === newValue)?.code
      if (!code) return
      this.repositoryCode = code

      // 只有在用户主动切换仓库时才重置节点选择
      // 如果是初始化恢复状态，不重置节点选择
      const isUserAction = oldValue && oldValue !== newValue

      if (isUserAction) {
        // 用户主动切换仓库，重置节点选择
        this.$bus.setMuseSiderState({
          repositoryId: newValue,
          repositoryCode: code,
          curNodeId: '', // 重置节点选择
          curNode: null
        })
      }

      this.getFolderList(code)
    }
  },
  mounted() {
    this.initSiderState()
    // 添加键盘事件监听，Ctrl+Shift+R 清除缓存并刷新
    this.addKeyboardListener()
  },
  beforeDestroy() {
    this.removeKeyboardListener()
  },
  methods: {
    async initSiderState() {
      await this.getRepositoryList()
      await this.restoreStateFromCache()
    },
    async getRepositoryList() {
      // 缓存中没有数据，从API获取
      try {
        const { data } = await getMuseRepositories()
        if (data?.length) {
          // 按照name字段进行排序
          const sortedData = [...data].sort((a, b) => {
            const nameA = (a.name || '').toLowerCase()
            const nameB = (b.name || '').toLowerCase()
            return nameA.localeCompare(nameB, 'zh-CN')
          })

          this.repositoryList = sortedData

          // 将排序后的项目列表存储到缓存和$bus中
          this.$bus.setMuseRepositoryListCache(sortedData)
          console.log('从API获取项目列表并缓存（已排序）:', sortedData)

          return sortedData
        }
      } catch (error) {
        console.error('获取项目列表失败:', error)
        throw error
      }
      return []
    },
    async restoreStateFromCache() {
      const savedState = this.$bus.getMuseSiderState()

      if (savedState.repositoryId && this.repositoryList.length > 0) {
        // 检查保存的仓库ID是否仍然存在
        const savedRepo = this.repositoryList.find(item => item.id === savedState.repositoryId)
        if (savedRepo) {
          // 恢复保存的状态
          this.repositoryId = savedState.repositoryId
          this.repositoryCode = savedState.repositoryCode
          console.log('从缓存恢复 sider 状态:', savedState)

          // 重要：需要先加载文件夹树，然后才能恢复节点选择
          // 这里不直接设置curNodeId，而是让getFolderList来处理节点恢复
          return
        }
      }

      // 如果没有有效的缓存状态，则使用默认值（第一个仓库）
      if (this.repositoryList.length > 0) {
        this.repositoryId = this.repositoryList[0]?.id
        this.repositoryCode = this.repositoryList[0]?.code
        console.log('使用默认 sider 状态')
      }
    },
    async getFolderList(repositoryCode) {
      // 先尝试从缓存获取文件夹树
      const cachedTree = this.$bus.getMuseFolderTreeCache(repositoryCode)
      let originalTree = null

      if (cachedTree) {
        originalTree = cachedTree
        console.log('从缓存获取文件夹树:', repositoryCode, cachedTree)
      } else {
        // 缓存中没有数据，从API获取
        try {
          const { data } = await getFolderTree(repositoryCode)
          originalTree = data?.[repositoryCode]

          // 将文件夹树存储到缓存
          if (originalTree) {
            this.$bus.setMuseFolderTreeCache(repositoryCode, originalTree)
            console.log('从API获取文件夹树并缓存:', repositoryCode, originalTree)
          }
        } catch (error) {
          console.error('获取文件夹树失败:', error)
          // 如果API失败但有缓存，使用缓存数据
          if (cachedTree) {
            originalTree = cachedTree
            console.log('API失败，使用缓存文件夹树:', repositoryCode, cachedTree)
          } else {
            throw error
          }
        }
      }

      // 创建根节点，并将原始树作为子节点
      const rootNode = {
        id: null, // 根节点ID为null，API调用时会被filter(Boolean)过滤掉
        name: '全部素材',
        children: originalTree || [],
        isRoot: true // 添加标识符用于缓存识别
      }

      this.folderTree = [rootNode]

      // 尝试恢复之前选中的节点
      const savedState = this.$bus.getMuseSiderState()
      let nodeToSelect = rootNode

      console.log('尝试恢复节点选择:', {
        savedNodeId: savedState.curNodeId,
        savedRepositoryCode: savedState.repositoryCode,
        currentRepositoryCode: repositoryCode,
        hasNode: !!savedState.curNode
      })

      if (savedState.repositoryCode === repositoryCode) {
        // 检查是否是根节点
        if (savedState.curNode && savedState.curNode.isRoot) {
          nodeToSelect = rootNode
          console.log('✅ 恢复选中根节点')
        } else if (savedState.curNodeId && savedState.curNode) {
          // 在树中查找保存的节点
          const findNode = (nodes, targetId) => {
            for (const node of nodes) {
              if (node.id === targetId) {
                return node
              }
              if (node.children && node.children.length > 0) {
                const found = findNode(node.children, targetId)
                if (found) return found
              }
            }
            return null
          }

          const foundNode = findNode(this.folderTree, savedState.curNodeId)
          if (foundNode) {
            nodeToSelect = foundNode
            console.log('✅ 成功恢复选中的节点:', foundNode)
          } else {
            console.log('❌ 未找到保存的节点，使用根节点。查找的ID:', savedState.curNodeId)
          }
        } else {
          console.log('❌ 不满足节点恢复条件，使用根节点')
        }
      } else {
        console.log('❌ 仓库代码不匹配，使用根节点')
      }

      // 选中节点（根节点或恢复的节点）
      this.selectNode(nodeToSelect)
      this.$forceUpdate()

      // 延迟触发事件，确保主页面已经准备好
      this.$nextTick(() => {
        console.log('🚀 初始化完成，触发节点选择事件:', nodeToSelect)
        this.$emit('onNodeClick', { node: nodeToSelect, repositoryCode: this.repositoryCode })
      })
    },
    // 选择节点但不触发事件（用于初始化恢复状态）
    selectNode(node) {
      this.curNodeId = node.id

      // 保存当前状态到缓存
      const stateToSave = {
        repositoryId: this.repositoryId,
        repositoryCode: this.repositoryCode,
        curNodeId: node.id,
        curNode: node
      }

      this.$bus.setMuseSiderState(stateToSave)
      console.log('💾 选择节点并保存状态:', stateToSave)
    },

    // 处理用户点击节点（会触发事件）
    handleNodeClick(node) {
      this.selectNode(node)
      console.log('👆 用户点击节点，触发事件:', node)
      this.$emit('onNodeClick', { node, repositoryCode: this.repositoryCode })
    },

    startResize(e) {
      e.preventDefault()
      const startX = e.clientX
      const startWidth = this.width
      const doDrag = (e) => {
        const newWidth = startWidth + e.clientX - startX
        // 限制最小宽度和最大宽度
        this.width = Math.min(Math.max(newWidth, 180), 400)
      }
      const stopDrag = () => {
        document.removeEventListener('mousemove', doDrag)
        document.removeEventListener('mouseup', stopDrag)
      }
      document.addEventListener('mousemove', doDrag)
      document.addEventListener('mouseup', stopDrag)
    },

    // 添加键盘事件监听
    addKeyboardListener() {
      this.keyboardHandler = (e) => {
        // Ctrl+Shift+R 清除缓存并刷新
        if (e.ctrlKey && e.shiftKey && e.key === 'R') {
          e.preventDefault()
          this.clearCacheAndRefresh()
        }
      }
      document.addEventListener('keydown', this.keyboardHandler)
    },

    // 移除键盘事件监听
    removeKeyboardListener() {
      if (this.keyboardHandler) {
        document.removeEventListener('keydown', this.keyboardHandler)
      }
    },

    // 清除缓存并刷新数据
    async clearCacheAndRefresh() {
      try {
        console.log('清除 muse 缓存并刷新数据...')

        // 清除所有缓存
        this.$bus.clearMuseCacheData()
        this.$bus.clearMuseSiderState()

        // 重置组件状态
        this.repositoryId = ''
        this.repositoryCode = ''
        this.curNodeId = ''
        this.repositoryList = []
        this.folderTree = []

        // 重新初始化
        await this.initSiderState()

        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '缓存已清除，数据已刷新'
        })
      } catch (error) {
        console.error('清除缓存失败:', error)
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '清除缓存失败: ' + error.message
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  min-width: 100px;
  max-width: 400px;
  border-right: 1px solid #3a3a3a;

  .selectContainer {
    width: 100%;
    margin: 8px 0;
    padding: 0 6px;

    .select {
      width: 100%;
      height: 32px;
      border: 1px solid #636363;
      background-color: #505050;
      border-radius: 2px;

      &:hover {
        border-color: #7a7a7a;
      }
    }
  }

  .tree {
    width: 100%;
    margin-top: 8px;
    color: #c7c7c7;
    background: transparent;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background-color: #555;
      border-radius: 3px;
    }

    .tree-node {
      display: flex;
      align-items: center;
      width: 100%;

      .expand-icon {
        padding: 0 4px;
        cursor: pointer;
      }

      .node-content {
        display: flex;
        align-items: center;
        flex: 1;
        padding: 6px;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: rgba(102, 102, 102, 0.5);
        }

        &.active {
          background-color: #666;
        }

        .node-text {
          margin-left: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }

    :deep(.el-tree-node) {
      border-left: 1px solid #666;
      padding-left: 8px;
    }

    :deep(.el-tree-node__content) {
      background-color: transparent;
      padding: 0;
      min-height: 26px;
    }

    :deep(.el-tree-node__expand-icon) {
      color: #999;
      font-size: 12px;
      transition: transform 0.3s;
      padding: 0;

      &.expanded {
        transform: rotate(90deg);
      }

      &.is-leaf {
        color: transparent;
      }
    }
  }

  .resizer {
    position: absolute;
    right: -3px;
    top: 0;
    width: 3px;
    height: 100%;
    cursor: col-resize;
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: transparent;
    transition: background-color 0.2s;

    img {
      position: absolute;
      width: 25px;
      padding: 2px;
      background-color: #444;
      border-radius: 5px;
      transition: all 0.2s;
    }

    &:hover {
      background-color: #1373e6;

      img {
        opacity: 1;
        background-color: #1373e6;
      }
    }

    &:active {
      background-color: #1373e6;

      img {
        opacity: 1;
        background-color: #1373e6;
      }
    }
  }
}
</style>
