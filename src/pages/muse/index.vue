<template>
  <MuseLayout>
    <template #sider>
      <MuseSider @onNodeClick="onNodeClick" />
    </template>

    <template #searchBar>
      <MuseSearchBar
        showUploadBtn
        @onUpload="() => {
          mode='upload'
          uploadModalVisible=true
        }"
        @onSave="() => {
          mode='save'
          uploadModalVisible=true
        }"
      />
    </template>

    <template #barLeft>
      <div>{{ pageInfo.loading ? '搜索中...' : `${pageInfo.total}个搜索结果` }}</div>
    </template>

    <template #barRight>
      <Tooltip placement="bottom-start" theme="dark">
        <div class="scale">
          <span>图像大小</span>
          <img :width="11" alt src="@/assets/img/scale.png" />
        </div>

        <div slot="content" class="scale-box">
          <IconFont :size="10" class="reduce" icon="reduce" @click="scale -= 5" />
          <Slider v-model="scale" class="slider-scale" />
          <IconFont :size="10" class="add" icon="add" @click="scale += 5" />
        </div>
      </Tooltip>
    </template>

    <template #default>
      <scroll-page
        ref="muse-scroll-page"
        :finished="pageInfo.finished"
        :list="dataList"
        :loading="pageInfo.loading"
        style="padding-top: 0"
        @scroll-bottom="handleBottom"
      >
        <template v-slot:list="{ list }">
          <Waterfall :dataList="list" :scale="scale">
            <template v-slot:default="{ item }">
              <MaterialCard
                :key="item.id"
                :item="item"
                :repositoryCode="repositoryCode"
                :scale="scale"
                @deleted="onDeleted" />
            </template>
          </Waterfall>
        </template>
      </scroll-page>

      <UploadModal
        :mode="mode"
        :visible="uploadModalVisible"
        :curNode="curNode"
        :repositoryCode="repositoryCode"
        :autoOpenFilePicker="mode === 'upload'"
        @changeVisible="changeVisible"
        @uploadSuccess="handleUploadSuccess"
      />
    </template>
  </MuseLayout>
</template>

<script>
import MuseLayout from '@/components/layouts/muse-layout/index.vue'
import MuseSider from './components/sider.vue'
import MuseSearchBar from './components/searchBar.vue'
import MaterialCard from './components/material-card.vue'
import Waterfall from '@/components/waterfall/index.vue'
import { getAssetList } from '@/api/muse'
import FilePutMixin from '@/mixins/filePut'
import UploadModal from './components/upload-modal.vue'

export default {
  name: 'Muse',
  mixins: [FilePutMixin],
  components: {
    MuseLayout,
    MuseSider,
    MuseSearchBar,
    MaterialCard,
    Waterfall,
    UploadModal
  },
  data() {
    return {
      curNode: null,
      repositoryCode: null,
      scale: 30,
      pageInfo: {
        loading: false,
        finished: false,
        total: 0,
        limit: 200,
        sort: 'DEFAULT',
        page_num: 1,
        page_size: 200
      },
      dataList: [],
      uploadModalVisible: false,
      mode: 'save' // save保存 upload自主上传
    }
  },
  computed: {
    keyword() {
      return this.$bus.museKeyword
    }
  },
  watch: {
    '$route.meta.scrollTop'(val) {
      if (val) {
        this.$refs['muse-scroll-page'].setScrollTop(val)
      }
    },
  },
  mounted() {
    this.$bus.showMuseNews = false
    this.$bus.setMuseKeyword('')
    window.localStorage.setItem('showMuseNews', '')
  },
  methods: {
    initPageInfo() {
      this.pageInfo.loading = false
      this.pageInfo.finished = false
      this.pageInfo.total = 0
      this.pageInfo.sort = 'DEFAULT'
      this.pageInfo.page_num = 1
      this.dataList = []
    },
    onNodeClick(data) {
      this.curNode = data.node
      this.repositoryCode = data.repositoryCode
      this.initPageInfo()
      this.getDataList()
    },
    async getDataList() {
      try {
        this.pageInfo.loading = true
        const { data } = await getAssetList(this.repositoryCode, {
          categories: [this.curNode.id].filter(Boolean),
          page_num: this.pageInfo.page_num,
          page_size: this.pageInfo.page_size,
          sort_by: 'time'
        })
        const formatList = data?.records?.map(i => ({
          ...i,
          thumbnailUrl: i.cover
        })) || []
        this.dataList = [...this.dataList, ...formatList]
        this.$bus.setMuseAssetsList([...this.dataList, ...formatList])
        this.pageInfo.total = data?.real_total_count || 0
        this.pageInfo.finished = formatList < this.pageInfo.page_size
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: e.message
        })
      } finally {
        this.pageInfo.loading = false
      }
    },
    async handleBottom() {
      if (this.pageInfo.finished) return
      this.pageInfo.page_num++
      await this.getDataList()
    },
    changeVisible(visible) {
      this.uploadModalVisible = visible
    },

    handleUploadSuccess(uploadData) {
      console.log('文件上传成功:', uploadData)
      // 这里可以添加上传成功后的处理逻辑
      // 比如刷新列表、显示成功提示等

      // 刷新当前页面数据
      this.pageInfo.page_num = 1
      this.dataList = []
      this.getDataList()
    },
    onDeleted(id) {
      this.dataList = this.dataList.filter(i => i.id !== id)
    }
  },
  beforeRouteLeave(to, from, next) {
    from.meta.scrollTop = this.$refs['muse-scroll-page'].getScrollTop()
    next()
  }
}
</script>

<style lang="less" scoped>
.scale {
  display: flex;
  align-items: center;
  margin: 0 12px;
  cursor: pointer;
}

:deep(.tooltip-popper) {
  transform: translateX(-50px);
}

:deep(.tooltip-inner) {
  padding: 0;
}

.scale-box {
  display: flex;
  align-items: center;
  padding: 6px;

  .slider-scale {
    width: 144px;
    margin: 0 8px;
  }

  .reduce,
  .add {
    cursor: pointer;

    &:hover {
      color: #1c7aec;
    }
  }
}
</style>