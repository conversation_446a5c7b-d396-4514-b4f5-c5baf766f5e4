<template>
  <MuseLayout>
    <template #searchBar>
      <MuseSearchBar :keyword="keyword" @onKeywordClear="onKeywordClear" @onSearch="search" />
    </template>

    <template #barLeft>
      <img
        :width="5"
        alt=""
        src="@/assets/img/arrow-left.png"
        style="margin-right: 9px; cursor: pointer;"
        @click="back"
      />
      <div>{{ pageInfo.loading ? '搜索中...' : `${pageInfo.total}个搜索结果` }}</div>
    </template>

    <template #barRight>
      <Tooltip placement="bottom-start" theme="dark">
        <div class="scale">
          <span>图像大小</span>
          <img :width="11" alt src="@/assets/img/scale.png" />
        </div>

        <div slot="content" class="scale-box">
          <IconFont :size="10" class="reduce" icon="reduce" @click="scale -= 5" />
          <Slider v-model="scale" class="slider-scale" />
          <IconFont :size="10" class="add" icon="add" @click="scale += 5" />
        </div>
      </Tooltip>
    </template>

    <template #default>
      <scroll-page
        ref="muse-scroll-page"
        :finished="pageInfo.finished"
        :list="dataList"
        :loading="pageInfo.loading"
        style="padding-top: 0"
        @scroll-bottom="handleBottom"
      >
        <template v-slot:list="{ list }">
          <Waterfall :dataList="list" :scale="scale">
            <template v-slot:default="{ item }">
              <MaterialCard :item="item" :repositoryCode="repositoryCode" :scale="scale" isSearch />
            </template>
          </Waterfall>
        </template>
      </scroll-page>
    </template>
  </MuseLayout>
</template>

<script>
import MuseLayout from '@/components/layouts/muse-layout/index.vue'
import MuseSearchBar from '@/pages/muse/components/searchBar.vue'
import Waterfall from '@/components/waterfall/index.vue'
import MaterialCard from '@/pages/muse/components/material-card.vue'
import { MUSE_ASSET, searchAggs, searchAssets } from '@/api/muse'

export default {
  name: 'MuseSearch',
  components: { MaterialCard, Waterfall, MuseSearchBar, MuseLayout },
  data() {
    return {
      scale: 20,
      pageInfo: {
        loading: false,
        finished: false,
        total: 0,
        limit: 200,
        sort: 'DEFAULT',
        page_num: 1,
        page_size: 200
      },
      dataList: []
    }
  },
  computed: {
    keyword() {
      return this.$bus.museKeyword
    },
    repositoryCode() {
      return this.$route.params.repositoryCode
    }
  },
  watch: {
    keyword: {
      async handler() {
        this.initPageInfo()
        await this.getAssetList()
      },
      immediate: true
    }
  },
  methods: {
    initPageInfo() {
      this.pageInfo.loading = false
      this.pageInfo.finished = false
      this.pageInfo.total = 0
      this.pageInfo.sort = 'DEFAULT'
      this.pageInfo.pageNum = 1
      this.dataList = []
    },
    search(keyword) {
      this.$bus.setMuseKeyword(keyword)
    },
    back() {
      this.$bus.setMuseKeyword('')
      this.$router.replace({ name: 'muse' })
    },
    onKeywordClear() {
      this.$bus.setMuseKeyword('')
      this.$router.replace({ name: 'muse' })
    },
    async getAssetList() {
      try {
        this.pageInfo.loading = true
        const { data: counts } = await searchAggs({
          keyword: this.keyword,
          asset_type: MUSE_ASSET
        })
        if (!counts?.[MUSE_ASSET]) return
        const { data } = await searchAssets({
          keyword: this.keyword,
          asset_type: MUSE_ASSET,
          page_num: this.pageInfo.page_num,
          page_size: this.pageInfo.page_size
        })
        const formatList = data[MUSE_ASSET].records?.map(i => ({
          ...i,
          thumbnailUrl: i.cover
        })) || []
        this.dataList = [...this.dataList, ...formatList]
        this.$bus.setMuseSearchList([...this.dataList, ...formatList])
        this.pageInfo.total = data[MUSE_ASSET].real_total_count || 0
        this.pageInfo.finished = formatList.length < this.pageInfo.page_size
      } catch (error) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: error.message
        })
      } finally {
        this.pageInfo.loading = false
      }
    },
    handleBottom() {
      if (this.pageInfo.finished) return
      this.pageInfo.page_num++
      this.getAssetList()
    }
  }
}
</script>

<style lang="less" scoped>
</style>