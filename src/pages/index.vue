<template>
  <div class="page-index">
    <div class="wrapper">
      <img :src="logo_url" width="48" />
      <h2>{{ title }}</h2>
      <div>让设计更简单</div>
      <div>- v{{ version }} -</div>
      <br />
      <br />
      <GButton
        @click="handleLogin"
        type="info"
        shape="circle"
        ghost
        style="border-width: 2px;"
      >
        {{ isLoading ? '正在登录...' : 'OpenID 登录' }}
      </GButton>
    </div>
  </div>
</template>
<script>
// 注释
import { loginResult } from '@/api'
import { isPermission } from '@/bridge/api'
import { APP_ENV, VERSION, HOST } from '@/config'
import { myStorage } from '@/utils/index.js'
import * as uuid from 'uuid'
import TimerMixin from '@/mixins/timer'

export default {
  name: 'Index',
  mixins: [TimerMixin],
  data() {
    return {
      isShowCleartextPsw: false, // 是否展示明文密码
      loginForm: {
        account: '',
        pwd: ''
      },
      version: VERSION,
      logo_url:
        APP_ENV === 'game'
          ? require('@/assets/img/game/logo.png')
          : require('@/assets/img/logo.png'),
      title: APP_ENV === 'game' ? 'GUI资源平台' : '推广设计中心',
      isLoading: false
    }
  },
  methods: {
    async handleLogin() {
      if (this.isLoading) return
      this.$logMsg.info('开始登录', 'Login')
      try {
        this.isLoading = true
        const deviceId = uuid.v4()
        const url = `${HOST}/auth/login?deviceId=${deviceId}`
        // 打开外部网页进行登录
        this.$csInterface.openURLInDefaultBrowser(url)

        // 轮询登录结果
        this.createTimer(async (timerKey) => {
          try {
            const res = await loginResult({ deviceId })
            if (!res.token) {
              return
            }
            this.isLoading = false
            this.clearTimer(timerKey) // 清除轮询

            // token存储
            myStorage.set('token', res.token)

            // 用户信息存储
            await this.$bus.getUserInfo()

            // 客户端信息初始化
            await isPermission()

            // 跳转页面
            if (this.$route.name !== 'list') {
              await this.$router.push({
                name: 'list'
              })
            }
            this.$logMsg.info('登录成功', 'Login')
          } catch (error) {
            this.isLoading = false
            this.clearTimer(timerKey) // 清除轮询
            this.$CustomToast({
              type: 'error',
              duration: 2,
              content: error.message
            })
            this.$logMsg.error('[login] 登录失败:' + error.message)
          }
        })
      } catch (error) {
        this.isLoading = false
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: error.message
        })
        this.$logMsg.error('[login] 登录失败:' + error.message)
      }
    }
  }
}
</script>
