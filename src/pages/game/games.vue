<template>
  <GameLayout @filter="filter" @search="search">
    <template #barLeft>
      <div class="summary">{{ pageInfo.total }}个搜索结果</div>
    </template>

    <template #barRight>
      <multi-select
        class="barRight"
        :value="pageInfo.sort"
        :options="reqTimeOptions"
        :width="120"
        :hasValue="true"
        @change="handleReqTimeChange"
      >
        <div slot="label" class="reqTimeSelect">
          <div>{{ reqTimeLabelName }}</div>
          <IconFont icon="sort" :size="16" />
        </div>
      </multi-select>
    </template>

    <scroll-page
      ref="games-scroll-page"
      class="m-page"
      :loading="pageInfo.loading"
      :finished="pageInfo.finished"
      :list="dataList"
      @scroll-bottom="handleBottom"
    >
      <template v-slot:list="{list}">
        <waterfall :dataList="list" :scale="35" :itemWidth="215" :itemHeight="205">
          <template v-slot:default="{item}">
            <GameCard :item="item" />
          </template>
        </waterfall>
      </template>
    </scroll-page>

    <div class="news" v-if="showGameNews" @click="closeNews">
      <div class="content" @click.stop>
        <div class="text">
          <div class="title">游戏截图功能上线啦</div>
          <div class="subtitle">搜索全网爆款游戏截图（支持搜索游戏名字、标签等）</div>
        </div>
        <div class="btn" @click="closeNews">好的</div>
      </div>
    </div>
  </GameLayout>
</template>

<script>
import GameLayout from '@/components/layouts/game-layout/index.vue'
import Waterfall from '@/components/waterfall/index.vue'
import GameCard from './components/game-card.vue'
import { searchGameList } from '@/api/spider'

export default {
  name: 'Games',
  components: {
    Waterfall,
    GameLayout,
    GameCard
  },
  computed: {
    reqTimeLabelName() {
      return this.reqTimeOptions.find(item => item.id === this.pageInfo.sort)?.name
    },
    showGameNews() {
      return this.$bus.showGameNews
    },
    updateFlag() {
      return this.$route.query.clear
    }
  },
  watch: {
    '$route.name': {
      immediate: true,
      handler(nv) {
        if (nv === 'games') {
          this.$bus.setGameSearchType('game')
        } else if (['gameSearch', 'game'].includes(nv)) {
          this.$bus.setGameSearchType('material')
        }
      }
    },
    '$route.meta.scrollTop'(val) {
      if (val) {
        this.$refs['games-scroll-page'].setScrollTop(val)
      }
    }
  },
  data() {
    return {
      reqTimeOptions: [
        {
          id: 'POPULARITY',
          name: '人气最多'
        },
        {
          id: 'NEWEST',
          name: '最新截图'
        },
        {
          id: 'LIKE',
          name: '点赞最多'
        }
      ],
      pageInfo: {
        loading: false,
        finished: false,
        total: 0,
        limit: 60,
        sort: 'POPULARITY',
        pageNum: 1,
        pageSize: 60
      },
      dataList: []
    }
  },
  mounted() {
    this.getData()
  },
  activated() {
    if (!this.updateFlag) return
    this.initPageInfo()
    this.getData().then(() => {
      this.$forceUpdate()
    })
  },
  methods: {
    async search(keyword) {
      this.$bus.setGameTitle('')
      this.$bus.setGameSearchKey(keyword)
      if (this.$bus.gameSearchType === 'game') {
        this.initPageInfo()
        await this.getData()
      } else {
        await this.$router.push({
          name: 'gameSearch',
          query: { keyword }
        })
      }
    },
    async filter(filterTagIds) {
      this.initPageInfo()
      this.$bus.setFilterTagIds(filterTagIds)
      await this.getData()
    },
    closeNews() {
      this.$bus.showGameNews = false
      window.localStorage.setItem('showGameNews', '')
    },
    initPageInfo() {
      this.pageInfo.loading = false
      this.pageInfo.finished = false
      this.pageInfo.total = 0
      this.pageInfo.sort = 'POPULARITY'
      this.pageInfo.pageNum = 1
      this.dataList = []
    },
    async getData() {
      try {
        this.pageInfo.loading = true
        const last = this.dataList.length ? this.dataList[this.dataList.length - 1] : null
        const params = {
          limit: this.pageInfo.limit,
          lastId: last?.id,
          lastScore: last?.score,
          sort: this.pageInfo.sort,
          pageNum: this.pageInfo.pageNum,
          pageSize: this.pageInfo.pageSize,
          keyword: this.$bus.gameSearchKey,
          filterTagIds: this.$bus.filterTagIds
        }
        const { list, total } = await searchGameList(params)
        this.pageInfo.total = total
        this.dataList = [...this.dataList, ...list]
        if (list.length !== this.pageInfo.pageSize) {
          this.pageInfo.finished = true
        }
      } finally {
        this.pageInfo.loading = false
      }
    },
    async handleBottom() {
      if (this.pageInfo.finished) return
      this.pageInfo.pageNum++
      await this.getData()
    },
    async handleReqTimeChange(id) {
      this.initPageInfo()
      this.pageInfo.sort = id
      await this.getData()
    }
  },
  beforeRouteLeave(_, from, next) {
    from.meta.scrollTop = this.$refs['games-scroll-page'].getScrollTop()
    next()
  }
}
</script>

<style lang="less" scoped>
.summary {
  font-size: 12px;
  padding: 0 12px;
}

.barRight {
  margin-right: 12px;
}

.reqTimeSelect {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.m-page {
  padding-top: 0;
}

.news {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.content {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  width: 460px;
  height: 484px;
  padding: 60px 0;
  background-color: rgba(0, 0, 0, 0.8);
  background-image: url("../../assets/img/game-news-bg.png");
  background-repeat: no-repeat;
  background-size: cover;

  .text {
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
      font-size: 28px;
    }

    .subtitle {
      margin-top: 4px;
      font-size: 12px;
      opacity: 0.7;
    }
  }

  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 240px;
    height: 40px;
    padding: 10px;
    font-size: 15px;
    cursor: pointer;
    border-radius: 28px;
    background: linear-gradient(290deg, #1473E6 -5%, #3BE8E2 121%);
  }
}
</style>

