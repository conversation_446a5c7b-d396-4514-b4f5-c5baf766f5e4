<template>
  <GameLayout @filter="filter" @search="search">
    <template #barLeft>
      <div class="summary">
        <img
          src="@/assets/img/arrow-left.png"
          :width="5"
          @click="back"
          alt=""
        />
        <span>{{ pageInfo.total }}个搜索结果</span>
      </div>
    </template>

    <template #barRight>
      <!-- 图像大小 -->
      <Tooltip placement="bottom-start" theme="dark">
        <div class="scale">
          <span>图像大小</span>
          <img src="@/assets/img/scale.png" :width="11" alt />
        </div>

        <div slot="content" class="scale-box">
          <IconFont icon="reduce" class="reduce" :size="10" @click="scale-=5" />
          <Slider v-model="scale" class="slider-scale" />
          <IconFont icon="add" class="add" :size="10" @click="scale+=5" />
        </div>
      </Tooltip>

      <!-- 排序 -->
      <multi-select
        class="barRight"
        :value="pageInfo.sort"
        :options="reqTimeOptions"
        :width="140"
        :hasValue="true"
        @change="handleReqTimeChange"
      >
        <div slot="label" class="reqTimeSelect">
          <div>{{ reqTimeLabelName }}</div>
          <IconFont icon="sort" :size="16" />
        </div>
      </multi-select>
    </template>

    <scroll-page
      ref="game-scroll-page"
      :loading="pageInfo.loading"
      :finished="pageInfo.finished"
      :list="dataList"
      @scroll-bottom="handleBottom"
    >
      <template v-slot:list="{list}">
        <waterfall :dataList="list" :scale="scale">
          <template v-slot:default="{item}">
            <MaterialCard :item="item" :scale="scale" />
          </template>
        </waterfall>
      </template>
    </scroll-page>
  </GameLayout>
</template>

<script>
import GameLayout from '@/components/layouts/game-layout/index.vue'
import GameCard from '@/pages/game/components/game-card.vue'
import MaterialCard from '@/pages/game/components/material-card.vue'
import Waterfall from '@/components/waterfall/index.vue'
import { searchGameImageList } from '@/api/spider'

export default {
  name: 'Game',
  components: {
    Waterfall,
    GameCard,
    GameLayout,
    MaterialCard
  },
  data() {
    return {
      reqTimeOptions: [
        {
          id: 'DEFAULT',
          name: '综合排序'
        },
        {
          id: 'MOST_FREQUENTLY_DOWNLOAD',
          name: '最常下载'
        },
        {
          id: 'RECENTLY_DOWNLOAD',
          name: '最近下载'
        },
        {
          id: 'UPLOAD_TIME_ASC',
          name: '按时间排序'
        },
        {
          id: 'UPLOAD_TIME_DESC',
          name: '按时间倒排序'
        }
      ],
      pageInfo: {
        loading: false,
        finished: false,
        total: 0,
        limit: 200,
        sort: 'DEFAULT',
        pageNum: 1,
        pageSize: 200
      },
      dataList: [],
      scale: 30,
      minW: 72,
      minH: 56,
      maxW: 464
    }
  },
  computed: {
    reqTimeLabelName() {
      return this.reqTimeOptions.find(item => item.id === this.pageInfo.sort)?.name
    },
    width() {
      return parseInt(String(this.minW + ((this.maxW - this.minW) * this.scale) / 100))
    },
    height() {
      return parseInt(String((this.width * this.minH) / this.minW))
    },
    gameTitle() {
      return this.$bus.gameTitle || window.sessionStorage.getItem('gameTitle')
    }
  },
  watch: {
    '$route.name': {
      immediate: true,
      handler(nv) {
        if (nv === 'games') {
          this.$bus.setGameSearchType('game')
        } else if (['gameSearch', 'game'].includes(nv)) {
          this.$bus.setGameSearchType('material')
        }
      }
    },
    '$route.meta.scrollTop'(val) {
      if (val) {
        this.$refs['game-scroll-page'].setScrollTop(val)
      }
    },
    '$bus.gameMaterialList'(nv) {
      this.dataList = nv
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    search(keyword) {
      this.$bus.setGameTitle('')
      this.$bus.setGameSearchKey(keyword)
      if (this.$bus.gameSearchType === 'game') {
        this.$router.push({
          name: 'games',
          query: { clear: true }
        })
      } else {
        this.$router.push({
          name: 'gameSearch',
          query: { keyword }
        })
      }
    },
    async filter(filterTagIds) {
      this.initPageInfo()
      this.$bus.setFilterTagIds(filterTagIds)
      await this.getData()
    },
    back() {
      this.$bus.setGameSearchKey('')
      this.$bus.setGameSearchType('game')
      this.$router.replace({
        name: 'games',
        query: { clear: true }
      })
    },
    initPageInfo() {
      this.pageInfo.loading = false
      this.pageInfo.finished = false
      this.pageInfo.total = 0
      this.pageInfo.sort = 'DEFAULT'
      this.pageInfo.pageNum = 1
      this.dataList = []
    },
    async getData() {
      try {
        this.pageInfo.loading = true
        const last = this.dataList.length ? this.dataList[this.dataList.length - 1] : null
        const { list, total } = await searchGameImageList({
          limit: this.pageInfo.limit,
          lastId: last?.id,
          lastScore: last?.score,
          sort: this.pageInfo.sort,
          pageNum: this.pageInfo.pageNum,
          pageSize: this.pageInfo.pageSize,
          keyword: this.$bus.gameSearchKey,
          filterTagIds: this.$bus.filterTagIds
        })
        this.pageInfo.total = total
        this.dataList = [...this.dataList, ...list]
        this.$bus.setGameMaterialList(this.dataList)
        if (list.length !== this.pageInfo.pageSize) {
          this.pageInfo.finished = true
        }
      } finally {
        this.pageInfo.loading = false
      }
    },
    async handleReqTimeChange(id) {
      this.initPageInfo()
      this.pageInfo.sort = id
      await this.getData()
    },
    async handleBottom() {
      if (this.pageInfo.finished) return
      this.pageInfo.pageNum++
      await this.getData()
    }
  },
  beforeRouteLeave(_, from, next) {
    from.meta.scrollTop = this.$refs['game-scroll-page'].getScrollTop()
    next()
  }
}
</script>

<style lang="less" scoped>
.summary {
  font-size: 12px;
  padding: 0 12px;

  img {
    margin-right: 9px;
    cursor: pointer;
  }
}

.scale {
  display: flex;
  align-items: center;
  margin: 0 12px;
  cursor: pointer;
}


:deep(.tooltip-popper) {
  transform: translateX(-50px);
}

:deep(.tooltip-inner) {
  padding: 0;
}

.scale-box {
  display: flex;
  align-items: center;
  padding: 6px;

  .slider-scale {
    width: 144px;
    margin: 0 8px;
  }

  .reduce,
  .add {
    cursor: pointer;

    &:hover {
      color: #1c7aec;
    }
  }
}

.barRight {
  margin-right: 20px;
}

.reqTimeSelect {
  display: flex;
  align-items: center;
  cursor: pointer;
}
</style>
