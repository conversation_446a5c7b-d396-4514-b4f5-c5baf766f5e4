<template>
  <div class="filter-bar" >
    <div v-if="opened" class="model"></div>
    <div  v-clickoutside="handleClose">
      <div class="spectrum-Examples-itemGroup">
        <GButton
            :key="item.key"
            v-for="item in dropList"
            size="small"
            @click="handelVisible"
            ghost
        >
          {{ item.name }}
          {{
            searchParams[item.key] && searchParams[item.key].length
              ? `(${searchParams[item.key].length})`
              : ''
          }}
          <IconFont
              icon="down-arrow"
              :size="22"
              :style="{ marginLeft: '3px' }"
              :class="{ rotate180: opened }"
          />
        </GButton>
      </div>
      <div
          v-if="opened"
          class="spectrum-Popover spectrum-Popover--bottom spectrum-Picker-popover is-open"
          :style="{ 'z-index': 6, width: popWidth }"
      >
        <div class="tags-group-item" v-for="item in dropList" :key="item.key">
          <div class="tags-group-label" @click="handleToggle(item.key)">
            {{ item.name }}{{ item.items.length > 0 ? `(${item.items.length})` : '' }}
            <IconFont
                class="arrow-icon"
                icon="down-arrow2"
                :size="22"
                :class="{ 'is-close': !tagToggle[item.key] }"
            />
          </div>
          <div
              class="spectrum-Examples-itemGroup tags-groups"
              :class="{ 'is-close': !tagToggle[item.key] }"
          >
            <button
                key="0"
                :class="{
                'is-selected':
                  !searchParams[item.key] || !searchParams[item.key].length,
              }"
                class="spectrum-ActionButton spectrum-ActionButton--sizeS spectrum-ActionButton--emphasized"
                @click="handleMultiCancel(item.key)"
            >
              <span class="spectrum-ActionButton-label">不限</span>
            </button>
            <button
                v-for="tag in item.items"
                :key="`${item.key}_${tag.value}`"
                :class="{
                'is-selected':
                  searchParams[item.key] &&
                  searchParams[item.key].includes(tag.value),
              }"
                class="spectrum-ActionButton spectrum-ActionButton--sizeS spectrum-ActionButton--emphasized"
                @click="handleMultiChange(item.key, tag.value)"
            >
              <span class="spectrum-ActionButton-label">{{ tag.label }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getDropList} from "@/api/spider";

export default {
  name: 'FilterBar',
  props: {
    searchParams: {
      type: Object,
      require: true
    }
  },
  data() {
    return {
      tagToggle: {},
      opened: false,
      popWidth: '0px',
      dropList: []
    }
  },
  watch: {
    opened(val) {
      if (val) {
        this.dropList.forEach((item) => {
          this.$set(this.tagToggle, item.key, true)
        })
      }
    },
  },
  mounted() {
    this.popWidth = `${document.body.clientWidth - 24}px`
    window.onresize = () => {
      this.popWidth = `${document.body.clientWidth - 24}px`
    }
    this.getDropList()
  },
  methods: {
    async getDropList() {
      let dropList = await getDropList()
      const curRoute = this.$route.name
      if (curRoute === 'games') {
        dropList = dropList.filter(item => !['游戏功能', '材质图案'].includes(item.name))
      }
      this.dropList = dropList?.map(item => ({
        name: item.name,
        key: item.key,
        items: item?.items.map(v => ({label: v.name, value: v.id})) || []
      })) || []
    },
    handleMultiChange(key, val) {
      const searchParams = JSON.parse(JSON.stringify(this.searchParams))
      if (searchParams[key] && searchParams[key].includes(val)) {
        searchParams[key].splice(searchParams[key].indexOf(val), 1)
      } else {
        if (!searchParams[key]) {
          searchParams[key] = []
        }
        searchParams[key].push(val)
      }
      this.$emit('change', key, searchParams[key])
      this.$emit('toggle')
      this.$forceUpdate()
    },
    handleMultiCancel(key) {
      this.$emit('change', key, [])
      this.$emit('toggle')
      this.$forceUpdate()
    },
    handleToggle(key) {
      this.$set(this.tagToggle, key, !this.tagToggle[key])
    },
    handelVisible(e) {
      e.stopPropagation()
      this.opened = !this.opened
    },
    handleClose() {
      if (this.opened) {
        this.opened = false
      }
    },
  }
}
</script>

<style lang="less" scoped>
.filter-bar {
  position: relative;
  margin-bottom: 8px;

  .model {
    position: fixed;
    content: '';
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: 5;
  }

  .spectrum-Popover {
    position: absolute;
    top: 25px;
    padding: 4px 12px;
    max-height: 400px;
    overflow-y: auto;

    .tags-group-item {
      margin: 4px 0;

      .tags-group-label {
        font-size: 12px;
        color: #ffffff;
        margin-bottom: 8px;
        cursor: pointer;

        .arrow-icon {
          display: inline-block;

          &.is-close {
            transform: rotate(90deg);
          }
        }
      }

      .tags-groups {
        margin: 0 -2px;
        height: auto;
        overflow: hidden;

        .color-index-wraper {
          margin: -3px;
        }

        .color-index {
          display: table-cell;
        }

        &.is-close {
          height: 0px;
          display: none;
        }
      }
    }
  }

  .spectrum-ActionButton {
    margin: 2px;
    border-color: #3e3e3e;
    border-radius: 4px;
    background-color: transparent;

    &.spectrum-ActionButton--emphasized.is-selected {
      background-color: #2c75de;
      border-color: #2c75de;
      color: #ffffff;
    }

    &.unlimited {
      border-color: #383838;
      background-color: #474747;
      color: #c7c7c7;

      &:hover {
        border-color: #383838;
        background-color: #535353;
        color: #ffffff;
      }

      &:active {
        border-color: #383838;
        background-color: #383838;
        color: #c7c7c7;
      }
    }
  }
}
</style>
