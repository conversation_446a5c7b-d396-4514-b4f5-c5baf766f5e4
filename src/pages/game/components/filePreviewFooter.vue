<template>
  <div class="footer">
    <GButton
      class="btn"
      type="primary"
      :disabled="isDownLoading"
      @click="handlePut()"
    >{{
        processNum
          ? `下载中 ${processNum}%`
          : '在当前窗口置入'
      }}
    </GButton>
  </div>
</template>

<script>
import { DOWN_OPERATE_ENUM } from '@/bridge/const'
import FilePutMixin from "@/mixins/filePut";

export default {
  name: 'FilePreviewFooter',
  mixins: [FilePutMixin],
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    DOWN_OPERATE_ENUM() {
      return DOWN_OPERATE_ENUM
    },
    processNum() {
      return this.$bus.downloadFiles[this.item.id] || 0
    },
  },
  data() {
    return {
      isDownLoading: false
    }
  },
  methods: {
    async handlePut() {
      try {
        this.isDownLoading = true
        let fileName
        if (this.item.id) fileName = this.item.id
        else fileName = new Date().getTime().toString() + Math.random().toString(36).slice(2, 11)
        await this.handelFilePut(this.item, `${fileName}.jpg`) // mixin方法
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: `下载置入失败，${e.message}`
        })
      } finally {
        this.isDownLoading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.footer {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 16px;

  .btn {
    width: 228px;
    height: 28px;
  }
}
</style>