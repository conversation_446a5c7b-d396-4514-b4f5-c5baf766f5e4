<template>
  <div class="gameCard thumbnail" @dblclick="skipGameDetail">
    <div class="cover">
      <img :src="item.coverUrl" alt=""  />

      <div v-if="hot" class="hot">
        <img
          v-for="i in hot"
          :key="i"
          src="@/assets/img/hot.png" alt=""
        />
      </div>
    </div>

    <div class="info">
      <div class="name">{{ item.title }}</div>
      <div class="count">{{ item.imageCount }}张</div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GroupCard',
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    hot() {
      if (!this.item.viewCount) return 0
      if (this.item.viewCount < 10000) return 1
      if (this.item.viewCount < 30000) return 2
      return 3
    }
  },
  data() {
    return {}
  },
  methods: {
    skipGameDetail() {
      this.$bus.setGameSearchKey('')
      this.$bus.setGameTitle(this.item.title)
      this.$bus.setGameSearchType('material')
      this.$router.push({
        name: 'game',
        params: {
          id: this.item.id
        }
      })
    }
  }
}
</script>

<style lang='less' scoped>
.gameCard {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  cursor: pointer;
  border-radius: 6px;
  overflow: hidden;
  border: 0.5px solid #3E3E3F;

  &:hover {
    background: #575757;
  }

  .cover {
    position: relative;
    width: 100%;

    > img {
      width: 100%;
      object-fit: contain;
    }

    .hot {
      position: absolute;
      top: 4px;
      right: 4px;
      display: flex;
      align-items: center;
      height: 14px;
      padding: 0 4px;
      border-radius: 1.56px;
      overflow: hidden;
      border: 0.39px solid rgba(204, 204, 204, 0.2);
      background-color: rgba(102, 102, 102, 0.4);
      backdrop-filter: blur(31.11px);

      img {
        height: 9px;
        margin-right: 4px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .info {
    flex: 1;
    height: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 0 12px;

    .name {
      font-size: 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .count {
      margin-top: 2px;
      font-size: 11px;
      color: #868686;
    }
  }
}
</style>
