<template>
  <layout>
    <div class="m-list">
      <nav-bar :title="getDetailData.projectName" />
      <search-box :project="false" @change="handleSearch" />
      <scroll-page
        ref="detail-scroll-page"
        :loading="pageInfo.loading"
        :finished="pageInfo.finished"
        :list="dataList"
        class="u-main"
        @scroll-bottom="handleBottom"
      >
        <template v-slot:list="slotProps">
          <material-detail
            :detail-data="detailData"
            @turnLast="turnLast"
            @turnNext="turnNext"
          />

          <hr class="spectrum-Divider spectrum-Divider--sizeM">

          <div class="u-similar-list">
            <div class="u-list-title">相似图片</div>
            <waterfall
              :dataList="slotProps.list"
              @item-click="handleItemClick"
              :scale="15"
            />
          </div>
        </template>
      </scroll-page>
    </div>
  </layout>
</template>
<script>
import NavBar from '@/components/layouts/nav-bar'
import SearchBox from '@/components/search-box'
import Waterfall from '@/components/waterfall'
import MaterialDetail from './material-detail/index.vue'
import { getRecommendationList, getResourceDetail } from '@/api/index.js'

export default {
  name: 'Detail',
  components: {
    NavBar,
    SearchBox,
    Waterfall,
    MaterialDetail
  },
  data() {
    return {
      pageInfo: {
        loading: false,
        finished: false,
        total: 0,
        pageSize: 200,
        pageNum: 1
      },
      dataList: [],
      getDetailData: {}
    }
  },
  computed: {
    detailData() {
      return { ...this.$bus.detail, ...this.getDetailData }
    },
    detailDataId() {
      return this.$bus.detail?.id || ''
    }
  },
  watch: {
    '$route.params.id'(val) {
      this.detailDataId = val
      this.getDataDetail()
      this.getData()
      this.$refs['detail-scroll-page'].setScrollTop(0)
    }
  },
  created() {
    this.getDataDetail()
    this.getData()
  },
  methods: {
    getData() {
      if (!this.detailData) return
      this.pageInfo.loading = true
      const params = {
        id: this.detailData.id,
        name: this.detailData.name,
        ext: this.detailData.ext,
        tags: this.detailData.tags,
        tagSplicing: this.detailData.tagSplicing,
        categoryId: this.detailData.categoryId,
        categoryName: this.detailData.categoryName,
        pageSize: this.pageInfo.pageSize,
        pageNum: this.pageInfo.pageNum
      }
      getRecommendationList(params).then(d => {
        this.dataList = [...this.dataList, ...d.list]
        this.pageInfo.total = d.total
        if (d.pageNum === d.pages) {
          this.pageInfo.finished = true
        }
      }).catch((e) => {
        this.$logMsg.error('[detail][getData] 获取文件详情失败 ' + e.message, '素材库')
      }).finally(() => {
        this.pageInfo.loading = false
      })
    },
    getDataDetail() {
      getResourceDetail(this.detailDataId).then(d => {
        this.getDetailData = d
      }).catch((e) => {
        this.$logMsg.error('[detail][getDataDetail] 获取文件详情失败 ' + e.message, '素材库')
      })
    },
    handleItemClick(item) {
      this.$bus.setDetailData(item)
      this.$router.push({
        name: 'detail',
        params: {
          id: item.id
        }
      })
    },
    // 加载更多
    handleBottom() {
      this.pageInfo.pageNum = this.pageInfo.pageNum + 1
      this.getData()
    },
    handleSearch() {
      // 参数改变，返回列表页面重新请求数据
      this.$bus.$emit('search')
      this.$router.push({
        name: 'list'
      })
    },
    async turnLast(id) {
      const curIndex = this.$bus.list.findIndex((item) => item.id === id)
      if (curIndex < 0) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '网络出问题了，请刷新页面'
        })
        return
      }

      if (curIndex === 0) {
        this.$CustomToast({
          type: 'warning',
          duration: 2,
          content: '已经是第一个了'
        })
        return
      }

      if (curIndex > 0) {
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '加载中，请稍后'
        })
        const detail = this.$bus.list[curIndex - 1]
        this.$bus.setDetailData(detail)
        await this.getDataDetail()
      }
    },
    async turnNext(id) {
      const curIndex = this.$bus.list.findIndex((item) => item.id === id)
      if (curIndex < 0) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '网络出问题了，请刷新页面'
        })
        return
      }

      if (curIndex === this.$bus.list.length - 1) {
        this.$CustomToast({
          type: 'warning',
          duration: 2,
          content: '已经是最后一个了'
        })
        return
      }

      if (curIndex >= 0) {
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '加载中，请稍后'
        })
        const detail = this.$bus.list[curIndex + 1]
        this.$bus.setDetailData(detail)
        await this.getDataDetail()
      }
    }
  }
}
</script>

<style lang="less" scoped>
.m-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .u-main {
    width: 100%;
    height: 100%;
    padding: 0;
    border-top-left-radius: 4px;
    border-left: 1px solid #383838;
    border-top: 1px solid #383838;
    overflow-y: scroll;

    .u-similar-list {
      padding: 12px;

      .u-list-title {
        padding-bottom: 12px;
      }
    }
  }
}
</style>
