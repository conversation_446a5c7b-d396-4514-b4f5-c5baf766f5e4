<template>
  <div class="m-material-detail">
    <div class="spectrum-Thumbnail">
      <img
        class="turnIcon turnLeftIcon"
        src="@/assets/img/svg/left.svg"
        alt=""
        @click="turn('last')"
      />

      <img
        :src="detailData.thumbnailUrl"
        class="spectrum-Thumbnail-image"
        alt="err"
      />

      <img
        class="turnIcon turnRightIcon"
        src="@/assets/img/svg/left.svg"
        alt=""
        @click="turn('next')"
      />

      <div v-if="loadingView" class="u-options-loading">
        <div class="u-options-progress" :style="{ width: `${processNum}%` }"></div>
        <div class="u-options-text">{{ processNum }}%</div>
      </div>
    </div>

    <div class="btns">
      <div class="download" @click.stop="handlePut(detailData)">下载</div>
      <!-- 管理员 -->
      <template v-if="$bus.profile.role === '系统管理员'">
        <div class="edit" @click="handleEdit">编辑素材</div>
        <div class="delete" @click.stop="handleDel">删除素材</div>
      </template>
    </div>

    <div class="u-text-field">
      <div class="u-label">素材名称</div>
      <div class="u-text-item">
        {{ detailData.name || '*' }}
      </div>
    </div>

    <div class="u-text-field">
      <div class="u-label">素材作者</div>
      <div class="u-text-item">
        {{ detailData.authorName || '*' }}
      </div>
    </div>

    <div class="u-text-field">
      <div class="u-label">制作耗时</div>
      <div class="u-text-item">
        {{ detailData.minutes ? `${detailData.minutes} 分钟` : '*' }}
      </div>
    </div>

    <div class="u-text-field">
      <div class="u-label">尺寸</div>
      <div class="u-text-item">
        {{ detailData.width && detailData.height ? `${detailData.width} \&times; ${detailData.height} px` : '*' }}
      </div>
    </div>

    <div class="u-text-field">
      <div class="u-label">文件大小</div>
      <div class="u-text-item">
        {{ detailData.size ? $renderSize(detailData.size) : '*' }}
      </div>
    </div>

    <div class="u-text-field">
      <div class="u-label">类型</div>
      <div class="u-text-item">
        {{ detailData.ext ? detailData.ext.toUpperCase() : '*' }}
      </div>
    </div>

    <div class="u-text-field">
      <div class="u-label">标签</div>
      <div class="u-text-item">
        {{ detailData.tags.length > 0 ? detailData.tags.map(item => item.name).join('/') : '*' }}
      </div>
    </div>

    <div class="u-text-field">
      <div class="u-label">归属</div>
      <div class="u-text-item">
        {{ detailData.projectName || '*' }}
      </div>
    </div>
  </div>
</template>

<script>
import FilePutMixin from '@/mixins/filePut'
import { deleteResource } from '@/api'

export default {
  mixins: [FilePutMixin],
  props: {
    detailData: {
      type: Object,
      default() {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false
    }
  },
  computed: {
    processNum() {
      return this.$bus.downloadFiles[this.detailData.id] || 0
    },
    loadingView() {
      return Object.prototype.hasOwnProperty.call(this.$bus.downloadFiles, this.detailData.id)
    }
  },
  methods: {
    handlePut(item) {
      if (this.loading) return
      this.loading = true
      let fileName
      const ext = item.ext || 'jpg'
      if (item.id) fileName = item.id
      else fileName = new Date().getTime().toString() + Math.random().toString(36).slice(2, 11)
      this.handelFilePut(item, `${fileName}.${ext}`) // mixin方法
      this.loading = false
    },
    handleEdit() {
      if (this.loading) return
      this.loading = true
      this.$router.push({
        name: 'upload',
        params: {
          mode: 'update',
          id: this.detailData.id,
          thumbnailUrl: this.detailData.thumbnailUrl,
          workTime: this.detailData.workTime,
          workTimeUnit: this.detailData.workTimeUnit,
          tags: this.detailData.tags,
          projectId: this.detailData.projectId
        }
      })
      this.loading = false
    },
    async handleDel() {
      if (!this.$bus.profile.role) {
        await this.$bus.getUserInfo()
      }

      if (this.$bus.profile.role !== '系统管理员') {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '仅系统管理员可删除素材'
        })
        return
      }

      if (this.loading) return
      try {
        this.loading = true
        this.$bus.setList(this.$bus.list.filter(i => i.id !== this.detailData.id))
        await deleteResource(this.detailData.id)
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '删除成功'
        })
        await this.$router.back()
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: e.message
        })
      } finally {
        this.loading = false
      }
    },
    turn(way) {
      if (way === 'last') return this.$emit('turnLast', this.detailData.id)
      this.$emit('turnNext', this.detailData.id)
    }
  }
}
</script>

<style lang="less" scoped>
.m-material-detail {
  width: 100%;
  padding: 0 12px 10px;

  .spectrum-Thumbnail {
    position: relative;
    //display: flex;
    //justify-content: space-between;
    width: 100%;
    height: 195px;
    margin: 12px 0;
    padding: 0 12px;
    background-color: #5A5A5A;
    background-image: none;
    box-shadow: none;
    border-radius: 5px;

    &::before {
      box-shadow: none;
    }

    &:hover {
      .u-options {
        display: block;
      }
    }

    .turnLeftIcon,
    .turnRightIcon {
      position: absolute;
      z-index: 10;
      cursor: pointer;
      border-radius: 3px;

      &:hover {
        background-color: #3D7AF5;
      }
    }

    .turnLeftIcon {
      left: 3%;
      top: 40%;
    }

    .turnRightIcon {
      right: 3%;
      top: 40%;
      transform: rotate(180deg);
    }

    .u-options {
      display: none;
      position: absolute;
      width: 100%;
      height: 24px;
      left: 0;
      bottom: 0;
      color: #fff;
      text-align: center;
      line-height: 24px;
      background: #1473E6;
      cursor: pointer;
      z-index: 2;
      border-radius: 0 0 4px 4px;
    }

    .u-options-loading {
      position: absolute;
      width: 100%;
      height: 24px;
      left: 0;
      bottom: 0;
      line-height: 24px;
      background: #fff;
      z-index: 2;
      border-radius: 0 0 4px 4px;
      overflow: hidden;

      .u-options-progress {
        position: absolute;
        left: 0;
        bottom: 0;
        height: 24px;
        background: #1473e6;
      }

      .u-options-text {
        position: absolute;
        line-height: 24px;
        right: 4px;
        bottom: 0;
        height: 24px;
        color: #333;
      }
    }
  }
}

.btns {
  display: flex;
  justify-content: space-evenly;
  margin-bottom: 12px;

  .download,
  .edit,
  .delete {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 32px;
    margin: 0 4px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 4px;
    cursor: pointer;
  }

  .download {
    flex: 0.5;
    background-color: #0d66d0;
  }

  .edit {
    flex: 0.25;
    border: 1px solid #E2E3E3;
  }

  .delete {
    flex: 0.25;
    background-color: #E41E1E;
  }
}

.u-text-field {
  display: flex;
  flex-direction: row;

  .u-label {
    width: 90px;
  }

  .u-text-item {
    flex: 1;
  }
}

:deep(.spectrum-Menu-item) {
  padding-left: 8px;
}
</style>
