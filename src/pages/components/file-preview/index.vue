<template>
  <div
    class="file-preview"
    ref="filePreviewRef"
    tabindex="0"
    @keyup="keyMonitor"
  >
    <img
      class="turnLeftIcon"
      src="@/assets/img/svg/left.svg"
      alt=""
      @click="turn('last')"
    />

    <div
      v-if="item.cover && item.cover.startsWith('http')"
      class="file-img-preview"
    >
      <element-drag
        id="imgView"
        :outerOptions="{ background: '#424242' }"
        :radio="radio"
        :scaleZoom="scaleZoom"
      >
        <img :src="item.cover" />
      </element-drag>
      <div class="file-preview-close-btn" @click="handleClose">
        <IconFont icon="close" :size="16" />
      </div>
    </div>
    <div v-else class="file-img-preview">
      <div class="file-img-preview-empty">
        <img :src="require('@/assets/img/svg/default.svg')" width="60" />
      </div>
      <div class="file-preview-close-btn" @click="handleClose">
        <IconFont icon="close" :size="16" />
      </div>
    </div>

    <img
      class="turnRightIcon"
      src="@/assets/img/svg/left.svg"
      alt=""
      @click="turn('next')"
    />

    <slot name="footer">
      <div class="file-preview-btn">
        <GButton v-if="showGoto" @click="handleGotoFile">前往文件位置</GButton>

        <div class="right">
          <GButton
            @click="handleOpenOnPS(item, DOWN_OPERATE_ENUM.INPUT)"
            :disabled="item.permission === 'READ_ONLY' || isDownLoading"
            :process="isInputOperate && isDownLoading ? process : 0"
            style="margin-right: 16px;"
          >{{
              isInputOperate && isDownLoading
                ? downloadProcessText
                : '在当前窗口置入'
            }}
          </GButton>

          <GButton
            @click="handleOpenOnPS(item, DOWN_OPERATE_ENUM.NEW_WINDOW_OPEN)"
            :disabled="item.permission === 'READ_ONLY' || isDownLoading"
            type="primary"
            :process="!isInputOperate && isDownLoading ? process : 0"
          >{{
              !isInputOperate && isDownLoading
                ? downloadProcessText
                : '在新窗口打开'
            }}
          </GButton>
        </div>
      </div>
    </slot>
  </div>
</template>

<script>
import { getFileInfoByUrl } from '@/utils/index'
import { memberList } from '@/api'
import router from '@/router'
import { DOWN_OPERATE_ENUM } from '@/bridge/const'
import ClientDownloadFileMixin from '@/mixins/client-download-file'

export default {
  mixins: [ClientDownloadFileMixin],
  name: 'FilePreview',
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    showGoto: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      height: null,
      width: null,
      radio: 1,
      scaleZoom: {
        max: 5,
        min: 0.2
      },
      DOWN_OPERATE_ENUM,
      isDownLoading: false
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getRadio()
      this.$refs.filePreviewRef.focus()
    })
  },
  computed: {
    isUploading() {
      return (
        this.$bus.uploadFilesList.findIndex((i) => i.sid === this.item.id) > -1
      )
    },
    downloadInfo() {
      return (
        this.$bus.downloadFilesList.find(
          (i) => i.file_key === this.item.fileKey
        ) || {}
      )
    },
    process() {
      return this.downloadInfo.process || 0
    },
    downloadLength() {
      return this.downloadInfo.download_length || 0
    },
    contentLength() {
      return this.downloadInfo.content_length || 0
    },
    isInputOperate() {
      return this.downloadInfo.operate_code === DOWN_OPERATE_ENUM.INPUT
    },
    downloadProcessText() {
      const { permission } = this.item
      if (permission === 'READ_ONLY') {
        return '暂无权限编辑此文件'
      } else if (this.isDownLoading) {
        return `${this.$renderSize(this.downloadLength)}/${this.$renderSize(
          this.contentLength
        )}`
      } else {
        return ''
      }
    }
  },
  watch: {
    downloadInfo: {
      deep: true,
      handler(val) {
        if (val.file_key) {
          this.isDownLoading = true
        }
      }
    }
  },
  methods: {
    keyMonitor(e) {
      switch (e.keyCode || KeyboardEvent.code) {
        case 37: // 左箭头键
          this.turn('last')
          break
        case 39: // 右箭头键
          this.turn('next')
          break
      }
    },
    handleClose() {
      this.$CustomDialogRemove()
    },
    async handleOpenOnPS(item, operateCode) {
      if (this.isDownLoading) {
        this.$CustomToast({
          type: 'warning',
          duration: 2,
          content: `文件正在下载，请稍后操作`
        })
        return
      }
      if (this.isUploading) {
        this.$CustomToast({
          type: 'warning',
          duration: 2,
          content: `文件正在上传中，请稍后操作`
        })
        return
      }

      try {
        this.isDownLoading = true
        await this.clientDownloadMixin({ item: this.item, operateCode })
        this.handleClose()
      } finally {
        this.isDownLoading = false
      }
    },
    getRadio() {
      if (!document.getElementById('imgView')) return
      const imgViewWidth = document.getElementById('imgView').clientWidth
      const imgViewHeight = document.getElementById('imgView').clientHeight
      getFileInfoByUrl(this.item.cover)
        .then((res) => {
          this.radio = 0.5
          if (imgViewWidth / imgViewHeight > res.Width / res.Height) {
            this.radio = parseFloat((imgViewHeight / res.Height).toFixed(2))
          } else {
            this.radio = parseFloat((imgViewWidth / res.Width).toFixed(2))
          }
          // console.log('radio', this.radio)
          this.scaleZoom.max = parseFloat((this.radio * 5).toFixed(2))
          this.scaleZoom.min = parseFloat((this.radio / 5).toFixed(2))
          this.height = res.Height
          this.width = res.Width
        })
        .catch(() => {
          this.height = 'auto'
          this.width = 'auto'
        })
    },
    async handleGotoFile() {
      let members = []
      let memberPage = 0
      while (true) {
        memberPage += 1
        const res = await memberList(this.item.groupId, memberPage)
        members = [...members, ...res.list]
        if (res.total >= members.length) {
          break
        }
      }
      const inGroup = members.filter((user) => {
        return this.$bus.profile.email === user.email
      })
      let gid = 0
      if (inGroup.length > 0) {
        gid = this.item.groupId
      } else {
        gid = this.$bus.shareWithMe
      }
      await router.push({
        name: 'group',
        params: { gid: gid },
        query: { locationId: this.item.id }
      })
      this.handleClose()
    },
    turn(way) {
      if (way === 'last') return this.$emit('turnLast', this.item.id)
      this.$emit('turnNext', this.item.id)
    }
  }
}
</script>

<style lang="less" scoped>
.file-preview {
  position: relative;
  width: 100%;
  height: 100%;

  .file-preview-close-btn {
    position: absolute;
    top: 16px;
    right: 12px;
    display: flex;
    border-radius: 4px;
    justify-content: center;
    align-items: center;
    background: #424242;
    cursor: pointer;
    color: #fff;
  }

  .file-img-preview {
    width: 100%;
    height: 100%;
    // height: 483px;
    border-radius: 4px;
    overflow: hidden;

    &-empty {
      width: 100%;
      height: 100%;
      background: #424242;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .file-preview-btn {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .right {
      flex: 1;
      text-align: right;
    }
  }
}

.turnLeftIcon,
.turnRightIcon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1000;
  cursor: pointer;
  border-radius: 3px;

  &:hover {
    background-color: #3D7AF5;
  }
}

.turnLeftIcon {
  left: -30px;
}

.turnRightIcon {
  right: -30px;
  transform: translateY(-50%) rotate(180deg);
}

.file-preview:focus {
  outline: none;
}
</style>
