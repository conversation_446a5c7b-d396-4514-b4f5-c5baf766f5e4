<template>
  <div class="u-search spectrum-Search spectrum-Search--quiet" style="position: relative;">
    <input
      v-model="keyword"
      type="number"
      placeholder="素材制作所耗费的时间"
      name="search"
      class="g-input spectrum-Textfield-input spectrum-Search-input"
      autocomplete="off"
      style="z-index: 2;"
      @focus="opened=true"
      @blur="handleBlur"
    />
    <div
      v-if="suggestOptions.length > 0"
      :class="{'is-open': opened}"
      class="spectrum-Popover"
      style="position: absolute; top: 38px; left: 0; z-index:500; width: 100%;max-height: 100px; overflow-y: auto;"
    >
      <ul class="spectrum-Menu" role="listbox">
        <li
          v-for="item in suggestOptions"
          :key="item.id"
          :tabindex="item.id"
          class="spectrum-Menu-item is-selected"
          role="option"
          @click.stop="handleSelected(item.id)"
        >
          <span class="spectrum-Menu-itemLabel">{{ item.name }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
const TIME_OPTIONS = {
  MINUTE: [{
    id: 5,
    name: '5'
  }, {
    id: 10,
    name: '10'
  }, {
    id: 15,
    name: '15'
  }, {
    id: 20,
    name: '20'
  }, {
    id: 30,
    name: '30'
  }, {
    id: 45,
    name: '45'
  }, {
    id: 60,
    name: '60'
  }],
  HOUR: [{
    id: 1,
    name: '1'
  }, {
    id: 1.5,
    name: '1.5'
  }, {
    id: 2,
    name: '2'
  }, {
    id: 5,
    name: '5'
  }, {
    id: 8,
    name: '8'
  }, {
    id: 16,
    name: '16'
  }, {
    id: 24,
    name: '24'
  }, {
    id: 32,
    name: '32'
  }]
}
export default {
  props: {
    units: {
      type: String,
      default: 'MINUTE'
    },
    value: {
      type: Number,
      default: 0
    }
  },
  data () {
    return {
      keyword: '',
      opened: false
    }
  },
  computed: {
    suggestOptions () {
      return TIME_OPTIONS[this.units] || []
    }
  },
  watch: {
    keyword (val) {
      this.$emit('input', val)
    },
    value(val) {
      this.keyword = val
    }
  },
  methods: {
    handleSelected (item) {
      this.keyword = item
    },
    handleBlur () {
      setTimeout(() => {
        this.opened = false
      }, 200)
    }
  }
}
</script>
<style lang="less" scoped>
.u-search {
  width: 100%;
  padding: 0;
}
</style>
