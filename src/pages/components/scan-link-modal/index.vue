<template>
  <Modal v-model="showModal">
    <template #content>
      <div class="content">
        <div v-if="loading" class="is-loading">
          <div class="loading">
            <img src="@/assets/gif/loading.gif" />
            <div class="text">检测到文件链接</div>
          </div>
          <GButton
            class="w-full"
            size="small"
            @click="handleCancel"
          >取消
          </GButton>

        </div>
        <div v-if="fileInfo.id && !loading" class="file-info">
          <div class="title">你是想查看以下文件吗？</div>
          <div class="filename">{{ fileInfo.name }}.{{ fileInfo.ext }}</div>
          <div class="footer">
            <GButton
              class="w-full"
              size="small"
              style="margin-bottom: 8px;"
              type="primary"
              @click="handleShowFile"
            >查看文件
            </GButton>

            <GButton
              class="w-full"
              size="small"
              @click="handleCancel"
            >取消
            </GButton>
          </div>
        </div>
        <div v-if="!fileInfo.id && !loading" class="error-info">
          <img src="@/components/basic/dialog/img/icon.png" />
          <div class="title">未检测出文件</div>
          <div class="desc">可能原因：文件已被修改权限、删除；</div>
          <GButton
            class="w-full"
            size="small"
            type="primary"
            @click="handleCancel"
          >知道了
          </GButton>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script>
import { getDriveFileDetail } from '@/api/cloud.js'
import {
  downloadFile
} from '@/bridge/api'
import FilePreview from '../file-preview/index.vue'
import { getResourceDetail } from '@/api'

export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    fileId: {
      type: String,
      default: undefined
    }
  },
  model: {
    prop: 'show',
    event: 'change'
  },
  computed: {
    showModal: {
      get() {
        return this.show
      },
      set(value) {
        this.$emit('change', value)
      }
    }
  },
  data() {
    return {
      loading: true,
      fileInfo: {}
    }
  },
  methods: {
    handleCancel() {
      this.showModal = false
    },
    async handleShowFile() {
      this.$CustomDialog({
        width: 'calc(100% - 80px)',
        style: {
          height: 'calc(100% - 160px)',
          margin: '0 0 16px'
        },
        custom: true,
        contRender: (h) => {
          return h(FilePreview, {
            props: {
              item: this.fileInfo,
              showGoto: true
            }
          })
        }
      })

      this.showModal = false
    }
  },
  async created() {
    this.loading = true
    try {
      const [res] = await Promise.race([
        getDriveFileDetail(this.fileId),
        getResourceDetail(this.fileId)
      ])
      this.fileInfo = res
    } catch (e) {
      this.fileInfo = {}
    }
    this.loading = false
  }
}
</script>

<style lang="less" scoped>
.content {
  height: fit-content;
  padding: 20px;

  .is-loading {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .loading {
      margin-top: 64px;
      margin-bottom: 30px;
      text-align: center;

      img {
        width: 40px;
      }
    }

    .text {
      margin: 20px 0 50px 0;
      font-size: 18px;
      text-align: center;
    }
  }

  .title {
    font-size: 18px;
  }

  .file-info {
    display: flex;
    height: 180px;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .filename {
      font-size: 14px;
    }

    .footer {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
    }
  }

  .error-info {
    display: flex;
    height: 220px;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;

    .desc {
      color: #ECECEC;
    }
  }

  // button {
  //   width: 100%;
  //   height: 28px;
  //   border-radius: 5px;
  //   border: none;
  //   background-color: #868686;
  //   color: #000;
  //   margin-bottom: 8px;
  // }

  // .primary-button {
  //   background-color: #1373e6;
  //   border: none;
  //   color: #fff;

  //   &:hover {
  //     background-color: #1c7aec;
  //   }
  // }

  // .cancel-button {
  //   background-color: #868686;
  //   &:hover {
  //     background-color: #7e7e7e;
  //   }
  // }
}
</style>
