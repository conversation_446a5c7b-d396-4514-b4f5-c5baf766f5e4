<template>
<div class="c-add-tag">
  <div class="display-flex">
    <div class="spectrum-Textfield flex-1">
      <input
        v-model="keyword"
        class="g-input spectrum-Textfield-input"
        aria-invalid="false"
        type="text"
        placeholder="输入标签，按回车添加"
        id="fieldLabelExample-emailaddress"
        @keyup.enter="handleAddTag"
      />
    </div>
    <div class="flex-none">
      <div v-if="value.length >= 10" style="padding: 6px 0 6px 8px;">最多添加10个</div>
      <div v-else class=" g-button spectrum-Tag spectrum-Tag--sizeM ml-12">
        <span class="spectrum-Tag-label" @click="handleAddTag">贴标签</span>
      </div>
    </div>
  </div>
  <div style="margin-top: 8px; margin-bottom: -8px;">
    <div v-for="tag in value" :key="tag" class=" g-tag spectrum-Tag spectrum-Tag--sizeM mb-8" tabindex="0">
      <span class="spectrum-Tag-label">{{ tag }}</span>
      <button @click="handleRemoveTag(tag)" class="spectrum-ClearButton spectrum-ClearButton--small spectrum-Tag-clearButton" aria-label="Remove tag 1" tabindex="-1">
        <IconFont icon="close" :size="14"/> 
      </button>
    </div>
  </div>
</div>
</template>
<script>
export default {
  name: 'AddTag',
  props: {
    value: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data () {
    return {
      keyword: ''
    }
  },
  methods: {
    handleAddTag () {
      const newTag = this.keyword.trim()
      const value = this.value
      if (newTag && !value.includes(newTag) && value.length < 10) {
        value.push(newTag)
        this.$emit('input', value)
        this.keyword = ''
      } else {
        this.keyword = ''
      }
    },
    handleRemoveTag (tagName) {
      const value = this.value.filter(item => item !== tagName)
      this.$emit('input', value)
    }
  }
}
</script>
<style lang="less" scoped>
.c-add-tag {
  width: 100%;
  .display-flex {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    .flex-1 {
      flex: 1;
      width: 0;
    }
    .flex-none {
      flex: none;
    }
  }
  .ml-12 {
    margin-left: 12px;
  }
  .mb-8 {
    margin-bottom: 8px;
    margin-right: 8px;
  }
  .spectrum-Tag {
    padding: 0 14px;
  }
}
</style>
