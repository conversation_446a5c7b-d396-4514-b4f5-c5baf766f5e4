<template>
  <div class="wrapper">
    <scroll-page
      :show-footer="false"
      ref="list-scroll-page"
      class="transfer-page"
      :loading="pageInfo.loading"
      :finished="pageInfo.finished"
      @scroll-bottom="handleBottom"
      :list="items"
    >
      <template #list>
        <PopoUserItem :item="item" v-for="item in items" :key="item.id">
          <div @click.stop="onClick(item)" style="cursor: pointer;">转移</div>
        </PopoUserItem>
      </template>
    </scroll-page>
  </div>
</template>

<script>
import PopoUserItem from './popo-user-item.vue'
import { memberList } from '@/api'

export default {
  name: 'GroupMemberList',
  props: {
    groupId: [Number, String],
    ownerEmail: String,
  },
  components: {
    PopoUserItem,
  },
  data() {
    return {
      items: [],
      pageInfo: {
        loading: false,
        finished: false,
        pageNum: 0,
      },
    }
  },
  async mounted() {
    await this.handleBottom()
  },
  methods: {
    onClick(item) {
      this.$emit('item-click', item)
    },
    async handleBottom() {
      this.pageInfo.loading = true
      const res = await memberList(this.groupId, this.pageInfo.pageNum + 1)
      this.pageInfo.loading = false
      this.pageInfo.pageNum += 1
      this.items = [
        ...this.items,
        ...res.list.filter((item) => item.email !== this.ownerEmail),
      ]
      if (this.items.length === res.total - 1) {
        this.pageInfo.finished = true
      }
    },
  },
}
</script>

<style lang="less" scoped>
.wrapper {
  margin: -8px -16px;
}
.transfer-page {
  overflow-x: hidden;
  max-height: 470px;
  overflow-y: auto;
  padding: 0;
}
</style>
