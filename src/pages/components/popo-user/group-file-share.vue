<!-- 文件分享给用户 -->
<template>
  <div class="group-file-share">
    <div class="title" @click="handleReturn">
      <IconFont v-if="step === 2" icon="return" :size="24" />
      天枢官网
    </div>

    <!-- POPO用户搜索 -->
    <PopoUserSelect
      @select="handleItemClick"
      :searchAction="searchAction"
      :searchParams="searchParams"
      style="margin-top: 4px;"
    />

    <div class="cont-wrap step1" v-if="step === 1">
      <div>
        <!-- 已邀请用户列表 -->
        <div class="share-wrap">
          <PopoUserItem
            v-for="collaboration in collaborationList"
            :key="collaboration.collaboratorKey"
            :item="collaboration"
          >
            <template #right>
              <div
                style="color: #919191;"
                v-if="!collaboration.operate.options"
              >
                {{ collaboration.operate.text }}
              </div>
              <dropdown
                v-else
                v-model="collaboration.permission"
                :options="collaboration.operate.options"
                @change="(e) => handlePermissionChange(e, collaboration)"
              />
            </template>
          </PopoUserItem>
        </div>
      </div>

      <!-- 获取链接权限改动 -->
      <div class="link-wrap">
        <div
          class="link-t"
          :class="{'no-bottom': fileCollaboratorId !== profileId}"
        >
          <div class="link-t-l">
            <div class="link-t-l-text" style="font-size: 12px">获取链接</div>
            <div style="font-size: 11px">{{ linkText }}</div>
          </div>

          <div class="link-t-r">
            <GButton type="primary" size="small" @click="handleCopyShareUrl">
              <IconFont icon="link" :size="16" />
              &nbsp;复制链接
            </GButton>
          </div>
        </div>

        <div class="link-b" v-if="fileCollaboratorId === profileId">
          获得链接的员工
          <dropdown
            class="link-b-select"
            placement="top-end"
            :offset="56"
            :options="linkShareOptions"
          >
            <div class="link-b-label">
              {{ currentSharePermissionInfo.show }}
              <IconFont v-if="sharePermission" icon="down-arrow2" :size="16" />
            </div>

            <ul class="link-b-select-box" slot="list">
              <template v-for="(item,index) in linkShareOptions">
                <li @click="handleShareClick(item.value)" :key="item.value">
                  <div>
                    获得链接的员工 <b>{{ item.show }}</b>
                    <IconFont
                      v-show="sharePermission === item.value"
                      icon="tick"
                      :size="16"
                      style="vertical-align: -3px; margin-left: 16px;"
                    />
                  </div>
                  <div class="subtitle">{{ item.show }} 可邀请</div>
                </li>

                <hr
                  v-if="index!==linkShareOptions.length-1"
                  style="border: none;background-color: #ACAAA7;height: 1px;"
                  :key="item.value"
                />
              </template>
            </ul>
          </dropdown>
        </div>
      </div>
    </div>

    <div class="cont-wrap" v-if="step === 2">
      <div class="flex-justify line-b">
        <div class="f-bold">以下协作者将收到邀请</div>
        <dropdown
          v-model="invitePermission"
          class="link-b-select"
          placement="bottom-end"
          :options="
            item.permission === 'READ_ONLY'
              ? [
                  {
                    label: '阅读者',
                    value: 'READ_ONLY',
                    show: '阅读者',
                  },
                ]
              : [
                  {
                    label: '编辑者',
                    value: 'READ_WRITE',
                    show: '编辑者',
                  },
                  {
                    label: '阅读者',
                    value: 'READ_ONLY',
                    show: '阅读者',
                  },
                ]
          "
        >
        </dropdown>
      </div>

      <div class="invite-wrap">
        <div
          v-for="item in inviteList"
          :key="item.collaboratorKey"
          class="invite-item"
        >
          <img :src="item.avatar" alt />
          <span class="text">{{ item.collaboratorName }}</span>
          <IconFont icon="close" @click="handleDelInviteItem(item.collaboratorKey)" :size="14" />
        </div>
      </div>

      <div class="flex-justify">
        <Checkbox v-model="isPopoMsg">发送POPO通知</Checkbox>
        <div>
          <GButton
            ghost
            size="small"
            @click="handleCancelInvite"
            class="btn-cancel"
          >
            取消
          </GButton>

          <GButton
            type="primary"
            size="small"
            @click="handleConfirmInvite"
            class="btn-confirm"
          >
            邀请
          </GButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import copy from 'copy-to-clipboard'
import PopoUserSelect from '../popo-user/popo-user-select.vue'
import PopoUserItem from '../popo-user/popo-user-item.vue'
import {
  getCollaborationList,
  getSearchedCollaborationList,
  getShareInfo,
  updateShareInfo,
  addCollaboration,
  updateCollaboration,
  delCollaboration
} from '@/api/cloud'

export default {
  name: 'ShareCont',
  components: {
    PopoUserSelect,
    PopoUserItem
  },
  props: {
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      step: 1,
      collaborationList: [],
      isPopoMsg: true,
      inviteList: [],
      invitePermission: this.item.permission,
      sharePermission: '',
      shareUrl: '',

      searchAction: getSearchedCollaborationList,

      linkShareOptions: [
        {
          label: '可阅读',
          value: 'READ_ONLY',
          show: '可阅读'
        },
        {
          label: '可编辑',
          value: 'READ_WRITE',
          show: '可编辑'
        }
      ]
    }
  },
  computed: {
    profileId() {
      return this.$bus.profile.id || ''
    },
    searchParams() {
      return {
        fileId: this.item.id
      }
    },
    fileCollaboratorId() {
      const { author } = this.item || {}
      const { collaboratorId } = author || {}
      return collaboratorId
    },
    linkText() {
      if (this.fileCollaboratorId === this.profileId) {
        if (this.sharePermission === 'READ_ONLY') {
          return '获得链接的员工只能查看文件'
        } else if (this.sharePermission === 'READ_WRITE') {
          return '获得链接的员工可以编辑文件'
        }
        return ''
      } else {
        return '获得链接的员工只能查看文件'
      }
    },
    currentSharePermissionInfo() {
      return this.linkShareOptions.filter(item => item.value === this.sharePermission)[0] || {}
    }
  },
  created() {
    this.step = 1
    this.initData()
  },
  methods: {
    async initData() {
      const list = await getCollaborationList({
        fileId: this.item.id
      })
      this.collaborationList = list.map((item) => {
        return {
          ...item,
          operate: this.getOpearate(item) // 获取操作权限
        }
      })
      const _shareInfo = await getShareInfo({
        fileId: this.item.id
      })
      this.sharePermission = _shareInfo.permission
      this.shareUrl = _shareInfo.shareUrl
    },
    handleShareClick(val) {
      const _sharePermission = this.sharePermission
      if (val !== this.sharePermission) {
        updateShareInfo({
          fileId: this.item.id,
          permission: val
        })
          .then((res) => {
            this.$CustomToast({
              type: 'success',
              duration: 2,
              content:
                '获得链接的员工' + (val === 'READ_ONLY' ? '可阅读' : '可编辑')
            })
            this.sharePermission = val
            this.shareUrl = res.shareUrl
            console.log(
              '🚀 ~ file: index.vue:242 ~ handleShareClick ~ res.shareUrl:',
              res.shareUrl
            )
          })
          .catch(() => {
            this.sharePermission = _sharePermission
          })
      }
      this.sharePermission = val
    },
    handleCopyShareUrl() {
      if (this.shareUrl) {
        if (copy(this.shareUrl)) {
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content: '复制成功'
          })
        }
      } else {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '复制失败'
        })
      }
    },
    handleItemClick(item) {
      if (this.step === 1) {
        this.step = 2
        this.inviteList = [item]
      } else {
        this.inviteList.findIndex(
          (value) => value.collaboratorKey === item.collaboratorKey
        ) === -1 && this.inviteList.push(item)
      }
    },
    handleDelInviteItem(key) {
      this.inviteList = this.inviteList.filter(
        (item) => item.collaboratorKey !== key
      )
      this.inviteList.length === 0 && this.handleCancelInvite()
    },
    handleReturn() {
      this.step === 2 && this.handleCancelInvite()
    },
    handleCancelInvite() {
      this.step = 1
      this.isPopoMsg = true
      this.inviteList = []
    },
    handleConfirmInvite() {
      addCollaboration({
        fileId: this.item.id,
        notify: this.isPopoMsg,
        collaborators: this.inviteList.map((item) => {
          return {
            ...item,
            permission: this.invitePermission
          }
        })
      }).then(() => {
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '邀请成功'
        })
        this.handleCancelInvite()
        this.initData()
      })
    },
    handleItemChange(cmd, item) {
      if (cmd === 'delete') {
        this.collaborationList = this.collaborationList.filter(
          (value) => value.collaboratorKey !== item.collaboratorKey
        )
      }
    },
    handlePermissionChange(val, userItem) {
      if (val === 'DELETE') {
        delCollaboration(userItem.id).then(() => {
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content: '已将成员从列表中删除!'
          })
          this.handleItemChange('delete', userItem)
        })
      } else {
        const data = {
          ...userItem,
          permission: val
        }
        updateCollaboration(data).then(() => {
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content:
              '权限已变更为' +
              (val === 'READ_WRITE' ? '编辑者' : '阅读者') +
              '!'
          })
          this.handleItemChange('update', data)
        })
      }
    },
    getOpearate(userItem) {
      const { author, permission } = this.item || {}
      const { collaboratorKey } = author || {}

      if (collaboratorKey === userItem.collaboratorKey) {
        return {
          text: '所有者'
        }
      } else if (
        permission === 'READ_ONLY' &&
        userItem.inviterId !== this.profileId
      ) {
        return {
          text: userItem.permission === 'READ_ONLY' ? '阅读者' : '编辑者'
        }
      } else if (
        permission === 'READ_ONLY' &&
        userItem.inviterId === this.profileId
      ) {
        return {
          text: '阅读者',
          options: [
            {
              label: '阅读者',
              value: 'READ_ONLY',
              show: '阅读者'
            },
            {
              label: '移出小组',
              value: 'DELETE',
              show: '',
              error: true
            }
          ]
        }
      } else {
        return {
          text: '编辑者',
          options: [
            {
              label: '编辑者',
              value: 'READ_WRITE',
              show: '编辑者'
            },
            {
              label: '阅读者',
              value: 'READ_ONLY',
              show: '阅读者'
            },
            {
              label: '移出小组',
              value: 'DELETE',
              show: '',
              error: true
            }
          ]
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.group-file-share {
  .title {
    display: inline-flex;
    align-items: center;
    font-size: 12px;
    font-weight: bold;
    line-height: 18px;

    i {
      font-weight: 400;
    }

    cursor: pointer;
  }

  .cont-wrap {
    &.step1 {
      margin: 0 -16px;

      .share-wrap {
        margin: 16px 0;

        :deep(.popo-user-item) {
          display: flex;
          align-items: center;
        }
      }
    }

    .link-wrap {
      position: relative;
      margin-top: 8px;
      padding: 16px 16px 0;
      font-size: 12px;
      // font-weight: 300;
      color: #868686;

      &::before {
        position: absolute;
        content: '';
        display: inline-block;
        height: 8px;
        background: rgba(0, 0, 0, 0.8);
        left: -1px;
        right: -1px;
        top: -8px;
      }

      .link-t {
        display: flex;
        padding-bottom: 8px;
        margin-bottom: 8px;
        border-bottom: 1px solid #636363;

        &.no-bottom {
          margin-bottom: 0;
          padding-bottom: 0;
          border-bottom: none;
        }

        &-l {
          flex: 1;

          &-text {
            font-size: 12px;
            font-weight: 600;
            line-height: 18px;
            color: #ffffff;
          }
        }

        &-r {
          width: auto;
          display: flex;
          align-items: center;
        }
      }
    }

    .invite-wrap {
      padding: 9px 0;
    }

    .flex-justify {
      display: flex;
      justify-content: space-between;
      font-size: 12px;

      .f-bold {
        font-weight: bold;
      }

      .btn-cancel,
      .btn-confirm {
        padding: 0 16px !important;
      }
    }

    .line-b {
      margin-top: 16px;
      padding-bottom: 8px;
      border-bottom: 1px solid #636363;
    }

    .link-b-select {
      .link-b-label {
        display: inline-flex;
        align-items: center;
        color: #ffffff;
      }

      .subtitle {
        color: #888683;
        // font-weight: 300;
        // color: rgba(255, 255, 255, 0.8);
        // color: rgba(255, 255, 255, 0.3);
      }

      &-box {
        width: 184px;
        color: #888683;

        .iconfont {
          color: #296dcb;
        }

        li {
          &:hover {
            background: #296dcb;
            color: #ffffff;

            .iconfont {
              color: #ffffff;
            }

            .subtitle {
              color: rgba(255, 255, 255, 0.8);
            }
          }
        }
      }
    }

    .invite-item {
      display: inline-flex;
      height: 28px;
      border-radius: 14px;
      border: 1px solid #3e3e3f;
      justify-content: space-between;
      align-items: center;
      overflow: hidden;
      margin-right: 8px;

      img {
        height: 22px;
        width: 22px;
        margin: 2px;
        border-radius: 13px;
      }

      .text {
        display: inline-block;
        margin: 0 8px;
        font-size: 12px;
      }

      .iconfont {
        margin-right: 8px;
        cursor: pointer;
      }
    }

    .btn-cancel {
      margin-right: 12px;
    }
  }
}
</style>
