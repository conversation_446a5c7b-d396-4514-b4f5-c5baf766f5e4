<!-- 退出小组转移成员 -->
<template>
  <Dialog
    v-model="showDialog"
    type="dialog"
    :subtitle="
      `你是&quot;${groupName}&quot;小组创建者，退出前请将组转让给其他成员`
    "
    :title="`确认退出&quot;${groupName}&quot;小组吗？`"
    okText="转让并退出"
    :ok-disabled="okDisabled"
    @ok="handleOK"
    @cancel="handleCancel"
  >
    <template v-slot:body>
      <div @click="showSelectedUser" class="user-box">
        <gInput value="请选择你要转移给的成员" size="large" disabled style="height: 50px">
          <PopoUserItem
            slot="value"
            :item="user"
            no-padding
            v-if="user.name"
            style="padding-left: 8px;"
          />
          <IconFont class="icon" slot="suffix" icon="down-arrow" :size="20" />
        </gInput>
      </div>
    </template>
  </Dialog>
</template>

<script>
import { transferOwner } from '@/api'
import PopoUserItem from './popo-user-item.vue'
import GroupMemberList from './group-member-list.vue'

export default {
  name: 'GroupTransferUser',
  components: {
    PopoUserItem,
    // eslint-disable-next-line vue/no-unused-components
    GroupMemberList
  },
  props: {
    groupId: [Number, String],
    groupName: String,
    ownerEmail: String,
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      user: {
        deptName: '',
        email: '',
        name: '',
        popoAvatar: '',
        userId: ''
      }
    }
  },
  model: {
    prop: 'show',
    event: 'change'
  },
  computed: {
    showDialog: {
      get() {
        return this.show
      },
      set(v) {
        this.$emit('change', v)
      }
    },
    okDisabled() {
      return !this.user.name
    }
  },
  methods: {
    showSelectedUser() {
      this.$CustomDialog({
        title: '选择组转移的成员',
        width: 400,
        contRender: (h, handleClose) => {
          return h(GroupMemberList, {
            props: {
              groupId: this.groupId,
              ownerEmail: this.ownerEmail
            },
            on: {
              'item-click': (item) => {
                this.user = item
                handleClose()
              }
            }
          })
        }
      })
    },
    async handleOK() {
      await transferOwner(this.groupId, this.user.userId)
      this.$emit('ok', this.data)
      this.handleCancel()
    },
    handleCancel() {
      this.user = {
        deptName: '',
        email: '',
        name: '',
        popoAvatar: '',
        userId: ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
.user-box {
  margin-bottom: 24px;
}
</style>
