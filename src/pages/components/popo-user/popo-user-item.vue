<template>
  <div
    class="popo-user-item"
    :class="{ disabled, 'no-padding': noPadding }"
    @click="onClick"
  >
    <div class="avatar-box">
      <div class="avatar-img">
        <img
          :src="
            item.popoAvatar || item.avatar || require('@/assets/img/avatar.png')
          "
        />
      </div>
      <div class="name-box">
        <div>{{ item.name || item.collaboratorName }}</div>
        <div class="text-subtitle">{{ item.deptName }}</div>
      </div>
    </div>
    <div class="operating-area">
      <slot />
    </div>
    <div>
      <slot name="right"/>
    </div>
  </div>
</template>
<script>
export default {
  name: 'PopoUserItem',
  props: {
    item: {
      type: Object,
      default: () => {},
    },
    disabled: Boolean,
    noPadding: Boolean,
  },
  methods: {
    onClick() {
      this.$emit('click', this.item)
    },
  },
}
</script>
<style lang="less" scoped>
.popo-user-item {
  display: flex;
  width: 100%;
  height: 48px;
  padding: 8px 24px;
  font-size: 12px;

  &.disabled {
    cursor: not-allowed;
  }

  &:hover {
    background-color: #474747;
  }

  &.no-padding {
    padding: 8px 0;
  }

  .avatar-box {
    flex: 1;
    display: flex;
    width: 100%;
    height: 100%;

    .avatar-img {
      height: 28px;
      margin: 3px 12px 0 0;
      border-radius: 50%;
      overflow: hidden;

      img {
        width: 28px;
        height: 28px;
        object-fit: cover;
        object-position: center;
        border-radius: 50%;
        z-index: 0;
      }
    }

    .name-box {
      flex: 1;
      width: 0;

      .text-subtitle {
        color: #919191;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        width: 100%;
      }
    }
  }

  .operating-area {
    visibility: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    line-height: 18px;
    margin-left: 20px;
  }

  &:hover {
    .operating-area {
      visibility: visible;
    }
  }
}
</style>
