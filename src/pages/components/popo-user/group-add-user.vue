<!-- 组增加成员 -->
<template>
  <div class="addUserModal" @click="closeMenu">
    <div class="header">
      <div class="tabs">
        <div
          class="tab"
          :class="{active: curTab === 'internal'}"
          @click="curTab='internal'"
        >
          <span>{{ isOwner ? '管理' : '添加' }}网易员工</span>
        </div>
        <div
          class="tab"
          :class="{active: curTab === 'external'}"
          @click="curTab='external'"
        >
          <span>添加外部员工</span>
        </div>
      </div>

      <img
        class="close"
        src="@/assets/img/close.png"
        @click="$emit('close')"
        alt=""
      />
    </div>

    <div v-show="curTab==='internal'" class="internal">
      <div class="title">{{ title }}</div>

      <!-- 用户搜索框 -->
      <PopoUserSelect
        class="search"
        @select="addGroupMember"
        :existsKeys="existsKeys"
        :searchAction="searchAction"
        :searchParams="searchParams"
        placeholder="添加POPO用户"
      />

      <div class="introduction">
        组成员有访问小组、邀请人员、修改小组名、下载和编辑组中文件的权限，不具有删除小组权限
      </div>

      <div class="bar" />

      <!-- 该组的成员列表 -->
      <div style="margin:8px -16px 0">
        <scroll-page
          :show-footer="false"
          ref="list-scroll-page"
          class="member-page"
          @scroll-bottom="getGroupMemberList"
          :loading="pageInfo.loading"
          :finished="pageInfo.finished"
          :list="memberList"
        >
          <template #list>
            <PopoUserItem :key="item.id" v-for="item in memberList" :item="item">
              <div v-if="item.email === ownerEmail">创建者</div>
              <IconFont
                v-else-if="$bus.profile.email === ownerEmail"
                icon="more"
                class="more-icon"
                :size="14"
                @click="(e) => showMenu(e, item)"
              />
            </PopoUserItem>
          </template>
        </scroll-page>
      </div>
      <!-- 右键菜单 -->
      <context-menu
        :show="menuShow"
        :position="menuPos"
        :menuBtns="menuBtns"
        @click="handleMenuClick"
        @close="closeMenu"
        style="width: auto;"
      >
      </context-menu>
    </div>

    <div v-show="curTab==='external'" class="external">
      <div class="content">
        <div class="email">
          <label>邮箱</label>
          <g-input
            class="emailInput"
            placeholder="请输入外部员工的POPO姓名"
            v-model="email"
          >
            <template #suffix>
              <IconFont
                v-if="email"
                icon="empty"
                @click="email=''"
              />
            </template>
          </g-input>
        </div>
      </div>

      <g-button
        class="submitBtn"
        type="primary"
        :disabled="!email || loading"
        @click="handleAddExternal"
      >
        确认添加
      </g-button>
    </div>
  </div>
</template>

<script>
import PopoUserSelect from './popo-user-select.vue'
import PopoUserItem from './popo-user-item.vue'
import { memberList, addMember, removeMember, searchPopoUsers } from '@/api'

export default {
  name: 'GroupAddUser',
  components: {
    PopoUserSelect,
    PopoUserItem
  },
  props: {
    groupId: [Number, String],
    ownerEmail: String,
    title: String
  },
  data() {
    return {
      curTab: 'internal', // internal-内部、external-外部
      memberList: [],
      pageInfo: {
        loading: false,
        finished: true,
        pageNum: 0
      },
      searchAction: searchPopoUsers,
      searchParams: {
        groupId: this.groupId
      },
      menuPos: {x: 0, y: 0},
      menuShow: false,
      currentUserItem: {},
      email: '',
      loading: false
    }
  },
  computed: {
    existsKeys() {
      return this.memberList.map((i) => i.email)
    },
    menuBtns() {
      return [
        {
          title: '移出小组',
          eventName: 'removeMember',
          className: 'warning',
          func: this.removeGroupMember
        }
      ]
    },
    isOwner() {
      const currentUser = this.$bus.profile.email
      return currentUser === this.ownerEmail
    }
  },
  methods: {
    showMenu(e, item) {
      e.stopPropagation()
      this.menuPos.x = e.clientX - 60
      this.menuPos.y = e.clientY + 16
      this.menuShow = true
      this.currentUserItem = item
    },
    closeMenu() {
      this.menuShow = false
    },
    handleMenuClick({func}) {
      func && func()
    },
    async addGroupMember(user) {
      const isExist = this.existsKeys.includes(user.email)
      if (!isExist) {
        const member = await addMember(this.groupId, user.email)
        this.memberList.splice(0, 0, member)
        this.$bus.$emit('groupUserChanged')
        this.$emit('groupUserChanged', this.memberList.length)
      }
    },
    async removeGroupMember() {
      try {
        await removeMember(this.groupId, this.currentUserItem.userId)
        this.memberList = this.memberList.filter(
          (item) => item.email !== this.currentUserItem.email
        )
        this.$bus.$emit('groupUserChanged')
        this.$emit('groupUserChanged', this.memberList.length)
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '移出成功！'
        })
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: e.message
        })
      }
    },
    async getGroupMemberList() {
      this.pageInfo.loading = true
      try {
        const {total, list} = await memberList(
          this.groupId,
          this.pageInfo.pageNum + 1
        )
        this.pageInfo.pageNum += 1
        this.memberList = [...this.memberList, ...list]
        if (this.memberList.length === total) {
          this.pageInfo.finished = true
        }
      } catch (error) {
      }

      this.pageInfo.loading = false
    },
    async handleAddExternal() {
      const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
      const isValidEmail = emailRegex.test(this.email)
      if (!isValidEmail) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '邮箱格式错误'
        })
        return
      }
      try {
        this.loading = true
        const member = await addMember(this.groupId, this.email)
        this.memberList.splice(0, 0, member)
        this.$bus.$emit('groupUserChanged')
        this.$emit('groupUserChanged', this.memberList.length)
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '添加成功！'
        })
        this.email = ''
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: e.message
        })
      } finally {
        this.loading = false
      }
    }
  },
  created() {
    this.getGroupMemberList()
  }
}
</script>

<style lang="less" scoped>
.addUserModal {
  width: 400px;
  height: 551px;
  border-radius: 3px;
  border: 1px solid #383838;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25);
  background-color: #535353;

  .header {
    display: flex;
    justify-content: space-between;
    height: 33px;
    padding-top: 4px;
    background-color: #4D4D4D;
    border-bottom: 1px solid #383838;

    .tabs {
      display: flex;

      .tab {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 29px;
        padding: 11px 12px;
        font-weight: bold;
        cursor: pointer;
        border-width: 1px 1px 0 1px;
        border-style: solid;
        border-color: #383838;
        color: #fff;

        span {
          opacity: 0.6;
        }
      }

      .active {
        border-bottom: none;
        background-color: #535353;

        span {
          opacity: 1;
        }
      }
    }

    .close {
      width: 20px;
      height: 20px;
      margin-right: 8px;
      margin-top: 2px;
    }
  }

  .internal {
    padding: 15px;

    .title {
      font-size: 12px;
      line-height: 18px;
      font-weight: normal;
      color: #ffffff;
    }

    .search {
      width: 100%;
      margin: 8px 0;
    }

    .introduction {
      margin-top: 5px;
      color: #ececec;
      font-size: 11px !important;
      width: 100%;
      line-height: 18px;
      margin-bottom: 8px;
    }

    .bar {
      border-bottom: 1px solid #636363;
      height: 0;
    }

    .member-page {
      overflow-x: hidden;
      max-height: 360px;
      overflow-y: auto;
      padding: 0;
    }

    .more-icon {
      cursor: pointer;
    }
  }

  .external {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 500px;
    padding: 15px;

    .content {
      width: 100%;
      flex: 1;
      padding-top: 16px;

      .email {
        label {
          font-size: 12px;
          font-weight: bold;
        }

        .emailInput {
          width: 368px;
          height: 40px;
          margin-top: 7px;
        }
      }
    }

    .submitBtn {
      padding: 6px 24px;
    }
  }
}
</style>
