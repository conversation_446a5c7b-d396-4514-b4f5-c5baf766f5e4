<template>
  <div class="popo-user-select">
    <gInput
      v-model="inputValue"
      :placeholder="placeholder"
      autocomplete="off"
      name="search"
      @input="handleChange"
      size="middel"
    />
    <div v-if="inputValue" class="select-dropdown">
      <ul
        v-show="!searchLoading && !searchList.length"
        class="select-not-found"
      >
        无匹配数据
      </ul>
      <ul
        class="select-dropdown-list"
        v-show="!searchLoading && searchList.length"
      >
        <li
          class="select-item"
          v-for="item in searchList"
          :key="item.email"
          @click="handelSelect(item)"
        >
          <PopoUserItem
            :item="item"
          >
            {{ existsKeys.includes(item.email) ? '已添加' : '添加' }}
          </PopoUserItem>
        </li>
      </ul>
      <ul v-show="searchLoading" class="select-loading">
        加载中
      </ul>
    </div>
  </div>
</template>
<script>
import PopoUserItem from './popo-user-item.vue'

export default {
  name: 'PopoUserSelect',
  components: {
    PopoUserItem
  },
  props: {
    placeholder: {
      type: String,
      default: '请输入POPO用户'
    },
    clearable: {
      type: Boolean,
      default: true
    },
    existsKeys: {
      type: Array,
      default: () => []
    },
    searchAction: {
      type: Function,
      default: () => () => {
      }
    },
    searchParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      inputValue: '',
      timer: null,
      searchLoading: false,

      searchList: []
    }
  },
  methods: {
    handleChange() {
      if (this.timer) clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.inputValue && this.handleSearch()
      }, 500)
    },
    handleSearch() {
      this.searchLoading = true
      this.searchList = []

      this.searchAction({
        ...this.searchParams,
        keyword: this.inputValue
      }).then((res) => {
        this.searchList = res || []
      })
        .catch(() => {
          this.searchList = []
        })
        .finally(() => {
          this.searchLoading = false
        })
    },
    handelSelect(item) {
      this.$emit('select', item)
      this.handleClear()
    },
    handleClear() {
      this.inputValue = ''
      this.searchList = []
    }
  }
}
</script>
<style lang="less" scoped>
.popo-user-select {
  display: block;
  position: relative;

  .select-dropdown {
    position: absolute;
    will-change: top, left;
    transform-origin: center top;
    width: 100%;
    top: 44px;
    left: 0;
    background: #535353;
    box-sizing: border-box;
    border: 1px solid #383838;
    border-radius: 3px;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
    z-index: 9;

    .select-dropdown-list {
      max-height: 200px;
      overflow-y: auto;
      padding: 6px 0;
      margin: 0;
      list-style: none;

      li {
        padding: 0;
        cursor: pointer;
      }
    }

    .select-not-found,
    .select-loading {
      padding: 0;
      list-style: none;
      text-align: center;
      color: #868686;
    }
  }
}

:deep(input::placeholder) {
  font-size: 12px;
}
</style>
