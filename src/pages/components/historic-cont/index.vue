<template>
  <div class="historic-cont">
    <div v-if="src && src.startsWith('http')" class="preview-wrap">
      <element-drag id="imgView" ref="img-pre" :radio="radio" :scaleZoom="scaleZoom">
        <img :src="src" alt />
      </element-drag>
    </div>

    <div v-else class="preview-wrap">
      <div class="preview-wrap-empty">
        <img :src="require('@/assets/img/svg/default.svg')" width="60" alt />
      </div>
    </div>

    <div class="historic-list">
      <historic-item
        v-for="(historicItem, index) in list"
        :activeId="activeId"
        :key="historicItem.historyId"
        :index="index"
        :item="historicItem"
        :permisiion="item.permission"
        @operate="handleOperate"
      />
    </div>

    <Dialog
      v-model="resetVisiable"
      type="warning"
      title="恢复后，该历史版本将作为最新版本显示"
      subtitle="确定将文件恢复到当前版本吗？"
      @ok="resetFileConfirm(resetItemObj)"
    />
  </div>
</template>

<script>
import HistoricItem from './historic-item.vue'
import { getFileInfoByUrl } from '@/utils'
import { putDriveFileRecover } from '@/api/cloud'

export default {
  name: 'HistoricCont',
  components: {
    HistoricItem
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    item: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      src: '',
      activeId: '',
      resetVisiable: false,
      resetItemObj: null,
      radio: 1,
      scaleZoom: {
        max: 5,
        min: 0.2
      }
    }
  },
  created() {
    this.src = this.list[0].cover
    this.activeId = this.list[0].historyId
    this.$nextTick(() => {
      this.getRadio()
    })
  },
  methods: {
    handleOperate(cmd, item) {
      switch (cmd) {
        case 'active':
          this.src = item.cover
          this.$nextTick(() => {
            this.getRadio(item.cover)
            this.activeId = item.historyId
          })
          // this.$refs['img-pre'].reset()
          // this.getRadio(item.cover)
          break
        case 'reset':
          this.handleReset(item)
          break
        case 'open':
          this.$emit('operate', cmd, item)
          break
        default:
          break
      }
    },
    handleReset(item) {
      this.resetItemObj = item
      this.resetVisiable = true
    },
    getRadio(imgUrl) {
      if (!document.getElementById('imgView')) return
      const imgViewWidth = document.getElementById('imgView').clientWidth
      const imgViewHeight = document.getElementById('imgView').clientHeight
      getFileInfoByUrl(imgUrl || this.item.cover).then(res => {
        if (imgViewWidth / imgViewHeight > res.Width / res.Height) {
          this.radio = parseFloat((imgViewHeight / res.Height).toFixed(2))
        } else {
          this.radio = parseFloat((imgViewWidth / res.Width).toFixed(2))
        }
        imgUrl && this.$refs['img-pre'].reset(this.radio)
        this.scaleZoom.max = parseFloat((this.radio * 5).toFixed(2))
        this.scaleZoom.min = parseFloat((this.radio / 5).toFixed(2))
        this.height = res.Height
        this.width = res.Width
      }).catch(() => {
        this.height = 'auto'
        this.width = 'auto'
      })
    },
    async resetFileConfirm() {
      const item = this.resetItemObj
      try {
        await putDriveFileRecover({ historyId: item.historyId })
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '恢复成功'
        })
        this.$emit('reset', item)
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: e.message
        })
      } finally {
        this.resetItemObj = null
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.getRadio(this.src)
    })
  }
}
</script>

<style lang="less" scoped>
.historic-cont {
  .preview-wrap {
    width: 100%;
    height: 205px;
    margin-bottom: 12px;

    &-empty {
      width: 100%;
      height: 100%;
      background: #424242;
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .historic-list {
    margin: 0 -16px;
    max-height: 240px;
    overflow-y: auto;
  }

  :deep(.dialog-confirm-text-title) {
    margin-top: 4px !important;
    margin-bottom: 0 !important;
    font-size: 12px !important;
    opacity: 0.6;
  }

  :deep(.dialog-confirm-text-subtitle) {
    margin-top: 24px !important;
    margin-bottom: 0 !important;
    font-size: 14px !important;
  }
}
</style>
