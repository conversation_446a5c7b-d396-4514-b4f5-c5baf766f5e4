<template>
  <div
    class="historic-item"
    :class="{ active: activeId === item.historyId }"
    @click.stop="handleClick"
  >
    <div
      class="item-img spectrum-Thumbnail spectrum-Thumbnail--sizeM spectrum-Thumbnail--cover"
      @click="$openPopo(item.lastUpdater.collaboratorKey)"
    >
      <img
        class="spectrum-Thumbnail-image"
        :src="item.lastUpdater && item.lastUpdater.avatar"
      />
    </div>
    <div class="item-center">
      <div>{{ item.lastUpdater && item.lastUpdater.collaboratorName }}</div>
      <div class="text-subtitle" :class="{ loading: isLoading }">
        {{
          isLoading
            ? downloadLength && contentLength
              ? `正在下载 ${$renderSize(downloadLength)}/${$renderSize(
                contentLength
              )}`
              : `正在下载 0 / ${$renderSize(contentLength)}`
            : '编辑了文件'
        }}
      </div>
    </div>
    <div class="item-right">
      <div>{{ item.updateTime }}</div>
      <div class="oparate-btns" :class="{ noClick: permisiion === 'READ_ONLY', active: activeId === item.historyId }">
        <span
          v-if="$bus.isWSInit"
          :style="{ visibility: isLoading ? 'hidden' : 'initial' }"
          @click.stop="handleOpenFile"
        >
          在Ps中打开
        </span>
        <span
          :style="{ visibility: index === 0 ? 'hidden' : 'initial' }"
          @click.stop="handleReset"
        >
          恢复
        </span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'HistoricItem',
  props: {
    activeId: {
      type: String,
      default: ''
    },
    item: {
      type: Object,
      default: () => ({})
    },
    index: {
      type: Number,
      required: true
    },
    permisiion: {
      type: String,
      default: ''
    }
  },
  computed: {
    isLoading() {
      return (
        this.$bus.downloadFilesList.findIndex(
          i => i.file_key === this.item.fileKey
        ) > -1
      )
    },
    process() {
      return this.isLoading
        ? this.$bus.downloadFilesList.find(
          i => i.file_key === this.item.fileKey
        ).process
        : 0
    },
    downloadLength() {
      return this.isLoading
        ? this.$bus.downloadFilesList.find(
          i => i.file_key === this.item.fileKey
        ).download_length
        : 0
    },
    contentLength() {
      return this.isLoading
        ? this.$bus.downloadFilesList.find(
          i => i.file_key === this.item.fileKey
        ).content_length
        : 0
    }
  },
  methods: {
    handleOpenFile() {
      if (this.permisiion === 'READ_ONLY') {
        return this.$CustomToast({
          type: 'warning',
          duration: 2,
          content: '无权限编辑该文件'
        })
      }
      this.$emit('operate', 'open', this.item)
    },
    handleReset() {
      if (this.permisiion === 'READ_ONLY') {
        return this.$CustomToast({
          type: 'warning',
          duration: 2,
          content: '无权限恢复该文件'
        })
      }
      this.$emit('operate', 'reset', this.item)
    },
    handleClick() {
      this.$emit('operate', 'active', this.item)
    }
  }
}
</script>

<style lang="less" scoped>
.historic-item {
  display: flex;
  width: 100%;
  height: 48px;
  padding: 8px 24px;
  font-size: 12px;

  &:hover {
    background-color: #474747;

    .item-right {
      .oparate-btns {
        display: flex;
      }
    }
  }

  &.active {
    background-color: #6b6b6b;
  }

  .item-img {
    width: auto;
    width: 28px;
    height: 28px;
    margin: 3px 12px 0 0;
    border-radius: 50%;
    overflow: hidden;
    cursor: pointer;
  }

  .item-center {
    flex: 1;
    width: 0;

    .text-subtitle {
      color: #919191;
      // font-weight: 300;
      &.loading {
        color: #ffffff;
      }
    }
  }

  .item-right {
    width: auto;
    min-width: 100px;
    // font-weight: 300;
    .oparate-btns {
      display: none;
      font-weight: 500;
      color: #4e91f3;
      justify-content: space-between;

      span {
        cursor: pointer;
      }

      &.noClick {
        color: #919191;
      }

      // &.active {
      //   display: flex;
      // }
    }
  }
}

.spectrum-Thumbnail-image {
  width: 28px;
  height: 28px;
  object-fit: cover;
  object-position: center;
  border-radius: 50%;
  z-index: 0;
}
</style>
