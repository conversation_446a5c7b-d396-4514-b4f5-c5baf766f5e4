<template>
  <layout>
    <div class="page">
      <nav-bar title="以图搜图" />

      <div class="content">
        <div class="header">
          <div class="imageContainer">
            <img
              v-if="imgUrl"
              :key="retryKey"
              :src="imgUrl"
              alt=""
              crossOrigin="anonymous"
              @error="onImgError"
              @load="onImgLoad"
            />
          </div>
        </div>

        <scroll-page
          :finished="pageInfo.finished"
          :list="dataList"
          :loading="pageInfo.loading"
          class="list"
          @scroll-bottom="handleBottom"
        >
          <template #header>
            <div class="title">搜索结果</div>
          </template>

          <template v-slot:list="{list}">
            <waterfall :dataList="list" :scale="10">
              <template v-slot:default="{item}">
                <MaterialCard :item="item" :scale="10" />
              </template>
            </waterfall>
          </template>
        </scroll-page>
      </div>
    </div>
  </layout>
</template>

<script>
import NavBar from '@/components/layouts/nav-bar/index.vue'
import AutoInput from '@/components/search-box/auto-input.vue'
import { searchImgByImg } from '@/api'
import Waterfall from '@/components/waterfall/index.vue'
import MaterialCard from '@/pages/game/components/material-card.vue'

export default {
  name: 'SearchImg',
  components: { MaterialCard, Waterfall, AutoInput, NavBar },
  data() {
    return {
      listInfo: {
        total: null
      },
      dataList: [],
      detailId: null,
      detailVisible: false,
      accept: ['psd', 'psb', 'jpg', 'jpeg', 'png', 'gif', 'crw', 'tiff', 'ai', 'bmp'],
      url: '',
      retryCount: 0,
      retryKey: 0,
      maxRetry: 5,
      retryDelay: 1000,
      imgUrl: '',
      finalUrl: '',
      imageLoadError: false,
      pageInfo: {
        loading: false,
        finished: false,
        total: 0,
        limit: 200
      }
    }
  },
  watch: {
    '$route.query': {
      immediate: true,
      handler(nv) {
        const { bucket, key } = nv
        if (key && bucket) {
          const url = `https://${bucket}.nos-jd.163yun.com/${key}`
          this.startLoad(url)
          this.searchImg(url)
        }
      }
    }
  },
  computed: {},
  methods: {
    handleBottom() {
      const { bucket, key } = this.$route.query
      if (key && bucket) {
        const url = `https://${bucket}.nos-jd.163yun.com/${key}`
        this.startLoad(url)
        this.searchImg(url)
      }
    },
    async startLoad(url) {
      this.retryCount = 0
      this.finalUrl = url
      this.imgUrl = url
      this.retryKey = Date.now() // 强制刷新 img
    },
    onImgLoad() {
      this.retryCount = 0
      this.imageLoadError = false
    },
    onImgError() {
      if (this.retryCount < this.maxRetry) {
        this.retryCount++
        setTimeout(() => {
          this.retryKey = Date.now()
          this.imgUrl = this.finalUrl + '?_t=' + this.retryKey
        }, this.retryDelay)
      } else {
        this.imageLoadError = true
      }
    },
    clearListInfo() {
      this.dataList = []
      this.listInfo.total = null
    },
    async searchImg(url) {
      if (!url) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '未找到图片，请重新上传'
        })
        return
      }
      try {
        this.pageInfo.loading = true
        this.clearListInfo()
        const { total, list } = await searchImgByImg({
          thumbnailUrl: url
        })
        if (!total) {
          this.$CustomToast({
            type: 'error',
            duration: 2,
            content: '未搜索到相关图片'
          })
        }
        this.listInfo.total = total || 0
        this.dataList = list || []
        this.pageInfo.finished = true
      } finally {
        this.pageInfo.loading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.page {
  border-radius: 4px;
  overflow: hidden;
  height: 100%;

  .content {
    padding: 4px;
    border: 0.5px solid #383838;
    background-color: #4D4D4D;
    height: 100%;

    .header {
      width: 100%;
      height: 90px;
      padding: 8px;
      box-shadow: 0 0.5px 0 0 #383838;

      .imageContainer {
        width: 100%;
        height: 100%;

        img {
          object-fit: contain;
          border-radius: 8px;
          overflow: hidden;
          max-width: 100%;
          max-height: 100%;
        }
      }
    }

    .list {
      height: 100%;

      .title {
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
}
</style>