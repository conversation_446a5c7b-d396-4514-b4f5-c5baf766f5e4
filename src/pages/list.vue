<template>
  <layout>
    <div class="m-list">
      <search-box
        :isIndex="true"
        :reqTime="reqTime"
        :reqTimeLabelName="reqTimeLabelName"
        :reqTimeOptions="reqTimeOptions"
        :searchTime="searchTime"
        @changeSearchTime="changeSearchTime"
        @change="handleChange"
        @handleReqTimeChange="handleReqTimeChange"
      />

      <div class="head-bar">
        <div class="left-box">{{ pageInfo.total }}个搜索结果</div>
        <div class="right-box">
          <div v-show="skipWords.length" class="skipWords">
            <img src="@/assets/img/closeWithBorder.png" :width="12" alt="" @click="clearSkipWords">
            <div class="words">
              屏蔽词汇：
              <span v-for="(word, index) in skipWords" :key="index">
                  <span>{{ word }}</span>
                  <span v-if="index !== skipWords.length - 1">、</span>
                </span>
            </div>
            <div v-show="skipWords.length" class="division" />
          </div>

          <Tooltip placement="bottom" theme="dark">
            <div style="display: flex; align-items: center;margin-right: 12px; cursor: pointer;">
              <div>屏蔽词汇</div>
              <IconFont icon="tuxiangtiaozheng" :size="14" style="vertical-align: -3px;" />
            </div>

            <div slot="content">
              <g-input
                class="skipWordInput"
                :limit="15"
                v-model="skipWord"
                placeholder="输入你想屏蔽的关键词"
                @enter="addSkipWord"
              />
            </div>
          </Tooltip>

          <div class="star-box" @click="onStar">
            <div>星标素材</div>
            <IconFont
              class="star-icon"
              :icon="isStar ? 'star' : 'unstar'"
              :class="isStar ? 'star-light' : ''"
              :size="10"
            />
          </div>

          <Tooltip placement="bottom-start" theme="dark">
            <div style="display: flex; align-items: center; margin: 0 12px; cursor: pointer;">
              <span>图像大小</span>
              <img src="@/assets/img/scale.png" :width="11" alt />
            </div>

            <div slot="content" class="scale-box">
              <IconFont icon="reduce" class="reduce" :size="10" @click="scale-=5" />
              <Slider v-model="scale" class="slider-scale" />
              <IconFont icon="add" class="add" :size="10" @click="scale+=5" />
            </div>
          </Tooltip>
        </div>
      </div>

      <scroll-page
        ref="list-scroll-page"
        class="m-page"
        :loading="pageInfo.loading"
        :finished="pageInfo.finished"
        :list="dataList"
        @scroll-bottom="handleBottom"
      >
        <template #header>
          <div v-show="showTips" class="tips">
            <img src="@/assets/img/warn-tips.png" alt="" />
            <div class="text">
              如资源为外宣所需（非项目内部使用），为避免使用到临时资源或侵权资源，请获得GUI同学书面确认后再使用，如未经书面确认外发导致舆情风险或侵权风险，后果自负。（请加入POPO群进行确认，群号：
              <span @click="handlePopoOpen('6846867', '1')">6846867</span>
              ，或联系GUI接口人
              <span @click="handlePopoOpen('<EMAIL>', '0')">黄聃彦</span>
              ）
            </div>
          </div>
        </template>

        <template v-slot:list="slotProps">
          <waterfall
            :dataList="slotProps.list"
            :scale="scale"
            @item-click="handleItemClick"
            @refresh="refresh"
          />
        </template>
      </scroll-page>
    </div>
  </layout>
</template>

<script>
import SearchBox from '@/components/search-box'
import Waterfall from '@/components/waterfall'
import { getResourceSearch } from '@/api/index.js'
import dayjs from 'dayjs'

export default {
  name: 'List',
  components: {
    SearchBox,
    Waterfall
  },
  data() {
    return {
      isStar: false,
      pageInfo: {
        loading: false,
        finished: false,
        total: 0,
        limit: 200
      },
      dataList: [],
      reqTime: 'DEFAULT',
      reqTimeOptions: [
        {
          id: 'DEFAULT',
          name: '综合排序'
        },
        {
          id: 'MOST_FREQUENTLY_DOWNLOAD',
          name: '最常下载'
        },
        {
          id: 'RECENTLY_DOWNLOAD',
          name: '最近下载'
        },
        {
          id: 'UPLOAD_TIME_ASC',
          name: '按时间排序'
        },
        {
          id: 'UPLOAD_TIME_DESC',
          name: '按时间倒排序'
        }
      ],
      scale: 10,
      searchTime: ['', ''],
      skipWord: '',
      skipWords: []
    }
  },
  computed: {
    searchParams() {
      return this.$bus.searchParams
    },
    reqTimeLabelName() {
      return this.reqTimeOptions.find(item => item.id === this.reqTime)?.name
    },
    showTips() {
      if (!this.$bus.projectSelectedId) return false
      const showTipGroups = ['6946513938675437568']
      return showTipGroups.includes(this.$bus.projectSelectedId)
    }
  },
  watch: {
    '$route.meta.scrollTop'(val) {
      if (val) {
        this.$refs['list-scroll-page'].setScrollTop(val)
      }
    },
    '$bus.list'(nv) {
      this.dataList = nv
    },
    '$route.query.refresh': {
      deep: true,
      async handler(val) {
        if (!val) return
        this.$CustomToast({
          type: 'info',
          duration: 2,
          content: '正在刷新列表，请稍后'
        })
        setTimeout(async () => {
          this.initPageInfo()
          await this.getData()
        }, 1500)
      }
    }
  },
  created() {
    this.onSearch()
    this.$bus.setProjectList()
  },
  methods: {
    async onStar() {
      try {
        this.isStar = !this.isStar
        this.$bus.setSearchValue('starSelected', this.isStar)
        this.initPageInfo()
        await this.getData()
      } catch (e) {
        this.isStar = !this.isStar
      }
    },
    refresh(id) {
      this.initPageInfo()
      this.getData()
    },
    handleItemClick(item) {
      this.$logMsg.info('[list][handleItemClick]' + item.id, '素材库')
      this.$bus.setDetailData(item)
      this.$router.push({
        name: 'detail',
        params: {
          id: item.id
        }
      })
    },
    handleChange() {
      this.$logMsg.info('[list][handleChange] 搜索', '素材库')
      this.reqTime = 'DEFAULT'
      this.$bus.setSearchValue('reqTime', 'DEFAULT')
      this.initPageInfo()
      this.getData()
    },
    onSearch() {
      this.$bus.$on('search', () => {
        this.handleChange()
      })
    },
    // 情况page的搜索条件
    initPageInfo() {
      this.pageInfo.loading = false
      this.pageInfo.finished = false
      this.pageInfo.total = 0
      this.dataList = []
    },
    getTagSearchParamByKeyname(keyName) {
      const paramsObj = this.searchParams
      const value = []
      for (const key in paramsObj) {
        if (key.startsWith(keyName)) {
          value.push(paramsObj[key])
        }
      }
      return value
    },
    handleReqTimeChange(id) {
      this.reqTime = id
      this.$bus.setSearchValue('reqTime', id)
      this.initPageInfo()
      this.getData()
    },
    async getData() {
      try {
        this.$logMsg.info('[list][getData] 开始获取数据', '素材库')
        this.pageInfo.loading = true
        const params = {
          limit: this.pageInfo.limit,
          keyword: this.searchParams.tags.join(' '),
          filterTagIds: this.getTagSearchParamByKeyname('inTagIds'),
          tags: this.searchParams.tags,
          colorRange: this.searchParams.colorRange,
          projectId: this.searchParams.projectId,
          sort: this.searchParams.reqTime, // 排序规则
          reqTime: 'ALL', // 时间范围所有
          lastId: this.dataList[this.dataList.length - 1]?.id || null,
          lastScore: this.dataList[this.dataList.length - 1]?.score || null,
          lastDownloadCount: this.dataList[this.dataList.length - 1]?.downloadCount || null,
          lastUploadTime: this.dataList[this.dataList.length - 1]?.uploadTime || null,
          lastUpdateTime: this.dataList[this.dataList.length - 1]?.updateTime || null,
          starSelected: this.isStar ? true : null,
          startTime: this.searchTime[0],
          endTime: this.searchTime[1],
          skipWords: this.skipWords
        }
        const d = await getResourceSearch(params)
        this.dataList = [...this.dataList, ...d.list]
        this.pageInfo.total = d.total
        if (d.list.length < params.limit) {
          this.pageInfo.finished = true
        }
        this.$bus.setList(this.dataList)
        this.$logMsg.info('[list][getData] 获取成功', '素材库')
      } catch (e) {
        this.$logMsg.error('[list][getData] 获取失败:' + e.message, '素材库')
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: e.message
        })
      } finally {
        this.pageInfo.loading = false
      }
    },
    // 加载更多
    handleBottom() {
      this.getData()
    },
    async changeSearchTime(time) {
      if (!time[0] && !time[1]) {
        this.searchTime = ['', '']
        this.initPageInfo()
        await this.getData()
        return
      }
      const start = dayjs(time[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
      const end = dayjs(time[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
      this.searchTime = [start, end]
      this.initPageInfo()
      await this.getData()
    },
    async addSkipWord(e) {
      const skipWord = e.target.value
      if (!skipWord) return this.$CustomToast({
        type: 'error',
        duration: 2,
        content: '关键词不能为空'
      })
      if (this.skipWords.includes(skipWord)) return this.$CustomToast({
        type: 'error',
        duration: 2,
        content: `关键词中已包含 ${skipWord}`
      })
      this.skipWords.push(skipWord)
      this.skipWord = ''
      this.$CustomToast({
        type: 'success',
        duration: 2,
        content: '添加成功'
      })
      this.initPageInfo()
      await this.getData()
    },
    async clearSkipWords() {
      this.skipWords = []
      this.$CustomToast({
        type: 'success',
        duration: 2,
        content: '清除成功'
      })
      this.initPageInfo()
      await this.getData()
    },
    handlePopoOpen(ssid, sstp) {
      this.$csInterface.openURLInDefaultBrowser(`http://popo.netease.com/static/html/open_popo.html?ssid=${ssid}&sstp=${sstp}`)
    }
  },
  beforeRouteLeave(to, from, next) {
    from.meta.scrollTop = this.$refs['list-scroll-page'].getScrollTop()
    next()
  }
}
</script>
<style lang="less" scoped>
.tips {
  display: inline-block;
  width: 120%;
  padding-bottom: 6px;
  transform: scale(0.83);
  transform-origin: left center;

  img {
    display: inline;
    width: 16px;
    height: 16px;
    transform: translateY(4px);
  }

  .text {
    display: inline;
    height: 20px;
    font-size: 12px;
    line-height: 18px;
    color: #A7A7A7;


    span {
      font-weight: bold;
      cursor: pointer;

      &:hover {
        color: #eee;
        text-decoration: underline;
      }
    }
  }
}

.m-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.m-page {
  border-top-left-radius: 4px;
  background-color: #4d4d4d;
  border-left: 1px solid #383838;
  border-top: 1px solid #383838;
  padding-top: 0;
}

.head-bar {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2px 8px;

  .right-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .skipWords {
      position: relative;
      display: flex;
      align-items: center;

      &:hover img {
        display: block;
      }

      img {
        position: absolute;
        top: -5px;
        left: -8px;
        display: none;
        cursor: pointer;
      }

      .words {
        display: inline-block;
        max-width: 166px;
        white-space: nowrap;
        overflow-x: hidden;
        text-overflow: ellipsis;
      }

      .division {
        width: 1px;
        height: 12px;
        margin: 0 8px;
        background-color: #fff;
      }
    }

    :deep(.tooltip-inner) {
      padding: 0;

      .skipWordInput {
        width: 320px;
        height: 32px;
        border-radius: 4px;
        border: 1px solid #636363;
        background: #474747;

        input {
          font-size: 12px;
        }
      }
    }

    .star-box {
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    :deep(.tooltip-popper) {
      transform: translateX(-50px);
    }

    .scale-box {
      display: flex;
      align-items: center;
      padding: 6px;

      .reduce,
      .add {
        cursor: pointer;

        &:hover {
          color: #1c7aec;
        }
      }
    }

    .slider-scale {
      width: 144px;
      margin: 0 8px;
    }
  }
}
</style>
