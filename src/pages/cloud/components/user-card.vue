<template>
  <div class="user">
    <div style="display: flex;width: 100%">
      <div class="info">
        <avatar :src="user.popoAvatar" :height="28" :width="28" />
        <div class="user-name">{{ user.name }}</div>
        <div style="color: #868686">参与编辑文件{{ user.totalFiles }}个</div>
      </div>
      <div class="thumbs">
        <div class="thumb" v-for="file in files" :key="file.id">
          <img
            :src="file.cover"
            style="width: 100%;height:100%;object-fit: cover"
            @click.stop="imgClick(file)"
          />
          <div class="thumb-mask">
            <div class="thumb-mask-text">{{ file.name }}</div>
            <IconFont
              class="location-item"
              icon="location"
              :size="16"
              @click.prevent="(e) => handleClickPosition(e, file.groupId)"
            />
          </div>
        </div>
        <template v-if="!files.length">
          <div class="thumb" v-for="index in 3" :key="index">
            <div
              class="no-thumb"
              :style="{ backgroundColor: `${index === 1 ? '#434343' : ''}` }"
            >
              <span>{{ index === 1 ? '暂无文件' : '' }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div
      style="margin: 20px 0 4px 0;cursor: pointer;"
      v-if="hasNextPage"
      @click="userMore"
    >
      查看更多
      <IconFont
        icon="viewmore"
        style="margin-left:4px;vertical-align: top;margin-top: 2px;"
      />
    </div>
    <div
      style="margin: 20px 0 4px 0;cursor: pointer;display: flex;justify-content:center"
      v-if="!hasNextPage && useUserMore"
      @click="userPackUp"
    >
      收起
      <IconFont
        icon="viewmore"
        style="margin-left:4px;vertical-align: top;margin-top: 4px;transform:rotate(180deg);"
      />
    </div>
  </div>
</template>

<script>
import { memberList, searchUserMore } from '@/api'
import FilePreview from '@/pages/components/file-preview/index.vue'

export default {
  name: 'user-card',
  props: {
    user: {
      type: Object,
    },
  },
  data() {
    return {
      files: this.user.files,
      pageNum: 0,
      hasNextPage: this.user.hasNextPage,
      useUserMore: false,
    }
  },
  methods: {
    async userMore() {
      this.useUserMore = true
      const res = await searchUserMore(this.user.id, this.pageNum + 1)
      if (this.pageNum === 0) {
        this.files = res.list
      } else {
        this.files = [...this.files, ...res.list]
      }
      if (this.files.length >= res.total) {
        this.hasNextPage = false
        return
      }
      this.pageNum += 1
    },
    async handleClickPosition(event, groupId) {
      event.stopPropagation()
      let members = []
      let memberPage = 0
      while (true) {
        memberPage += 1
        const res = await memberList(groupId, memberPage)
        members = [...members, ...res.list]
        if (res.total >= members.length) {
          break
        }
      }
      const inGroup = members.filter((user) => {
        return this.$bus.profile.email === user.email
      })
      if (inGroup.length > 0) {
        await this.$router.push({
          name: 'group',
          params: { gid: groupId },
        })
      } else {
        await this.$router.push({
          name: 'group',
          params: { gid: this.$bus.shareWithMe },
        })
      }
    },
    userPackUp() {
      this.pageNum = 0
      this.hasNextPage = true
      this.useUserMore = false
      this.files = this.files.splice(0, 6)
    },
    imgClick(item) {
      this.$CustomDialog({
        width: 'calc(100% - 80px)',
        style: {
          height: 'calc(100% - 160px)',
          margin: '0 0 16px',
        },
        custom: true,
        contRender: (h) => {
          return h(FilePreview, {
            props: {
              item,
              showGoto: true,
            },
          })
        },
      })
    },
  },
}
</script>

<style lang="less" scoped>
.user {
  display: flex;
  min-height: 153px;
  width: 100%;
  padding: 16px 12px;
  background-color: #4d4d4d;
  border-bottom: 1px solid #424242;
  flex-direction: column;
  align-items: center;

  &:hover {
    background-color: #575757;
  }

  .info {
    min-width: 140px;

    .user-name {
      font-size: 12px;
      line-height: 14px;
      margin: 8px 0 4px 0;
    }
  }
  .thumbs {
    display: inline-grid;
    grid-template-columns: 33% 33% 33%;
    grid-row-gap: 12px;
    grid-column-gap: 12px;
    padding: 0 16px 0 16px;

    .thumb {
      height: 130px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 4px;
      overflow: hidden;
      position: relative;

      .thumb-mask {
        width: 100%;
        height: 32px;
        background: rgba(0, 0, 0, 0.6);
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-left: 8px;
        visibility: hidden;

        .thumb-mask-text {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          display: inline-block;
        }

        .location-item {
          margin: 0 10px 0 2px;
          color: #ffffff;
          cursor: pointer;
        }
      }
      &:hover .thumb-mask {
        visibility: visible;
      }
    }

    .no-thumb {
      color: #989898;
      width: 800px;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      span {
        text-align: center;
        width: 73%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        display: inline-block;
      }
    }
  }
}
</style>
