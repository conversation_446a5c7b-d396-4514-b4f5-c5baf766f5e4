<template>
  <div
    class="group"
    :class="currentGroupId === data.id ? 'group-active' : ''"
    ref="group"
    @mouseover="onMouseover"
    @mouseleave="onMouseleave"
  >
    <div class="info">
      <div class="info__title">
        <div v-if="data.projectId && data.projectId !== '0'" class="designator">项目协作组</div>
        <div>{{ groupName }}</div>
      </div>

      <div class="info__footer">
        <div
          class="info__avatars"
          v-if="groupName !== '与我共享' && !isSimple"
        >
          <Avatar
            v-for="item in members"
            :key="item.id"
            :hover-text="item.name"
            :src="item.popoAvatar"
            @avatarClick="addGroupMember"
          />

          <Avatar
            v-if="data.projectId === '0'"
            hover-text="成员管理"
            :src="membersManageIcon"
            @avatarClick="addGroupMember"
          />

          <dropdown
            v-if="data.memberCount > 4 && data.projectId && data.projectId !== '0'"
            class="popoUsers"
            trigger="hover"
          >
            <div class="memberCount" @mouseenter="getMembers">
              {{ data.memberCount }}
            </div>

            <template #list>
              <Avatar
                v-for="item in totalMembers"
                :key="item.userId"
                style="margin: 0 4px 4px 0"
                :hover-text="item.name"
                :src="item.popoAvatar"
              />
            </template>
          </dropdown>
        </div>

        <div class="info__actions">
          <div class="info__actions__file-num">
            <IconFont
              v-if="groupName !== '与我共享'"
              @click="onStar"
              class="star-icon"
              :icon="this.isStarred ? 'star' : 'unstar'"
              :class="this.isStarred ? 'star-light' : ''"
              size="14"
            />
            <span>{{ data.fileCount }} 个文件</span>
          </div>

          <span
            v-if="groupName !== '与我共享' && !isSimple && data.projectId === '0'"
            class="more-icon"
            @click.stop="showContextMenu"
            :style="{
              visibility:
                currentGroupId === data.id || this.isHover ? 'visible' : '',
            }"
          >
            <IconFont icon="more" size="18" />
          </span>
        </div>
      </div>
    </div>

    <div class="thumbs">
      <div v-if="!files.length" class="no-thumb">还没有作品</div>

      <div class="thumb" v-for="file in files" :key="file.id">
        <ImgViewer
          :src="file.cover"
          :imgName="file.name"
          :style="{objectFit: 'cover'}"
          thumbnail
        />
      </div>

      <template v-if="files.length && files.length < column">
        <div class="thumb" v-for="index in column - files.length" :key="index">
          <img :src="require('@/assets/img/svg/default.svg')" alt="" />
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { memberList, setStar } from '@/api'
import membersManageIcon from '@/assets/img/members_manage.png'

export default {
  props: {
    data: {
      type: Object
    },
    windowWidth: {
      type: Number
    },
    isSimple: {
      type: Boolean,
      default: false
    },
    currentGroupId: {
      type: [Number, String]
    }
  },
  data() {
    return {
      membersManageIcon,
      isHover: false,
      isStarred: !!this.data.isStarred,
      totalMembers: []
    }
  },
  computed: {
    groupName() {
      return this.data.name
    },
    members: {
      get() {
        return (this.data.members || []).slice(0, 4)
      }
    },
    column() {
      return Math.floor(this.windowWidth / 260)
    },
    files() {
      return (this.data.files || []).slice(0, this.column)
    }
  },
  methods: {
    showContextMenu(e) {
      this.$emit('showMenu', {
        x: e.clientX + 20,
        y: e.clientY - 150,
        item: this.data
      })
    },
    showProjectGroupMembers(e) {
      this.$emit('showProjectGroupMembers', {
        x: e.clientX + 20,
        y: e.clientY - 150,
        item: this.data
      })
    },
    addGroupMember() {
      this.$emit('addMember', { item: this.data })
    },
    async onStar() {
      if (this.groupName === '与我共享' || this.isSimple) {
        return
      }
      this.isStarred = !this.isStarred
      await setStar(this.data.id, this.isStarred)
      this.$emit('star', { item: this.data, isStarred: this.isStarred })
    },
    onMouseover() {
      this.isHover = true
    },
    onMouseleave() {
      // this.isHover = false
    },
    async getMembers() {
      const { list } = await memberList(this.data.id, 1)
      this.totalMembers = list
    }
  }
}
</script>

<style lang="less" scoped>
.group {
  display: flex;
  height: 155px;
  width: 100%;
  padding: 12px;
  background-color: #4d4d4d;
  border: 1px solid #404040;
  border-radius: 10px;
  margin-bottom: 10px;
  box-sizing: border-box;

  &:hover {
    background-color: #575757;
  }

  .info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &__title {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      flex: 1;
      width: 160px;
      font-size: 14px;
      overflow-wrap: break-word;
      color: rgba(255, 255, 255, 255);

      .designator {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0 6px;
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 18px;
        border-radius: 4px;
        transform: scale(0.83) translateX(-8px);
        color: #EEEFF1;
        background-color: #737373;
      }
    }

    &__avatars {
      display: flex;
      margin-bottom: 14px;
      //visibility: hidden;

      ::v-deep .avatar {
        margin-right: 4px;

        &:last-child {
          margin-right: 0;
        }
      }

      .memberCount {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 25px;
        height: 25px;
        font-size: 11px;
        font-weight: 600;
        border-radius: 50%;
        cursor: pointer;
        color: #F0F0F0;
        background: #777777;
      }

      .popoUsers {
        :deep(.select-dropdown) {
          display: flex;
          flex-wrap: wrap;
          width: 169px;
          padding: 10px 12px 6px 12px;

          .hover-text {
            border-radius: 4px;
            background-color: #EEEEEE;
          }
        }
      }
    }

    .more-icon {
      margin-right: 2px;
      visibility: hidden;
      cursor: pointer;
    }

    &__actions {
      display: flex;
      justify-content: space-between;
      color: #868686;
      margin-right: 8px;

      &__file-num {
        flex: 1;
      }
    }
  }

  .thumbs {
    display: flex;
    width: 100%;
    align-content: space-around;
    overflow: hidden;
    cursor: pointer;

    .no-thumb {
      background-color: #434343;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #868686;
      border-radius: 4px;
    }

    .thumb {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;
      width: 200px;
      overflow: hidden;
      border-radius: 5px;
      background-color: #434343;
      position: relative;

      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.group-active {
  border: 2px solid #1373e6;
  background-color: #4d4d4d;
  padding: 11px;
}

.group-item-active {
  visibility: visible;
}

.star-icon {
  margin-right: 4px;
  cursor: pointer;
  vertical-align: top;

  &.star-light {
    color: #f2b713;
  }
}
</style>
