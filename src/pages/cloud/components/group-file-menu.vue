<template>
  <context-menu
    :show="show"
    :position="position"
    :menuBtns="menuBtns"
    @click="handleMenuClick"
    @close="closeContextMenu"
  >
    <div class="item gray" style="color: #1A1A1A">文件信息</div>
    <div>
      文件作者：
      <span
        class="popo-btn"
        @click="handlePopoOpen(fileItem.author.collaboratorKey)"
      >
        {{ fileItem.author && fileItem.author.collaboratorName }}
        <img
          class="popo-icon"
          src="@/assets/img/popo.png"
          width="12"
          height="12"
          alt=""
        />
      </span>
    </div>

    <div>创建时间：{{ fileItem.uploadTime }}</div>
    <div>
      最近更新：
      <span
        class="popo-btn"
        @click="handlePopoOpen(fileItem.lastUpdater.collaboratorKey)"
      >
      {{ fileItem.lastUpdater && fileItem.lastUpdater.collaboratorName }}
        <img
          class="popo-icon"
          src="@/assets/img/popo.png"
          width="12"
          height="12"
          alt=""
        />
      </span>
    </div>

    <div>更新时间：{{ fileItem.updateTime }}</div>
    <div>
      格式大小：{{ fileItem.ext }} /
      {{ fileItem.size && $renderSize(fileItem.size) }}
    </div>

    <div class="bar" />
  </context-menu>
</template>

<script>
export default {
  props: {
    show: Boolean,
    fileItem: {
      type: Object,
      default: () => ({})
    },
    position: {
      type: Object,
      default: () => ({
        x: 0,
        y: 0
      })
    },
    hasDelAuth: Boolean
  },
  data() {
    return {}
  },
  computed: {
    menuBtns() {
      return [
        {
          title: '分享文件给',
          eventName: 'share',
          show: true
        },
        {
          title: '在新窗口打开',
          eventName: 'download',
          disabled: this.fileItem.permission === 'READ_ONLY' || !this.$bus.isWSInit
        },
        {
          title: '在当前窗口置入',
          eventName: 'put',
          disabled: !this.$bus.isWSInit
        },
        {
          title: '下载文件',
          eventName: 'save',
          disabled: !this.$bus.isWSInit
        },
        {
          title: `查看历史版本（${this.fileItem.historyCount}）`,
          eventName: 'history'
        },
        {
          title: '上传到素材库',
          eventName: 'saveToSC'
        },
        {
          title: '修改名称',
          eventName: 'editName'
        },
        {
          title: '移除文件',
          eventName: 'removeShared',
          className: 'warning'
        },
        {
          title: '删除',
          eventName: 'del',
          className: 'warning',
          hidden: !this.hasDelAuth
        }
      ]
    }
  },
  methods: {
    closeContextMenu() {
      this.$emit('close')
    },
    handlePopoOpen(url) {
      if (url) {
        this.$csInterface.openURLInDefaultBrowser(
          `http://popo.netease.com/static/html/open_popo.html?ssid=${url}&sstp=0`
        )
      }
      this.closeContextMenu()
    },
    handleMenuClick({ eventName }) {
      this.$emit(eventName, this.fileItem)
      this.closeContextMenu()
    }
  }
}
</script>
<style lang="less" scoped>
</style>
