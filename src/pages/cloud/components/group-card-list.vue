<template>
  <div>
    <!-- 列表 -->
    <div>
      <template v-for="group in groups">
        <GroupCard
          v-if="!group.isProjectGroup"
          :key="group.id"
          :data="group"
          :windowWidth="Number(windowWidth)"
          :currentGroupId="currentGroupItem.id"
          :isSimple="isSimple"
          @click.native="handleGroupClick(group)"
          @dblclick.native="handleGroupDblClick(group)"
          @showMenu="handelShowMenu"
          @addMember="addGroupMember"
          @showProjectGroupMembers="showProjectGroupMembers"
        />

        <template v-else-if="group.isProjectGroup">
          <template v-if="isProjectGroupFold">
            <ProjectGroupCard
              :key="group.id"
              :data="group"
              :windowWidth="Number(windowWidth)"
              :currentGroupId="currentGroupItem.id"
              :isSimple="isSimple"
              @click.native="handleGroupClick(group)"
              @dblclick.native="handleGroupDblClick(group)"
            />
          </template>

          <template v-else>
            <GroupCard
              v-for="item in group.list"
              :key="`${group.id}${item.id}`"
              :data="item"
              :windowWidth="Number(windowWidth)"
              :currentGroupId="currentGroupItem.id"
              :isSimple="isSimple"
              @click.native="handleGroupClick(item)"
              @dblclick.native="handleGroupDblClick(item)"
              @showMenu="handelShowMenu"
              @addMember="addGroupMember"
              @showProjectGroupMembers="showProjectGroupMembers"
            />
          </template>
        </template>
      </template>
    </div>

    <context-menu
      :show="menuShow"
      :position="menuPos"
      :menuBtns="menuBtns"
      @click="handleMenuClick"
      @close="closeMenu"
    >
      <div class="contextMenu">
        <div class="title">小组信息</div>
        <div class="info">
          小组管理员: {{ currentGroupItem.owner && currentGroupItem.owner.name }}
          <img
            class="popo-btn"
            src="@/assets/img/popo.png"
            :width="12"
            :height="12"
            @click.stop="handlePopoOpen(currentGroupItem.owner && currentGroupItem.owner.email)"
            alt
          />
        </div>

        <div>创建时间: {{ currentGroupItem.createTime }}</div>
        <div class="bar" />
      </div>
    </context-menu>

    <!-- 项目协作组点点点 -->
    <context-menu
      :show="projectGroupMembersShow"
      :position="menuPos"
      @click="handleMenuClick"
      @close="closeMenu"
    >
    </context-menu>

    <GroupTransferUser
      class="groupTransferUser"
      v-model="exitGroupVisiable"
      v-if="isOwner"
      :groupId="currentGroupItem.id"
      :groupName="currentGroupName"
      :ownerEmail="currentGroupItem.owner.email"
      @ok="exitGroupSubmit"
    />

    <Dialog
      v-else
      v-model="exitGroupVisiable"
      type="warning"
      okText="退出"
      title="确认退出该小组吗？"
      :subtitle="`&quot;${currentGroupName}&quot;小组`"
      @ok="exitGroupSubmit"
    />

    <Dialog
      v-if="isOwner"
      v-model="dissolveGroupVisiable"
      type="warning"
      okText="解散"
      :title="`确认解散&quot;${currentGroupName}&quot;小组吗？<br/>解散后<br/>组内的文件也将同时被删除`"
      :subtitle="`&quot;${currentGroupName}&quot;共有${currentGroupItem.fileCount}个文件`"
      @ok="dissolveGroupSubmit"
    />

    <NameFormModal
      v-model="showEditGroupModal"
      :initValue="rename"
      title="修改名称"
      @ok="onRename"
    />
  </div>
</template>

<script>
import GroupTransferUser from '@/pages/components/popo-user/group-transfer-user.vue'
import GroupAddUser from '@/pages/components/popo-user/group-add-user.vue'
import { deleteGroup, quitGroup, renameGroup } from '@/api'
import GroupCard from './group-card.vue'
import ProjectGroupCard from './project-group-card.vue'
import { getGroupDetail } from '@/api/cloud'
import NameFormModal from './name-form-modal.vue'

export default {
  components: {
    GroupCard,
    ProjectGroupCard,
    NameFormModal,
    GroupTransferUser,
    GroupAddUser
  },
  props: {
    groups: {
      type: Array,
      default: () => []
    },
    isSimple: Boolean
  },
  data() {
    return {
      menuPos: {
        x: 0,
        y: 0
      },
      menuShow: false,
      projectGroupMembersShow: false,
      currentGroupItem: {},

      dissolveGroupVisiable: false,
      exitGroupVisiable: false,
      showEditGroupModal: false,
      rename: '',

      windowWidth: ''
    }
  },
  computed: {
    currentGroupName() {
      return this.currentGroupItem.name
    },
    menuBtns() {
      return [
        {
          title: '添加成员',
          eventName: 'addMember',
          func: this.addGroupMember
        },
        {
          title: '修改名称',
          eventName: 'edit',
          func: this.editGroupName
        },
        {
          title: '退出该组',
          eventName: 'exit',
          className: 'warning',
          func: this.exitGroupClick,
          hidden: this.members.length <= 1
        },
        {
          title: '解散该组',
          eventName: 'del',
          className: 'warning',
          func: this.dissolveGroupClick,
          hidden: !this.isOwner
        }
      ]
    },
    isOwner() {
      if (this.currentGroupItem.owner === null) {
        return false
      }
      const { email } = this.currentGroupItem.owner || {}
      const currentUser = this.$bus.profile.email

      return email === currentUser && currentUser
    },
    members() {
      const tmp = this.currentGroupItem.members || []
      return tmp.slice(0, 4)
    },
    isProjectGroupFold() {
      return this.groups.find(i => i.isProjectGroup)?.list?.length >= 10
    }
  },
  mounted() {
    this.windowWidth = document.body.clientWidth
    window.onresize = () => {
      this.windowWidth = document.body.clientWidth
    }
  },
  methods: {
    // 外部调用
    closeMenu() {
      this.menuShow = false
      this.projectGroupMembersShow = false
      this.$forceUpdate()
    },
    handelShowMenu({ x, y, item }) {
      if (!item || !item.name) return
      this.menuPos = { x, y }
      this.menuShow = true
      this.currentGroupItem = item
    },
    showProjectGroupMembers({ x, y, item }) {
      if (!item || !item.name) return
      this.menuPos = { x, y }
      this.projectGroupMembersShow = true
      this.currentGroupItem = item
    },
    handleMenuClick({ func }) {
      this.$nextTick(() => {
        func && func()
      })
    },
    handleGroupClick(group) {
      this.currentGroupItem = group
    },
    // 双击事件
    async handleGroupDblClick(group) {
      if (group.isProjectGroup) {
        this.$bus.setBreadcrumbList([{
          text: '项目协作组',
          routeName: 'projectGroups'
        }])
        await this.$router.push({ name: 'projectGroups' })
        return
      }

      const gid = group.id
      const groupDetail = await getGroupDetail(gid)
      if (!groupDetail) {
        this.groups.splice(this.groups.indexOf(group), 1)
        return this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '当前小组已被删除'
        })
      }
      this.$bus.breadcrumbList.push({
        text: group.name,
        routeName: 'group',
        query: { parentId: '0' },
        params: { gid }
      })
      await this.$router.push({
        name: 'group',
        params: { gid },
        query: { parentId: '0' }
      })
    },
    setColumn(val) {
      this.column = Math.floor(val / 260)
    },
    dissolveGroupClick() {
      this.dissolveGroupVisiable = true
    },
    exitGroupClick() {
      this.exitGroupVisiable = true
    },
    async addGroupMember(data = {}) {
      if (data.item) {
        this.currentGroupItem = data.item
      }
      this.$CustomDialog({
        // title: this.isOwner ? '管理小组成员' : '添加小组成员',
        width: 400,
        custom: true,
        contRender: (h, handleClose) => {
          return h(GroupAddUser, {
            props: {
              groupId: this.currentGroupItem.id,
              ownerEmail:
                this.currentGroupItem.owner &&
                this.currentGroupItem.owner.email,
              title: this.currentGroupItem.name
            },
            on: {
              'close': () => handleClose()
            }
          })
        }
      })
    },
    async exitGroupSubmit() {
      this.exitGroupVisiable = false
      const { id, name } = this.currentGroupItem
      await quitGroup(id, this.$bus.profile.email)

      this.$CustomToast({
        type: 'success',
        duration: 2,
        content: `成功退出小组 "${name}"`
      })
      this.$emit('del', this.currentGroupItem)
    },
    async dissolveGroupSubmit() {
      this.dissolveGroupVisiable = false
      const { id, name } = this.currentGroupItem
      await deleteGroup(id)
      this.$emit('del', this.currentGroupItem)
      this.$CustomToast({
        type: 'success',
        duration: 2,
        content: `成功删除小组 "${name}"`
      })
    },
    handlePopoOpen(url) {
      url &&
      this.$csInterface.openURLInDefaultBrowser(
        `http://popo.netease.com/static/html/open_popo.html?ssid=${url}&sstp=0`
      )
    },
    editGroupName() {
      this.rename = this.currentGroupItem.name
      this.showEditGroupModal = true
    },
    async onRename({ name }) {
      const { id } = this.currentGroupItem
      renameGroup(id, name)
        .then((res) => {
          this.$emit('update', { id: res.id, name })
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content: '名称修改成功'
          })
        })
        .catch((e) => {
          this.$logMsg.error(
            '[onRename] 小组重命名失败：' + e.message,
            'name-form-modal'
          )
        })
    }
  }
}
</script>

<style lang="less" scoped>
.contextMenu {
  .title {
    color: #1A1A1A;
  }

  .info {
    font-weight: normal;
  }
}

.groupTransferUser {
  :deep(input::placeholder) {
    font-size: 12px;
    color: #919191;
  }
}
</style>
