<template>
  <div>
    <SaveFileModal
      :initParams="saveParams"
      v-model="showSaveModal"
      @setUploadModalVisible="setUploadModalVisible"
    />

    <SaveFileModal
      v-model="saveOther"
      :initParams="saveParams"
      title="另存当前PSD"
      mode="save-else"
    />

    <div class="buttons">
      <GButton v-if="$bus.isWSInit" type="primary" size="small" @click="handleShowSaveModal" style="height: 24px">
        <span v-if="waitLoadingProcess === 100" class="spectrum-ActionButton-label">保存</span>
        <span v-else class="spectrum-ActionButton-label">{{ `${waitLoadingProcess} %` }}</span>
        <div class="line">&nbsp;</div>
        <span
          class="down-arrow-icon "
          @mouseenter="handleOnHover(true)"
          @mouseleave="handleOnLeave"
          @click.stop="() => {}"
        >
          <IconFont icon="down-arrow" size="18" class="f-8" />
        </span>
      </GButton>
      <div
        v-if="iconHover && waitLoadingProcess === 100"
        @mouseenter="handleOnHover(true)"
        @mouseleave="handleOnLeave"
        style="width: 80px;position: absolute;top: 20px;left: -10px;padding-top: 10px;background-color: transparent;"
      >
        <context-menu
          :show="true"
          :position="{ x: 0, y: 10 }"
          :menuBtns="[
            {
              title: '另存为',
              eventName: 'saveOther',
            },
          ]"
          @click="handleSaveOther"
          @close="iconHover = false"
          style="width: 100%;position: absolute;text-align: center;"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getDriveFileDetailByName } from '@/api/cloud.js'
import { getFileInfo } from '@/bridge/api'
import { getCurrentCloudPanPSD } from '@/utils/ps'
import SaveFileModal from './save-file-modal.vue'

export default {
  components: {
    SaveFileModal
  },
  data() {
    return {
      showSaveModal: false,
      saveOther: false,
      iconHover: false,
      saveParams: {
        name: '',
        group: '',
        sourcePath: ''
      },
      timeout: null
    }
  },
  computed: {
    waitLoadingProcess() {
      return this.$bus.waitLoadingProcess
    },
    parentId() {
      return this.$route.query.parentId
    }
  },
  methods: {
    handleOnHover(bool) {
      if (bool && this.timeout) {
        clearTimeout(this.timeout)
      }
      this.iconHover = bool
    },
    handleOnLeave() {
      this.timeout = setTimeout(() => this.handleOnHover(false), 500)
    },
    async handleShowSaveModal() {
      this.$logMsg.info(
        '[handleShowSaveModal] 点击保存文件',
        'save-file-button'
      )

      if (!this.$bus.canUpload()) {
        this.$logMsg.info('[handleShowSaveModal] 操作频繁', 'save-file-button')
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '操作频繁，请稍后再试'
        })
        return
      }

      try {
        // 获取当前PSD文件源路径、文件名（内部会处理保存逻辑）
        const fileSourceData = await getCurrentCloudPanPSD(this)
        const {
          filename: localFileName,
          sourcePath,
          group,
          ext
        } = fileSourceData

        this.$logMsg.info(
          '[handleShowSaveModal] getCurrentCloudPanPSD 获取本地文件信息:' +
          JSON.stringify(fileSourceData),
          'save-file-button'
        )

        let onlineGroup = group
        let onlineFileName = localFileName

        if (!onlineGroup) {
          // 非从网盘下载文件但可能是本地新建文件已经上传到网盘，通过源路径找到gid信息
          const fileInfo = (await getFileInfo({ source_path: sourcePath })).data
          const { gid, name } = fileInfo.data || {}

          this.$logMsg.info(
            '[handleShowSaveModal] getFileInfo（file/ps/info）返回:' +
            JSON.stringify(fileInfo.data),
            'save-file-button'
          )

          onlineGroup = this.$route.params.gid || gid
          // 历史bug原因导致客户端数据文件名为空,这里做个拦截有值再赋值
          if (name) onlineFileName = name.split('.')[0]
        }

        let uploadDataInfo = {}

        if (onlineGroup) {
          // 通过group、filename定位到文件的具体信息
          uploadDataInfo = await getDriveFileDetailByName({
            groupId: onlineGroup,
            name: onlineFileName,
            ext,
            parentId: this.parentId
          })
        }

        if (!uploadDataInfo || !uploadDataInfo.size) {
          // 确认非线上文件则弹框填写信息
          this.$logMsg.info(
            '[handleShowSaveModal] 确认非线上文件',
            'save-file-button'
          )
          this.saveParams = {
            name: fileSourceData.filename,
            sourcePath
          }
          this.showSaveModal = true
        } else {
          // 已存在线上文件，直接保存
          this.$CustomToast({
            type: 'info',
            duration: 2,
            content: '保存中，请稍后'
          })
          const { id, groupName, groupId, size, name, isSharedWithMe } = uploadDataInfo
          // 开始上传文件
          await this.$uploadFileAction(this, {
            id,
            groupId: onlineGroup || groupId,
            groupName,
            size,
            fileName: name || localFileName,
            sourcePath,
            isSharedWithMe
          })
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content: '保存成功，正在刷新列表'
          })
        }
      } catch (e) {
        this.$logMsg.error(
          '[handleShowSaveModal] 保存文件失败：' + e.message,
          'save-file-button'
        )
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: e.message
        })
      }
    },
    async handleSaveOther() {
      this.$logMsg.info('[handleSaveOther] 点击另存为', 'save-file-button')
      const fileSourceData = await getCurrentCloudPanPSD(this)
      const { group, filename, sourcePath } = fileSourceData
      this.saveParams = {
        name: filename,
        sourcePath,
        group
      }
      this.saveOther = true
    },
    setUploadModalVisible(visible) {
      this.$emit('setUploadModalVisible', visible)
    }
  }
}
</script>

<style lang="less" scoped>
.line {
  background-color: #ffffff46;
  border-radius: 10px;
  margin: 0 8px;
  width: 1px;
  height: 60%;
}

.buttons {
  position: relative;
  width: fit-content;
  height: 24px;
  margin-left: 10px;
}

.down-arrow-icon {
  display: inline-block;
  width: fit-content;
}
</style>
