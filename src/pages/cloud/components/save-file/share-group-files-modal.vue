<template>
  <Modal v-model="showModal" width="90%" :canBlankClose="false">
    <template #content>
      <div class="content">
        <div class="header">
          <div>选择要更新的文件</div>
          <div>
            <GButton @click="handleCancel" size="small" class="button"
              >取消</GButton
            >
            <GButton
              @click="handleConfirm"
              type="primary"
              style="margin-left: 8px;"
              size="small"
              class="button"
              :disabled="!currentItem.id"
              >确认更新</GButton
            >
          </div>
        </div>
        <div class="body">
          <div class="files" v-if="data.length">
            <div
              class="file-card"
              :key="item.id"
              v-for="item in data"
              @click="handleClick(item)"
            >
              <div class="checked" v-show="item.id === currentItem.id">
                <img :src="require('@/assets/img/checked.png')" width="32px" />
              </div>
              <div class="image">
                <ImgViewer
                  class="spectrum-Thumbnail-image"
                  :src="item.cover"
                  style="width: 100%;height:100%;object-fit: cover"
                  thumbnail
                />
              </div>
              <div class="info">
                <div class="filename">{{ item.name }}</div>
                <div class="update-time">
                  {{ `更新于${$fromNow(item.updateTimestamp)}` }}
                </div>
              </div>
            </div>
          </div>
          <div class="no-file-text" v-else>当前还没有文件</div>
        </div>
      </div>
    </template>
  </Modal>
</template>

<script>
import { getDriveFileList } from '@/api/cloud.js'
import { GROUP_SHARE_WITHME_ID } from '@/utils/const'

export default {
  name: 'SelectSharedFileModal',
  props: {
    show: {
      type: Boolean,
      default: false,
    },
  },
  model: {
    prop: 'show',
    event: 'change',
  },
  data() {
    return {
      data: [],
      currentItem: {},
    }
  },
  async created() {
    const { list } = await getDriveFileList({
      groupId: GROUP_SHARE_WITHME_ID,
      pageSize: 100,
    })
    this.data = list
  },
  computed: {
    showModal: {
      get() {
        return this.show
      },
      set(value) {
        this.$emit('change', value)
      },
    },
  },
  methods: {
    handleCancel() {
      this.showModal = false
    },
    handleClick(item) {
      this.currentItem = item
    },
    handleConfirm() {
      if (this.currentItem.id) {
        this.$emit('confirm', this.currentItem)
        this.showModal = false
      }
    },
  },
}
</script>

<style lang="less" scoped>
.content {
  position: relative;
  height: 90vh;
  min-height: 90vh;
  overflow: hidden;

  .header {
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 24px;
    width: 100%;
    background-color: #4d4d4d;
    height: 48px;
    border-top-right-radius: 10px;
    border-top-left-radius: 10px;
    border-bottom: 1px solid #3a3a3a;
  }

  .body {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: auto;
    height: calc(90vh - 48px - 16px);

    .no-file-text {
      font-size: 14px;
      color: #989898;
    }

    .files {
      padding: 16px 24px;
      box-sizing: border-box;
      display: grid;
      width: 100%;
      height: 100%;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      grid-row-gap: 12px;
      grid-column-gap: 12px;
    }

    .file-card {
      position: relative;
      height: 184px;
      width: 100%;
      border: 1px solid #3a3a3a;
      background-color: #4d4d4d;
      border-radius: 10px;
      .image {
        width: 100%;
        height: 137px;
        overflow: hidden;
        border-top-right-radius: 10px;
        border-top-left-radius: 10px;
      }
      .info {
        height: 46px;
        padding: 8px;
        box-sizing: border-box;

        .update-time {
          color: #868686;
        }
      }
      &:hover {
        background-color: #575757;
      }
      .checked {
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 10px;
        border: 2px solid #1373e6;

        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }

  .button {
    width: 80px;
  }
}
</style>
