<template>
  <div class="save-file-modal-wrapper">
    <Modal v-model="modalShow" :canBlankClose="false" width="260px">
      <template #content v-if="!isNextConflictStep">
        <div class="content">
          <div class="header">
            <div class="title">{{ title }}</div>
            <div class="title">“{{ initParams.name || '空' }}”</div>
          </div>

          <div class="data">
            <Select
              class="form-item"
              v-model="group"
              :options="groupOptions"
              placeholder="请选择小组"
            >
              <div slot="empty" class="select-empty" @click="showCreateGroupModal = true">
                暂无小组，去创建小组
                <IconFont
                  icon='enter'
                  size='18'
                />
              </div>
            </Select>

            <gInput
              v-if="!showShareSelectName"
              v-model="name"
              placeholder="名称"
              @paste="handlePasteInput"
            />

            <div
              v-else
              class="form-item"
              @click.stop="handleClickSelectSharedFile"
            >
              <gInput :value="name" placeholder="文件保存至">
                <template #suffix>
                  <IconFont #suffix icon="down-arrow" class="icon" />
                </template>
              </gInput>
            </div>

            <div class="error-text" v-show="!overLen && name.length !== 0">
              文件名不能包含下列字符\/:*?"&#60;>｜.，不超过50字符
            </div>

            <div v-if="showShareSelectName">
              在“与我共享”的组内，你可以将别人分享的文件编辑后，作为最新版本覆盖到现有文件上。
            </div>
          </div>

          <div class="footer">
            <GButton
              @click="handleSave"
              :disabled="!name || !Boolean(group || innerGroup) || uploading"
              type="primary"
              class="w-full"
              style="margin-bottom: 12px;"
            >
              确认
            </GButton>

            <GButton @click="handleClose" class="w-full">取消</GButton>
          </div>
        </div>
      </template>

      <template #content v-else>
        <div class="content">
          <div class="header">
            <img src="@/components/basic/dialog/img/icon.png" alt />
          </div>

          <div class="data">
            <div class="info">"{{ name }}"已存在，是否覆盖在该文件上？</div>
            <div>不用担心，被覆盖的文件，可通过历史版本找回覆盖原文件</div>
            <div v-if="conflictUpdater?.collaboratorName">
              上次是由{{ conflictUpdater.collaboratorName }}
              <span v-if="conflictUpdater.updateTime">在{{ conflictUpdater.updateTime }}</span>
              上传的
            </div>
          </div>

          <div class="footer">
            <GButton
              @click="handleSave"
              :disabled="uploading"
              class="w-full"
            >
              覆盖源文件
            </GButton>

            <GButton
              @click="handleBack"
              :disabled="uploading"
              type="primary"
              class="w-full"
              style="margin-bottom: 12px;"
            >
              取消
            </GButton>
          </div>
        </div>
      </template>
    </Modal>

    <ShareGroupFilesModal
      v-if="showShareGroupFilesModal"
      v-model="showShareGroupFilesModal"
      @confirm="handleConfirmSharedFile"
    />

    <NameFormModal
      v-model="showCreateGroupModal"
      title="创建小组"
      @ok="onGroupCreated"
    />
  </div>
</template>

<script>
import { getGroupList, createGroup, getProjectGroupList } from '@/api'
import { getUploadInit, getDriveFileDetailByName } from '@/api/cloud.js'
import { GROUP_SHARE_WITHME_ID } from '@/utils/const'
import ShareGroupFilesModal from './share-group-files-modal.vue'
import NameFormModal from '../name-form-modal.vue'
import path from 'path'

const UPLODA_TYPE_MAPPER = {
  file: 0,
  folder: 1
}

export default {
  components: {
    ShareGroupFilesModal,
    NameFormModal
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '保存当前PSD'
    },
    initParams: {
      // 初始化参数
      type: Object,
      default: () => ({
        name: '',
        group: '',
        sourcePath: ''
      })
    },
    mode: {
      type: String,
      default: 'save'
    }
  },
  model: {
    prop: 'show',
    event: 'change'
  },
  data() {
    return {
      name: '', // 文件名
      group: '', // 组别
      innerGroup: '', // 与我共享里的组别
      groupOptions: [], // 组别选择列表
      isNextConflictStep: false, // 文件重名提醒
      showShareSelectName: false, // 与我共享组时显示选择框
      showShareGroupFilesModal: false, // 分享组别列表弹框
      uploading: false, // 是否上传中,
      showCreateGroupModal: false, // 创建小组弹框
      conflictUpdater: null
    }
  },
  computed: {
    modalShow: {
      get() {
        return this.show
      },
      set(value) {
        this.$emit('change', value)
      }
    },
    overLen() {
      return this.name && /^[^/\\:*?"<>|\\.]{1,50}$/.test(this.name)
    },
    validate() {
      return (this.group || this.innerGroup) && this.overLen
    },
    parentId() {
      return this.$route.query.parentId
    }
  },
  watch: {
    modalShow: async function (value) {
      if (value) {
        // 初始化弹窗信息
        this.uploading = false
        await this.initGroups()

        this.name = this.initParams.name
        this.group = this.$route.params.gid || this.initParams.group

        const isExist = this.groupOptions.some(
          (item) => item.value === this.group && !item.disabled
        )

        if (!isExist) {
          this.group = ''
        }
      } else {
        this.name = ''
        this.group = ''
        this.innerGroup = ''
        this.isNextConflictStep = false
      }
      this.$logMsg.info(
        `监听modalShow:${value}事件,初始化信息：this.name:${this.name ||
        '空'},this.group:${this.group || '空'}`,
        'save-file-modal'
      )
    },
    group(value) {
      // 切换为与我共享
      if (value === GROUP_SHARE_WITHME_ID) {
        this.showShareSelectName = true
        this.name = ''
      } else {
        this.showShareSelectName = false
        this.innerGroup = ''
      }
    }
  },
  methods: {
    // 查询的组别列表数据
    async initGroups() {
      // 先尝试从缓存获取
      const cachedData = this.$bus.getCloudGroupListCache()

      if (cachedData.groupList.length > 0 || cachedData.projectGroupList.length > 0) {
        this.groupOptions = [...cachedData.groupList, ...cachedData.projectGroupList]
          .filter((group) => group !== null)
          .map((group) => ({
            label: group.name,
            value: group.id,
            disabled:
              group.id === GROUP_SHARE_WITHME_ID && this.mode === 'import'
          }))
        return
      }

      // 缓存中没有数据，从API获取
      try {
        const { list: groupList } = await getGroupList()
        const projectGroupList = await getProjectGroupList()

        // 缓存数据
        this.$bus.setCloudGroupListCache(groupList, projectGroupList)

        this.groupOptions = [...groupList, ...projectGroupList]
          .filter((group) => group !== null)
          .map((group) => ({
            label: group.name,
            value: group.id,
            disabled:
              group.id === GROUP_SHARE_WITHME_ID && this.mode === 'import'
          }))
      } catch (error) {
        // 如果API失败但有缓存，使用缓存数据
        if (cachedData.groupList.length > 0 || cachedData.projectGroupList.length > 0) {
          this.groupOptions = [...cachedData.groupList, ...cachedData.projectGroupList]
            .filter((group) => group !== null)
            .map((group) => ({
              label: group.name,
              value: group.id,
              disabled:
                group.id === GROUP_SHARE_WITHME_ID && this.mode === 'import'
            }))
        } else {
          throw error
        }
      }
    },
    // 名称粘贴截断长度
    handlePasteInput(e) {
      e.preventDefault()
      const data = e.clipboardData.getData('Text')
      if (data.length > 50) {
        this.name = data.slice(0, 50)
        return
      }

      this.name = data
    },
    // 分享组别文件夹选择确认
    handleConfirmSharedFile(item) {
      this.$logMsg.info(
        `[handleConfirmSharedFile] 组别文件夹选择确认:this.innerGroup = ${item.groupId ||
        '空'}, this.name = ${item.name || '空'}`,
        'save-file-modal'
      )

      this.innerGroup = item.groupId
      this.name = item.name
    },
    // 取消保存
    handleClose() {
      this.$logMsg.info('取消保存文件', 'save-file-modal')
      this.modalShow = false
    },
    // 返回到第一步
    handleBack() {
      this.isNextConflictStep = false
    },
    // 显示分享文件夹选择弹窗
    handleClickSelectSharedFile() {
      this.showShareGroupFilesModal = true
    },
    // 点击保存
    async handleSave() {
      this.$logMsg.info(
        `[handleSave] ${
          this.isNextConflictStep ? '弹框点击继续保存' : '弹框点击保存'
        }`,
        'save-file-modal'
      )

      if (!this.validate || this.uploading) {
        let message = ''
        if (this.uploading) {
          message = '文件上传中，请稍后...'
        } else if (this.group === GROUP_SHARE_WITHME_ID) {
          message = '请在“文件保存至”选择需要更新的文件'
        }
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: message
        })
        this.$logMsg.error(
          '[handleSave] 校验不通过:' + message,
          'save-file-modal'
        )
        return
      }
      const ext = /(?:\.([^.]+))?$/.exec(
        path.basename(this.initParams.sourcePath)
      )[1]

      const name = this.name

      const fileInfo = await getDriveFileDetailByName({
        groupId: this.innerGroup || this.group,
        name,
        ext,
        parentId: this.parentId
      })

      const hasConflict = fileInfo && fileInfo.size // 文件名与线上冲突

      // 如果是第一步动作，非共享组别文件已存在提示
      if (
        !this.isNextConflictStep &&
        this.group !== GROUP_SHARE_WITHME_ID &&
        hasConflict
      ) {
        this.$logMsg.error(
          '[handleSave] 文件名冲突，弹出提示',
          'save-file-modal'
        )
        this.isNextConflictStep = true
        if (fileInfo.lastUpdater) {
          this.conflictUpdater = {
            ...fileInfo.lastUpdater,
            updateTime: fileInfo.updateTime
          }
        }
        return
      }

      this.$emit('setUploadModalVisible', true)

      this.modalShow = false

      this.$CustomToast({
        type: 'info',
        duration: 2,
        content: '正在初始化上传，请稍等'
      })

      const { size, id, groupName, groupId } = await getUploadInit({
        groupId: this.innerGroup || this.group,
        name: `${name}.${ext}`,
        type: UPLODA_TYPE_MAPPER.file,
        parentId: this.parentId,
        size: fileInfo?.size
      })

      this.$bus.uploadStack.push({
        id,
        filename: name,
        process: 10,
        size,
        status: 'progress',
        groupName,
        groupId,
        sourcePath: this.initParams.sourcePath,
        isSharedWithMe: !!this.innerGroup
      })

      await this.$uploadFileAction(this, {
        id,
        groupId,
        groupName,
        size,
        fileName: name,
        sourcePath: this.initParams.sourcePath,
        isSharedWithMe: !!this.innerGroup
      })
    },
    onGroupCreated({ name }) {
      createGroup({ name })
        .then((res) => {
          // 清除缓存，强制重新获取最新数据
          this.$bus.clearCloudGroupsCache()
          this.initGroups()
          this.group = res.id
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content: `成功创建小组 “${name}”`
          })
          this.showCreateGroupModal = false
          this.$bus.$emit('refreshGroupList')
        })
        .catch((e) => {
          this.$logMsg.error(`[onGroupCreated] 小组创建失败：${e.message}`, 'groups')
        })
    }
  }
}
</script>

<style lang="less" scoped>

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  height: fit-content;
  padding: 24px 16px;
  width: 260px;

  .header {
    font-family: 'PingFang SC', serif;
    text-align: center;

    .title {
      font-size: 14px;
      font-weight: 500;
      word-break: break-all;
    }
  }

  .data {
    width: 100%;
    margin: 16px 0;

    .form-item {
      margin-bottom: 8px;

      .icon {
        font-size: 22px;
        color: #fff;
      }
    }

    .info {
      text-align: center;
      width: 180px;
      font-size: 14px;
      transform: translateX(10%);
    }
  }

  .footer {
    margin-top: 10px;
    width: 100%;
  }

  .error-text {
    color: red;
  }
}

.select-empty {
  cursor: pointer;
  color: #fff;
  line-height: 24px;
  width: 100%;

  &:hover {
    background: #474747;
  }
}
</style>
