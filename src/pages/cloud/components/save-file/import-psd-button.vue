<template>
  <div>
    <SaveFileModal
      v-model="showSaveModal"
      :initParams="saveParams"
      title="上传文件"
      mode="import"
      @setUploadModalVisible="$emit('setUploadModalVisible', true)"
    />
    <GButton
      v-if="!loading && $bus.isWSInit"
      :loading="loading"
      type="primary"
      @click="handleClick"
      style="color: #fff; padding: 6px 24px"
    >
      上传文件
    </GButton>
  </div>
</template>

<script>
import path from 'path'
import SaveFileModal from './save-file-modal.vue'

export default {
  components: {
    SaveFileModal
  },
  data() {
    return {
      showSaveModal: false,
      saveParams: {
        sourcePath: '',
        name: '',
        group: ''
      },
      loading: false
    }
  },
  methods: {
    async handleClick() {
      try {
        if (this.showSaveModal) return
        this.loading = true
        this.$logMsg.info(
          '[天枢网盘-导入文件][Button Click] 点击导入文件',
          'import-psd-button'
        )

        const { data } = await window.cep.fs.showOpenDialogEx(
          true,
          false,
          '导入文件',
          '',
          ['psd', 'psb', 'jpg', 'jpeg', 'png', 'gif', 'crw', 'tiff', 'ai', 'bmp', 'TTF', 'WOFF'],
          '天枢支持文件格式',
          'Open'
        )

        if (data && data.length) {
          this.saveParams = {
            name: path.basename(data[0]).split('.')[0],
            sourcePath: data[0]
          }
          this.showSaveModal = true
        }
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: e.message
        })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.upload-btn {
  margin: 0 10px;
}
</style>
