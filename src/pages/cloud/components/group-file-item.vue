<template>
  <div
    v-if="isFile"
    :id="item.id"
    class="cloud-group-item"
    @contextmenu.prevent="handleClickRight"
  >
    <div
      class="item-img spectrum-Thumbnail spectrum-Thumbnail--cover"
      @click.stop="handleItemClick"
    >
      <div
        class="spectrum-Thumbnail-background"
        style="background-color: #434343"
      />
      <ImgViewer
        v-if="item.cover || uploadFileItemInfo?.preview_url"
        class="spectrum-Thumbnail-image"
        :alt="item.name"
        :src="item.cover || uploadFileItemInfo?.preview_url"
        :style="{
          height: '132px',
          width: '100%'
        }"
        thumbnail
      />
      <img
        class="spectrum-Thumbnail-image"
        v-else
        :src="require('@/assets/img/svg/default.svg')"
        :alt="item.name"
        :style="{
          height: '40px',
          width: '40px',
        }"
      />
      <div
        class="author-avatar-box"
        @click.stop="$openPopo(item.lastUpdater.collaboratorKey)"
      >
        <Tooltip
          :content="`最后更新：${item.lastUpdater.collaboratorName}`"
          placement="right"
        >
          <ImgViewer
            v-if="item.lastUpdater.avatar"
            class="author-avatar"
            :src="item.lastUpdater.avatar"
            :defaultSrc="require('@/assets/img/logo.png')"
            thumbnail
          />
        </Tooltip>
      </div>

      <div
        class="upload-icon"
        v-show="['uploading', 'NotExist'].includes(item.state) || isUploading"
      >
        <div
          v-if="isUploading"
          class="pie-simple"
          :style="{ '--percent': uploadProcess }"
        >
          <div class="pie-left"></div>
          <div class="pie-right"></div>
        </div>
        <IconFont v-else icon="cloud" :size="12" />
      </div>

      <div
        v-show="isDownLoading"
        class="process-bar"
        :style="{ '--width': `${downloadProcess}%` }"
      />
    </div>

    <div class="item-text">
      <div class="item-text-l">
        <div class="truncate">
          {{ item.name }}{{ item.ext ? `.${item.ext}` : '' }}
        </div>

        <div class="item-subtitle truncate" v-if="!item.new">
          {{ isDownLoading ? downloadProgressText : `更新于${$fromNow(item.updateTimestamp)}` }}
        </div>
      </div>

      <template v-if="!isUploading">
        <div class="item-text-r" v-if="!isSimple">
          <Tooltip
            :content="`历史版本：${item.historyCount}个`"
            :delay="100"
            placement="bottom-start"
          >
            <IconFont icon="history" :size="16" @click="handleHistory" />
          </Tooltip>
          <Tooltip content="邀请协作" placement="bottom-start" :delay="100">
            <IconFont icon="share" :size="16" @click="handleShare" />
          </Tooltip>
          <IconFont icon="more" :size="16" @click="handleClickRight" />
        </div>

        <div v-else class="item-simple-r item-text-r">
          <IconFont
            icon="location"
            :size="18"
            @click="handleClickPosition"
          />
        </div>
      </template>
    </div>
  </div>

  <div v-else :id="item.id" class="cloud-group-item">
    <div class="item-img spectrum-Thumbnail spectrum-Thumbnail--cover folder-img" @dblclick.stop="handleClickFolder">
      <img
        :src="require('@/assets/img/svg/folder.svg')"
        :alt="item.name"
      />
    </div>

    <div class="item-text">
      <div class="item-text-l">
        <div class="truncate">{{ item.name }}</div>
        <div class="item-subtitle truncate" v-if="!item.new">
          {{ isDownLoading ? downloadProgressText : `更新于${$fromNow(item.updateTimestamp)}` }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { memberList } from '@/api'

export default {
  name: 'CloudGroupItem',
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    isSimple: {
      type: Boolean,
      default: false
    },
    showCloud: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    isFile() {
      return this.item.type !== 1 || this.uploadFileItemInfo
    },
    parentId() {
      return this.$route.query.parentId
    },
    downLoadFileItemInfo() {
      // 该文件上传中的信息
      const filesList = this.$bus.downloadFilesList
      return filesList.filter((i) => i.file_key === this.item.fileKey)[0] || {}
    },
    isDownLoading() {
      return !!this.downLoadFileItemInfo.file_key
    },
    downloadProcess() {
      return this.downLoadFileItemInfo.process || 0
    },
    downloadProgressText() {
      const {
        content_length: contentLength,
        download_length: downloadLength
      } = this.downLoadFileItemInfo
      return downloadLength && contentLength
        ? `正在下载：${this.$renderSize(downloadLength)}/${this.$renderSize(
          contentLength
        )}`
        : `正在下载：0 / ${this.$renderSize(contentLength)}`
    },
    uploadFileItemInfo() {
      // 该文件上传中的信息
      const uploadFilesList = this.$bus.uploadFilesList
      const item = uploadFilesList.find((i) => i.sid === this.item.id)
      if (item) return {
        ...item,
        process: this.$bus.uploadStack.find(i => i.id === this.item.id)?.process
      }
      return null
    },
    isUploading() {
      return !!this.uploadFileItemInfo?.sid
    },
    uploadProcess() {
      return this.uploadFileItemInfo?.process || 0
    }
  },
  mounted() {
    const { hash } = this.$router.history.current
    if (`#${this.item.id}` === hash) {
      const target = document.getElementById(this.item.id)
      target.scrollIntoView()
    }
  },
  methods: {
    handleHistory() {
      this.$emit('operate', 'histroy', this.item)
    },
    handleShare() {
      this.$emit('operate', 'share', this.item)
    },
    handleClickRight(event) {
      event.stopPropagation()
      // 上传中、下载中的文件不出现菜单栏
      if (this.isUploading || this.isDownLoading) {
        this.$CustomToast({
          type: 'warning',
          duration: 2,
          content: `文件正在${
            this.isUploading ? '上传' : '下载'
          }中，请稍后操作`
        })
        return false
      }
      if (this.item.new) {
        return false
      } else {
        this.$emit('operate', 'context-menu', this.item, event)
      }
    },
    async handleClickPosition() {
      let members = []
      let memberPage = 0
      while (true) {
        memberPage += 1
        const res = await memberList(this.item.groupId, memberPage)
        members = [...members, ...res.list]
        if (res.total >= members.length) {
          break
        }
      }
      const inGroup = members.filter((user) => {
        return this.$bus.profile.email === user.email
      })
      let gid = 0
      if (inGroup.length > 0) {
        gid = this.item.groupId
      } else {
        gid = this.$bus.shareWithMe
      }
      await this.$router.push({
        name: 'group',
        params: { gid: gid },
        query: { locationId: this.item.id }
      })
    },
    handleItemClick() {
      if (this.item.new) {
        return false
      } else {
        this.$emit('operate', 'item-click', this.item)
      }
    },
    handleClickFolder() {
      this.$bus.breadcrumbList.push({
        text: this.item.name,
        routeName: 'group',
        query: { parentId: this.item.id },
        params: { gid: this.item.groupId }
      })
      this.$router.push({
        name: 'group',
        params: { gid: this.item.groupId },
        query: { parentId: this.item.id }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.cloud-group-item {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  border-radius: 4px;
  border: 1px solid #434343;
  min-width: 100px;
  min-height: 100px;

  .item-img {
    position: relative;
    flex: 1;
    width: 100%;
    height: 0;
    background-color: #434343;
    background-image: none;
    border-radius: 4px;

    .spectrum-Thumbnail-image {
      width: 100%;
      height: 132px;
      object-fit: cover;
      object-position: center;
      z-index: 0;
    }

    .author-avatar-box {
      position: absolute;
      left: 8px;
      bottom: 8px;
      width: 24px;
      height: 24px;
      z-index: 2;
    }

    .cloud-item {
      position: absolute;
      right: 8px;
      bottom: 8px;
      width: 18px;
      height: 18px;
      z-index: 2;
      background-color: #5b5b5b;
      border-radius: 9px;
      border: 1px solid #383838;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #fff;
    }

    .author-avatar {
      height: 24px;
      width: 24px;
      border-radius: 50%;
      object-fit: cover;
      object-position: center;
      border: 0.5px solid #ffffff;
      cursor: pointer;
      overflow: hidden;
    }

    .process-bar {
      position: absolute;
      left: 0;
      bottom: 0;
      width: 100%;
      height: 2px;
      background: #383838;

      &::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: 0;
        width: var(--width);
        height: 100%;
        background: #1373e6;
      }
    }

    .upload-icon {
      position: absolute;
      bottom: 9px;
      right: 7px;
      width: 18px;
      height: 18px;
      border-radius: 50%;
      background: #5b5b5b;
      border: 1px solid #383838;
      text-align: center;
      line-height: 16px;
    }

    :deep(.tooltip-content) {
      margin-left: 4px;
    }
  }

  .folder-img {
    background: #404040;

    &::before {
      box-shadow: none;
    }
  }

  &:hover {
    .item-text {
      background: #575757;

      .item-text-r {
        display: flex;
      }

      .item-simple-r {
        display: flex;
      }
    }
  }

  .item-text {
    display: flex;
    height: 46px;
    padding: 7px 8px;
    color: #ffffff;
    font-size: 12px;
    background: #4d4d4d;
    border-radius: 0 0 4px 4px;

    .item-text-l {
      flex: 1;
      width: 0;

      .item-subtitle {
        font-weight: normal;
        transform: scale(0.92) translateX(-8px) translateY(3px);
        color: #868686;
      }
    }

    .item-text-r {
      display: flex;
      flex-direction: row;
      width: 64px;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      z-index: 1;

      .iconfont {
        font-size: 18px;
        color: #989898;
        cursor: pointer;

        &:hover {
          color: #ffffff;
        }
      }

      :deep(.tooltip-content) {
        margin-top: 4px;
      }
    }

    .item-simple-r {
      width: 22px;
      align-items: center;
      justify-content: space-around;
      cursor: pointer;

      .iconfont {
        color: #999999;

        &:hover {
          color: #ffffff;
        }
      }
    }
  }
}

.pie-simple {
  display: inline-block;
  width: 14px;
  height: 14px;
  line-height: 18px;
  margin-top: 2px;
  background-color: #989898;
  border-radius: 50%;
  overflow: hidden;
}

.pie-left,
.pie-right {
  width: 50%;
  height: 100%;
  float: left;
  position: relative;
  overflow: hidden;
}

.pie-left::before,
.pie-right::before,
.pie-right::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
}

.pie-left::before {
  left: 100%;
  transform-origin: left;
  transform: rotate(calc(3.6deg * (var(--percent) - 50)));
  opacity: calc(99999 * (var(--percent) - 50));
}

.pie-right::before {
  right: 100%;
  transform-origin: right;
  transform: rotate(calc(3.6deg * var(--percent)));
}

.pie-right::after {
  opacity: calc(99999 * (var(--percent) - 50));
  left: 0;
}

.pie-percent {
  transform: translateY(-10%);
}

:deep(.spectrum-Thumbnail) {
  &::before {
    box-shadow: none;
  }
}
</style>
