<template>
  <div>
    <!-- 文件列表 -->
    <div class="file-item-list">
      <div
        v-for="item in dataList"
        :key="item.id"
        class="file-item"
      >
        <GroupFileItem
          :item="item"
          @operate="handleOperate"
          :isSimple="isSimple"
        />
      </div>
    </div>

    <!-- 右键菜单 -->
    <GroupFileMenu
      :show="menuShow"
      :fileItem="fileItem"
      :position="menuPos"
      :hasDelAuth="
        gid !== GROUP_SHARE_WITHME_ID || profileId === contMenuAuthorId
      "
      @close="closeContextMenu"
      @share="handleShare"
      @download="handleDownloadFile"
      @put="handelPut"
      @history="handleHistroy"
      @saveToSC="handleSaveToSC"
      @save="handleSaveFile"
      @editName="handleEditName"
      @removeShared="handleRemove"
      @del="handleDel"
    />

    <!-- 删除 二次确认弹窗 -->
    <Dialog
      v-model="delVisiable"
      type="warning"
      okText="删除"
      title="确认删除该文件吗？"
      :width="260"
      :subtitle="`“${fileItem.name}”包含历史版本${fileItem.historyCount}个`"
      @ok="handleDelFile"
    />

    <!-- 修改名称 二次确认弹窗 -->
    <NameFormModal
      v-model="editNameVisiable"
      :initValue="fileItem.name"
      title="修改名称"
      @ok="editNameConfirm"
    />

    <!-- 移除文件 二次确认弹窗 -->
    <Dialog
      class="removeDialog"
      v-model="removeVisible"
      type="warning"
      okText="移除"
      title="移除文件"
      @ok="handleRemoveFile"
      hideTopImg
    >
      <template #body>
        <div class="fileName">{{ fileItem.name }}.{{ fileItem.ext }}</div>
        <div class="removeTips">从“分享给我的”中移除该文件，不会删除和影响来源文件</div>
      </template>
    </Dialog>
  </div>
</template>

<script>
import {
  delCollaboration,
  delDriveFile, getDriveFileDetailByName,
  getDriveFileHistroyList,
  updateFileName
} from '@/api/cloud.js'
import { delFile, postTouchFile } from '@/bridge/api'
import { GROUP_SHARE_WITHME_ID } from '@/utils/const'
import { DOWN_OPERATE_ENUM } from '@/bridge/const'
import { saveFileLocal } from '@/utils/fs'
import ClientDownloadFileMixin from '@/mixins/client-download-file'
import FilePreview from '@/pages/components/file-preview/index.vue'
import HistoricCont from '@/pages/components/historic-cont/index.vue'
import GroupFileShare from '@/pages/components/popo-user/group-file-share.vue'
import GroupFileMenu from './group-file-menu.vue'
import NameFormModal from './name-form-modal'
import GroupFileItem from './group-file-item.vue'

export default {
  mixins: [ClientDownloadFileMixin],
  props: {
    dataList: {
      type: Array,
      default: () => []
    },
    isSimple: Boolean
  },
  components: {
    GroupFileMenu,
    NameFormModal,
    GroupFileItem,
    HistoricCont,
    GroupFileShare,
    FilePreview
  },
  data() {
    return {
      GROUP_SHARE_WITHME_ID,
      gid: this.$route.params.gid,
      delVisiable: false,
      editNameVisiable: false,
      historyList: [],
      menuPos: {
        x: 0,
        y: 0
      },
      fileItem: {},
      menuShow: false,
      removeVisible: false
    }
  },
  computed: {
    profileId() {
      return this.$bus.profile.email || ''
    },
    contMenuAuthorId() {
      return this.fileItem.author && this.fileItem.author.collaboratorKey
    },
    parentId() {
      return this.$route.query.parentId
    }
  },
  methods: {
    // 文件卡片的工具栏事件
    handleOperate(cmd, item, e) {
      this.fileItem = { ...this.fileItem, ...item }
      switch (cmd) {
        case 'context-menu':
          this.handleContextMenu(e, item)
          break
        case 'item-click':
          this.menuShow = false
          postTouchFile(item.groupId, item.fileKey)
          this.$CustomDialog({
            width: 'calc(100% - 80px)',
            style: {
              height: 'calc(100% - 160px)',
              margin: '0 0 16px'
            },
            custom: true,
            contRender: (h, handleClose, route) => {
              return h(FilePreview, {
                props: {
                  item
                },
                on: {
                  turnLast: (id) => {
                    const list = this.dataList.filter(i => !i.type)
                    const index = list.findIndex(i => i.id === id)
                    if (index < 0) {
                      this.$CustomToast({
                        type: 'error',
                        duration: 2,
                        content: '网络出问题了，请刷新页面'
                      })
                      return
                    }
                    if (index === 0) {
                      this.$CustomToast({
                        type: 'warning',
                        duration: 2,
                        content: '已经是第一个了'
                      })
                      return
                    }
                    if (index > 0) {
                      this.$CustomToast({
                        type: 'success',
                        duration: 2,
                        content: '加载中，请稍后'
                      })

                      const newItem = list[index - 1]
                      handleClose()
                      this.handleOperate('item-click', newItem)
                    }
                  },
                  turnNext: (id) => {
                    const list = this.dataList.filter(i => !i.type)
                    const index = list.findIndex(i => i.id === id)
                    if (index < 0) {
                      this.$CustomToast({
                        type: 'error',
                        duration: 2,
                        content: '网络出问题了，请刷新页面'
                      })
                      return
                    }

                    if (index === list.length - 1) {
                      this.$CustomToast({
                        type: 'warning',
                        duration: 2,
                        content: '已经是最后一个了'
                      })
                      return
                    }

                    if (index >= 0) {
                      this.$CustomToast({
                        type: 'success',
                        duration: 2,
                        content: '加载中，请稍后'
                      })

                      const newItem = list[index + 1]
                      handleClose()
                      this.handleOperate('item-click', newItem)
                    }
                  }
                }
              })
            }
          })
          break
        case 'histroy':
          this.handleHistroy(item)
          break
        case 'share':
          this.handleShare(item)
          break
        default:
          break
      }
    },
    // 获取历史版本数据
    handleHistroy(item, isReset = false) {
      postTouchFile(item.groupId, item.fileKey)
      getDriveFileHistroyList({ fileId: item.id }).then((d) => {
        this.historyList = d
        !isReset && this.handleOpenHistoryDialog(item)
      })
    },
    // 查看历史版本弹窗
    handleOpenHistoryDialog(item) {
      this.$CustomDialog({
        title: `“${item.name}”的历史版本`,
        width: 400,
        contRender: (h) => {
          return h(HistoricCont, {
            props: {
              item,
              list: this.historyList
            },
            on: {
              reset: (i) => {
                this.handleHistroy(item, true)
                this.$emit('update')
              },
              operate: (cmd, i) => {
                if (cmd === 'open') {
                  this.handleDownloadFile(i)
                }
              }
            }
          })
        }
      })
    },
    // 编辑名称弹窗打开
    handleEditName() {
      this.editNameVisiable = true
    },
    // 编辑名称
    async editNameConfirm({ name }) {
      try {
        const fileInfo = await getDriveFileDetailByName({
          groupId: this.gid,
          name: name,
          ext: this.fileItem.ext,
          parentId: this.parentId
        })

        const isConflict = fileInfo && fileInfo.size

        if (isConflict) {
          this.$CustomToast({
            type: 'error',
            duration: 2,
            content: '文件已存在，请更换名称重新编辑'
          })
          return
        }

        const data = {
          id: this.fileItem.id,
          permission: this.fileItem.permission,
          historyCount: this.fileItem.historyCount,
          name
        }
        await updateFileName(data)
        this.$emit('update', { id: data.id, name })
        this.$CustomToast({
          type: 'success',
          content: '名称修改成功'
        })
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          content: e.message
        })
      }
    },
    // 分享
    handleShare(item) {
      postTouchFile(item.groupId, item.fileKey)
      this.$CustomDialog({
        title: '邀请协作者',
        width: 400,
        contRender: (h) => {
          return h(GroupFileShare, {
            props: { item }
          })
        }
      })
    },
    handleDel(item) {
      this.fileItem = { ...this.fileItem, ...item }
      this.delVisiable = true
    },
    handleDelFile() {
      delFile({
        mode: 'local',
        files: [{ file_key: this.fileItem.fileKey, sid: this.fileItem.id }]
      })
      delDriveFile(this.fileItem.id)
        .then((d) => {
          this.$CustomToast({
            type: 'success',
            content: '删除成功'
          })
          this.$emit('del', { ...this.fileItem })
        })
        .catch(e => {
          this.$CustomToast({
            type: 'error',
            content: e.message
          })
        })
        .finally(() => {
          this.delVisiable = false
        })
    },
    handleRemove(item) {
      this.fileItem = { ...this.fileItem, ...item }
      this.removeVisible = true
    },
    async handleRemoveFile() {
      try {
        await delCollaboration(this.fileItem.collaboratorId)
        this.$CustomToast({
          type: 'success',
          content: '移除成功'
        })
        // del handler也是纯前端数据filter
        this.$emit('del', { ...this.fileItem })
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          content: e.message
        })
      } finally {
        this.removeVisible = false
      }
    },
    // 客户端下载文件
    handleDownloadFile(item, operateCode = DOWN_OPERATE_ENUM.NEW_WINDOW_OPEN) {
      this.clientDownloadMixin({ item, operateCode })
    },
    handelPut(item) {
      this.handleDownloadFile(item, DOWN_OPERATE_ENUM.INPUT)
    },
    // 关闭菜单,也对外部开放
    closeContextMenu(e) {
      this.menuShow = false
    },
    // 显示菜单
    handleContextMenu(e, item) {
      if (this.isSimple) {
        return
      }
      const screenWidth = document.body.clientWidth
      const screenHeight = document.body.clientHeight
      this.menuPos.x =
        screenWidth - e.clientX > 196 ? e.clientX : e.clientX - 196
      this.menuPos.y =
        screenHeight - e.clientY > 268 ? e.clientY : e.clientY - 268
      this.menuShow = true
    },
    // 保存文件到本地
    handleSaveFile(item) {
      const { filePath, fileName } = saveFileLocal(item.name, [item.ext])
      if (!filePath || !fileName) return
      item.name = fileName
      this.clientDownloadMixin({ item, filePath })
    },
    // 转存至素材
    handleSaveToSC(item) {
      this.$logMsg.info(
        '[group][handleDownloadFile] 点击转存至素材',
        '天枢网盘'
      )
      this.$router.push({
        name: 'upload',
        query: {
          isCloud: true,
          thumbnailUrl: item.cover,
          url: item.url,
          fileId: item.id,
          size: item.size,
          name: item.name
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.file-item-list {
  display: grid;
  width: 100%;
  height: auto;
  grid-template-columns: repeat(auto-fill, minmax(210px, 1fr));
  grid-row-gap: 12px;
  grid-column-gap: 12px;

  .file-item {
    width: 100%;
    height: 180px;
  }
}

.removeDialog {
  :deep(.dialog-confirm-text-title) {
    margin: 0 !important;
    font-size: 16px !important;
  }

  .fileName {
    width: 100%;
    font-size: 12px;
    text-align: center;
    color: #ECECEC;
  }

  .removeTips {
    width: 100%;
    margin-top: 10px;
    margin-bottom: 32px;
    font-size: 14px;
    text-align: center;
    color: #FFFFFF;
  }
}
</style>
