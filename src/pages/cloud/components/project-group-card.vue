<template>
  <div
    class="group"
    :class="currentGroupId === data.id ? 'group-active' : ''"
    ref="group"
    @mouseover="onMouseover"
  >
    <div class="info">
      <div class="info__title">
        项目协作组
        <div class="subtitle">{{ data.list[0].name }}等{{ data.list.length }}个</div>
      </div>
    </div>

    <div class="thumbs">
      <div v-if="!files.length" class="no-thumb">还没有作品</div>

      <div class="thumb" v-for="file in files" :key="file.id">
        <ImgViewer
          :src="file.cover"
          :style="{
            height: '100%',
            width: '100%',
            objectFit: 'cover',
          }"
          thumbnail
        />
        <div class="thumb-mask">{{ file.name }}</div>
      </div>

      <template v-if="files.length && files.length < column">
        <div class="thumb" v-for="index in column - files.length" :key="index">
          <img :src="require('@/assets/img/svg/default.svg')" />
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { setStar } from '@/api'
import membersManageIcon from '@/assets/img/members_manage.png'

export default {
  props: {
    data: {
      type: Object
    },
    windowWidth: {
      type: Number
    },
    isSimple: {
      type: Boolean,
      default: false
    },
    currentGroupId: {
      type: [Number, String]
    }
  },
  data() {
    return {
      membersManageIcon,
      isHover: false,
      isStarred: !!this.data.isStarred
    }
  },
  computed: {
    members: {
      get() {
        return (this.data.members || []).slice(0, 4)
      }
    },
    column() {
      return Math.floor(this.windowWidth / 260)
    },
    files() {
      return this.data.list.reduce((pre, cur) => [...pre, ...cur.files], []).slice(0, this.column)
    }
  },
  methods: {
    onMouseover() {
      this.isHover = true
    },
  }
}
</script>

<style lang="less" scoped>
.group {
  display: flex;
  height: 155px;
  width: 100%;
  padding: 12px;
  background-color: #4d4d4d;
  border: 1px solid #404040;
  border-radius: 10px;
  margin-bottom: 10px;
  box-sizing: border-box;

  &:hover {
    background-color: #575757;
  }

  .info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    &__title {
      flex: 1;
      width: 160px;
      font-size: 14px;
      overflow-wrap: break-word;
      color: #fff;

      .subtitle {
        margin-top: 5px;
        font-size: 12px;
        color: #ccc;
      }
    }

    &__avatars {
      display: flex;
      margin-bottom: 14px;
      //visibility: hidden;

      ::v-deep .avatar {
        margin-right: 4px;

        &:last-child {
          margin-right: 0;
        }
      }
    }

    .more-icon {
      margin-right: 2px;
      visibility: hidden;
      cursor: pointer;
    }

    &__actions {
      display: flex;
      justify-content: space-between;
      color: #868686;
      margin-right: 8px;

      &__file-num {
        flex: 1;
      }
    }
  }

  .thumbs {
    display: flex;
    width: 100%;
    align-content: space-around;
    overflow: hidden;
    cursor: pointer;

    .no-thumb {
      background-color: #434343;
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #868686;
      border-radius: 4px;
    }

    .thumb {
      flex: 1;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;
      width: 200px;
      overflow: hidden;
      border-radius: 5px;
      background-color: #434343;
      position: relative;

      &:last-child {
        margin-right: 0;
      }

      .thumb-mask {
        width: 100%;
        height: 32px;
        background: rgba(0, 0, 0, 0.6);
        position: absolute;
        bottom: 0;
        left: 0;
        display: flex;
        justify-content: left;
        align-items: center;
        padding: 0 4px;
        visibility: hidden;

        span {
          width: 100%;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          display: inline-block;
        }
      }
    }
  }
}

.group-active {
  border: 2px solid #1373e6;
  background-color: #4d4d4d;
  padding: 11px;
}

.group-item-active {
  visibility: visible;
}

.star-icon {
  margin-right: 4px;
  cursor: pointer;
  vertical-align: top;

  &.star-light {
    color: #f2b713;
  }
}
</style>
