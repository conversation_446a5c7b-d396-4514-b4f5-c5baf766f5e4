<template>
  <div>
    <div class="header" v-if="isALL && total">
      <div>{{title}} ({{ total }})</div>
      <div
        @click="more"
        style="display:flex;align-items:center"
      >
        更多
        <IconFont
        icon="fold"
        :size="14"
        style="margin-left:4px;"
      />
      </div>
    </div>

    <div v-if="!isALL" style="margin-bottom:8px">{{ total }}个结果</div>
  </div>
</template>

<script>
export default {
  props: {
    isALL: Boolean,
    total: Number,
    title:String,
  },
  methods: {
    more() {
      this.$emit('more')
    },
  },
}
</script>

<style lang="less" scoped>
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0;
  cursor: pointer;
}
</style>
