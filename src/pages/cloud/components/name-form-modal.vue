<template>
  <Dialog
    v-model="dialogVisible"
    type="info"
    okText="确定"
    :okDisabled="!editName || !validateEditName"
    @ok="editNameConfirm"
  >
    <div class="edit-name-cont">
      <div class="title">{{ title }}</div>
      <g-input
        class="text-input"
        :limit="15"
        v-model="editName"
        placeholder="输入名称"
      >
        <span v-show="!validateEditName&&editName">文件名称不能包含下列字符\/:*?"&#60;>｜.</span>
      </g-input>
    </div>
  </Dialog>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    initValue: {
      type: String,
      default: ''
    },
    value: Boolean
  },
  data() {
    return {
      dialogVisible: false,
      editName: '',
      // eslint-disable-next-line no-useless-escape
      editNameReg: /^[^/\\\:\*\?\"\<\>\|\\\.]{1,255}$/
    }
  },
  watch: {
    value(v) {
      this.dialogVisible = v
    },
    dialogVisible(v) {
      if (!v) {
        this.editName = ''
      } else {
        this.editName = this.initValue
      }
      this.$emit('input', v)
    }
  },
  computed: {
    validateEditName() {
      return this.editNameReg.test(this.editName)
    }
  },
  methods: {
    editNameConfirm() {
      this.$emit('ok', { name: this.editName })
    }
  }
}
</script>
<style lang="less" scoped>
.edit-name-cont {
  .title {
    margin: 8px 0;
    font-size: 14px !important;
    font-weight: 600;
    text-align: center;
  }

  .text-input {
    margin-top: 16px;

    :deep(input::placeholder) {
      color: #919191;
    }
  }
}

:deep(.com-button--disabled) {
  color: #fff !important;
}

:deep(button) {
  border-radius: 6px !important;
}
</style>
