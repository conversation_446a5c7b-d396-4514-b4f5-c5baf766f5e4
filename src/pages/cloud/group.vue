<template>
  <CloudLayout @search="onCloudGSearch">
    <!-- 导航栏左边 -->
    <template #barLeft>
      <div class="back-btn">
        <template v-if="isRoot">
          <IconFont
            icon="return"
            :size="24"
            style="cursor: pointer"
            @click="handleReturn"
          />
          <span>{{ groupInfo && groupInfo.name }}</span>
        </template>

        <template v-else>
          <div style="cursor: pointer;font-size: 12px" @click="handleReturn">上一级</div>
          <div class="divider" />
          <div class="breadcrumbList">
            <div class="breadcrumbItem">
              <span
                style="cursor: pointer; opacity: 0.6"
                @click="$router.push({name: 'groups'})"
              >
                全部小组
              </span>
              <img
                class="separator"
                :src="require('@/assets/img/svg/separator.svg')"
                alt=""
              />
            </div>

            <div
              v-for="(item, index) in breadcrumbList"
              :key="index"
              class="breadcrumbItem"
              @click="skipBreadcrumb(item, index)"
            >
              <span
                style="cursor: pointer"
                :style="{opacity: index === breadcrumbList.length - 1 ? '1' : '0.6'}"
              >
                {{ item.text }}
              </span>
              <img
                v-if="index !== breadcrumbList.length - 1"
                class="separator"
                :src="require('@/assets/img/svg/separator.svg')"
                alt=""
              />
            </div>
          </div>
        </template>
      </div>
    </template>
    <!-- 导航栏右边 -->
    <template #barRight>
      <SaveFileButton @setUploadModalVisible="setUploadModalVisible" />
    </template>

    <!-- 文件列表区域 -->
    <scroll-page
      ref="cloud-list-scroll-page"
      :loading="pageInfo.loading"
      :finished="pageInfo.finished"
      :list="allDataList"
      class="m-page"
      @scroll-bottom="getData"
      @scroll="closeContextMenu"
    >
      <template #list>
        <!-- 文件列表 -->
        <GroupFileList
          ref="GroupFileList"
          :dataList="allDataList"
          @del="handelDel"
          @update="handelUpdate"
        />
      </template>
      <template #empty>
        <div class="empty">
          <div>你可以点击右上角「保存」按钮，将正在PS中编辑的文件存储到这里</div>
          <div>也可以 <img src="@/assets/img/svg/add-folder.svg" alt="" /> 新建文件夹</div>
        </div>
      </template>
    </scroll-page>

    <div class="btns">
      <!-- 上传文件 -->
      <ImportPSDButton @setUploadModalVisible="setUploadModalVisible" />

      <!-- 上传文件夹 -->
      <GButton
        type="primary"
        style="min-width: 0; margin: 0 12px 0 8px; padding: 6px"
        @click="handleAddFolder"
      >
        <img src="@/assets/img/svg/add-folder.svg" alt="" />
      </GButton>
    </div>

    <NameFormModal
      v-model="showAddFolderModal"
      title="新建文件夹"
      @ok="addFolder"
    />

    <DragModal
      v-model="uploadModalVisible"
      classname="uploadModal"
      width="260px"
      :draggable="true"
      :originPosition="{x:200, y:200}"
    >
      <template #content>
        <div class="header">
          <div class="left">
            <div class="info">
              <img v-if="uploadImg === 'progress'" src="@/assets/img/upload-progress.png" :width="16" alt="">
              <img v-else-if="uploadImg === 'failure'" src="@/assets/img/upload-failure.png" :width="16" alt="">
              <img v-else src="@/assets/img/upload-success.png" :width="16" alt="">
              <div style="margin-left: 8px; color: #fff">{{ uploadStatus }}</div>
              <div class="font-bold mx-1" style="font-weight: bold; margin: 0 4px">·</div>
              <div style="transform: scale(0.92) translateX(-2px);">剩余
                {{ $bus.uploadStack.filter(i => i.status === 'progress').length }} 项
              </div>
            </div>

            <div class="icons">
              <Tooltip content="展示全部" placement="bottom-start" :delay="500">
                <img
                  v-show="uploadModalCollapsed"
                  src="@/assets/img/collapsed.png"
                  :width="20"
                  @click="uploadModalCollapsed = !uploadModalCollapsed"
                  alt=""
                />
              </Tooltip>

              <Tooltip content="折叠全部" placement="bottom-start" :delay="500">
                <img
                  v-show="!uploadModalCollapsed"
                  src="@/assets/img/expanded.png"
                  :width="20"
                  @click="uploadModalCollapsed = !uploadModalCollapsed"
                  alt=""
                />
              </Tooltip>

              <Tooltip content="关闭提示" placement="bottom-start" :delay="500">
                <img
                  src="@/assets/img/close.png"
                  :width="20"
                  style="margin: 0 2px 0 12px"
                  @click="uploadModalVisible = false"
                  alt=""
                />
              </Tooltip>
            </div>
          </div>

          <div
            style="height: 4px; width: 100%;"
            :style="{
              width: uploadTotalProgress + '%',
              backgroundColor: '#1373E6'
            }"
          />
        </div>

        <div
          :style="{
            display: uploadModalCollapsed ? 'none' : 'block',
            padding: uploadModalCollapsed ? '0' : '8px',
            paddingTop: 0
          }"
        >
          <div
            v-for="(item, index) in $bus.uploadStack"
            :key="`${item.filename}-${index}`"
            class="uploadItem"
          >
            <div
              class="mask"
              :style="{
                width: (fakeProgressMap[item.id] || 0) + '%',
                borderRadius: (fakeProgressMap[item.id] || 0) === 100 ? '8px' : '8px 0 0 8px'
              }"
            />

            <div class="uploadInfo">
              <div class="filename">{{ item.filename }}</div>
              <div class="info">
                <div>{{ (item.size / Math.pow(1024, 2)).toFixed(2) }} MB</div>
                <div style="font-weight:bold; margin: 0 2px">·</div>
                <div v-if="item.status !== 'failure'">上传至 <span class="path">{{ item.groupName }} </span>中</div>
                <div v-if="item.status === 'failure'" style="color: #C8252D">上传失败</div>
              </div>
            </div>
            <div v-if="item.status === 'failure'" class="operation">
              <img
                src="@/assets/img/delete.png"
                :width="20"
                @click="$bus.uploadStack.splice(index, 1)"
                alt=""
              />
              <img
                src="@/assets/img/retry.png"
                :width="20"
                style="margin-left: 12px"
                @click="retry(item)"
                alt=""
              />
            </div>
          </div>
        </div>
      </template>
    </DragModal>
  </CloudLayout>
</template>

<script>
import {
  checkFileNames,
  getDriveFileList,
  getGroupDetail,
  getUploadInit
} from '@/api/cloud.js'
import { getFilesState } from '@/bridge/api'
import { GROUP_SHARE_WITHME_ID } from '@/utils/const'
import ImportPSDButton from './components/save-file/import-psd-button.vue'
import SaveFileButton from './components/save-file/save-file-button.vue'
import CloudLayout from '@/components/layouts/cloud-layout'
import GroupFileList from './components/group-file-list.vue'
import GroupAddUser from '@/pages/components/popo-user/group-add-user.vue'
import Upload from '@/components/basic/upload'
import NameFormModal from '@/pages/cloud/components/name-form-modal.vue'
import SaveFileModal from '@/pages/cloud/components/save-file/save-file-modal.vue'

export default {
  components: {
    SaveFileModal,
    NameFormModal,
    CloudLayout,
    SaveFileButton,
    ImportPSDButton,
    GroupFileList,
    GroupAddUser,
    Upload
  },
  data() {
    return {
      GROUP_SHARE_WITHME_ID,
      gid: this.$route.params.gid,
      locationId: this.$route.query.locationId || null,

      groupInfo: {
        owner: {
          email: ''
        },
        name: '',
        memberCount: 0
      },

      pageInfo: {
        loading: false,
        finished: false,
        total: 0,
        pageSize: 60,
        pageNum: 0
      },

      dataList: [],
      showAddFolderModal: false,
      uploadModalVisible: false,
      uploadModalCollapsed: false
    }
  },
  computed: {
    isRoot() {
      return false
    },
    breadcrumbList() {
      return this.$bus.breadcrumbList
    },
    uploadImg() {
      if (!this.$bus.uploadStack.length) return 'success'
      if (this.$bus.uploadStack.some(i => i.status === 'progress')) return 'progress'
      if (this.$bus.uploadStack.some(i => i.status === 'failure')) return 'failure'
      return 'success'
    },
    uploadStatus() {
      if (!this.$bus.uploadStack.length) return '上传完成'
      if (this.$bus.uploadStack.some(i => i.status === 'progress')) return '正在上传'
      return '上传完成'
    },
    fakeProgressMap() {
      return this.$bus.fakeProgressMap
    },
    uploadTotalProgress() {
      const list = this.$bus.uploadStack.filter(i => i.status === 'progress')
      if (!list.length) return 0
      return list.reduce((sum, cur) => sum + (this.fakeProgressMap[cur.id] || 0), 0) / list.length
    },
    parentId() {
      return this.$route.query.parentId
    },
    loadingList() {
      const loadingDatas = []
      if (
        !this.dataList.length &&
        !this.$bus.privateUploadFilesList &&
        this.pageInfo.loading
      ) {
        for (let i = 0; i < 24; i++) {
          loadingDatas.push({
            new: true,
            lastUpdater: { collaboratorName: '' }
          })
        }
        return loadingDatas
      }
      return []
    },
    allDataList() {
      const ids = this.dataList.map((item) => item.id)
      // 如果列表数据已经存在，则过滤重复数据
      const privateUploadFilesList = this.$bus.privateUploadFilesList
      const uploadingFiles = privateUploadFilesList.filter(
        (item) => !ids.includes(item.id)
      )
      return [
        ...this.loadingList,
        ...uploadingFiles, // 新的上传中的文件
        ...this.dataList
      ]
    },
    refreshPage() {
      return this.$bus.refreshPage
    },
    downloadFileKey() {
      return this.$bus.downloadFileKey
    },
    validateEditName() {
      return this.editNameReg.test(this.editName)
    }
  },
  watch: {
    refreshPage(val) {
      // 文件上传完成页面刷新事件
      if (val) {
        const { gid } = this.$bus
        if (gid === this.gid || this.gid === GROUP_SHARE_WITHME_ID) {
          setTimeout(async () => {
            this.$refs.GroupFileList.closeContextMenu()
            this.initPage()
            await this.getData()
          }, 1500)
        }
      }
    },
    '$bus.uploadStack': {
      deep: true,
      handler(newVal) {
        if (newVal.length && newVal.every(i => i.status === 'success')) {
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content: '上传完毕，正在刷新列表'
          })
          // setTimeout(() => this.$bus.refreshPage++, 1500)
        }
      }
    },
    downloadFileKey(val) {
      if (val) {
        const index = this.dataList.findIndex((item) => item.fileKey === val)
        index > -1 && (this.dataList[index].state = 'finished')
      }
    }
  },
  created() {
    this.initData()
    this.getData()
    this.$bus.$on('allUploadsCompleted', this.handleAllUploadsCompleted)
  },
  beforeDestroy() {
    this.$bus.$off('allUploadsCompleted', this.handleAllUploadsCompleted)
  },
  methods: {
    async initData() {
      this.$bus.getUploadFilesList(this.gid)
      this.$bus.getDownloadFilesList(this.gid)
      this.$bus.initPrivateUploadFiles(this.gid)
      this.groupInfo =
        this.gid === GROUP_SHARE_WITHME_ID
          ? { name: '与我共享', memberCount: 1 }
          : await getGroupDetail(this.gid)
    },
    // 初始化分页数据
    initPage() {
      this.pageInfo.loading = false
      this.pageInfo.finished = false
      this.pageInfo.total = 0
      this.pageInfo.pageNum = 0
      this.dataList = []
    },
    // 从客户端获取文件状态
    async getStateMapFromClient(params) {
      let stateMap = {}
      try {
        const { data: stateFiles } = await getFilesState(this.gid, {
          gid: this.gid,
          files: params
        })
        stateFiles.data.forEach((item) => {
          stateMap[item.file_key] = item.state
        })
      } catch (error) {
        stateMap = params.map((item) => {
          stateMap[item.file_key] = 'NotExist'
        })
      }
      return stateMap
    },

    // 获取列表数据，组装file的state
    async getData() {
      if (this.pageInfo.loading) {
        return
      }
      this.pageInfo.loading = true
      this.pageInfo.pageNum = this.pageInfo.pageNum + 1

      const { list, pageNum, total, pages } = await getDriveFileList({
        groupId: this.gid,
        parentId: this.parentId,
        pageNum: this.pageInfo.pageNum,
        pageSize: this.pageInfo.pageSize
      })

      // 从客户端获取文件状态
      const params = list.map((item) => {
        return {
          file_key: item.fileKey,
          sid: item.id
        }
      })
      const stateMap = await this.getStateMapFromClient(params)

      // 放入查询回来的列表里
      const newList = list.map((item) => ({
        ...item,
        state: stateMap[item.fileKey] || 'finished'
      }))
      // 赋值列表数据
      if (this.pageInfo.pageNum === 1) {
        this.dataList = newList
      } else {
        this.dataList = [...this.dataList, ...newList]
      }
      // 赋值total
      this.pageInfo.total = total

      // 数据已经到底
      if (pageNum === pages || pages === 0) {
        this.pageInfo.finished = true
      }

      // 滚动到对应位置
      if (pageNum === 1 && this.locationId) {
        this.$nextTick(() => {
          const el = document.getElementById(this.locationId)
          el &&
          this.$refs['cloud-list-scroll-page'].setScrollTop(
            el.parentNode.offsetTop - 90
          )
        })
      }

      this.pageInfo.loading = false
    },
    // 删除刷新列表数据
    handelDel({ id }) {
      this.dataList = this.dataList.filter((item) => item.id !== id)
    },
    // 修改名称
    async handelUpdate() {
      this.initPage()
      await this.getData()
    },
    onAddGroupMembers() {
      this.$CustomDialog({
        title: this.isOwner ? '管理小组成员' : '添加小组成员',
        width: 400,
        contRender: (h) => {
          return h(GroupAddUser, {
            props: {
              groupId: this.groupInfo.id,
              ownerEmail: this.groupInfo.owner.email,
              title: `“${this.groupInfo.name}”小组`
            },
            on: {
              groupUserChanged: (num) => {
                this.groupInfo.memberCount = num
              }
            }
          })
        }
      })
    },
    // 搜索
    onCloudGSearch(keyword) {
      this.$router.push({
        name: 'search',
        params: { kw: keyword }
      })
    },
    // 返回上一页
    handleReturn() {
      this.$bus.breadcrumbList.pop()
      this.$router.back()
    },
    // 滚动触发
    closeContextMenu() {
      this.$refs.GroupFileList.closeContextMenu()
    },
    // 上传文件夹
    handleAddFolder() {
      this.showAddFolderModal = true
    },
    async addFolder({ name }) {
      if (!name) return
      // 重命名检测
      const res = await checkFileNames({
        groupId: this.gid,
        parentId: this.parentId,
        fileNames: [name]
      })
      if (res?.[0]?.isExist) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '文件夹已存在，请更换名称重新新建'
        })
      } else {
        try {
          // 新建文件夹
          const params = {
            type: 1,
            parentId: this.parentId,
            groupId: this.gid,
            name
          }
          await getUploadInit(params)
          // 刷新列表
          this.initPage()
          await this.getData()
        } catch (e) {
          console.log(e.message)
        }
      }
    },
    skipBreadcrumb(item, index) {
      if (index === this.breadcrumbList.length - 1) return
      this.$bus.breadcrumbList.splice(index + 1)
      this.$router.replace({
        name: item.routeName,
        params: item.params,
        query: item.query
      })
    },
    async retry(item) {
      await this.$uploadFileAction(this, item)
    },
    setUploadModalVisible(visible) {
      this.uploadModalVisible = visible
    },
    handleAllUploadsCompleted() {
      this.uploadModalVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
.m-page {
  flex: 1;
  background: #4d4d4d;
  border-top: 1px solid #383838;
  border-left: 1px solid #383838;
  border-top-left-radius: 4px;

  .container-header {
    font-size: 12px;
    margin-bottom: 12px;
  }

  .empty {
    height: 80vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    color: #fff;

    div {
      display: flex;
      align-items: center;

      img {
        margin: 0 2px;
      }
    }
  }
}

.back-btn {
  display: flex;
  align-items: center;
  line-height: 1em;

  .divider {
    width: 1px;
    height: 10px;
    margin: 0 10px;
    background-color: #fff;
  }

  .breadcrumbList {
    display: flex;
    align-items: center;
    height: 17px;
    font-size: 12px;

    .breadcrumbItem {
      display: flex;
      align-items: center;

      .separator {
        width: 14px;
        height: 14px;
        padding: 3px 5px;
        margin: 0 4px;
        opacity: 0.6;
      }
    }
  }
}

.btns {
  position: absolute;
  bottom: 24px;
  right: 12px;
  display: flex;
  align-items: center;
  z-index: 99;
}

:deep(.uploadModal) {
  position: absolute;
  border: 1px solid #5A5A5A !important;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25) !important;
  background: #323232 !important;
  cursor: default;

  .header {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 44px;
    margin: 4px 0;

    .left {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      height: 100%;
      padding: 0 12px;

      .info {
        display: flex;
        align-items: center;
      }

      .icons {
        display: flex;
        align-items: center;

        img {
          padding: 2px;
          cursor: pointer;
          border-radius: 50%;
          border: 1px solid #434343;

          &:hover {
            background-color: #868686;
          }
        }
      }
    }
  }

  .uploadItem {
    position: relative;
    display: flex;
    justify-content: space-between;
    height: 48px;
    padding: 8px;
    margin-bottom: 4px;
    border-radius: 8px;
    border: 1px solid #434343;

    &:hover {
      border: 1px solid #868686;
    }

    .mask {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: #434343;
    }

    .uploadInfo {
      position: relative;
      display: flex;
      flex-direction: column;

      .filename {
        display: inline-block;
        width: 80%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .info {
        display: flex;
        align-items: center;
        font-size: 11px;
        transform: scale(0.92) translateX(-6px) translateY(3px);
        color: #868686;

        .path {
          display: inline-block;
          //width: 30px;
          height: 100%;
          font-weight: bold;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          text-decoration: underline;
          cursor: pointer;
          color: #1373E6;
          vertical-align: middle;
        }
      }
    }

    .operation {
      position: relative;
      display: flex;
      align-items: center;

      img {
        padding: 2px;
        cursor: pointer;
        border-radius: 50%;
        border: 1px solid #434343;

        &:hover {
          background-color: #868686;
        }
      }
    }
  }
}
</style>
