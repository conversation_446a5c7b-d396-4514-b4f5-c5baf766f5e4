<template>
  <CloudLayout
    @search="onCloudGSearch"
    hasCancelBtn
    :keyword="keyword"
    @cancel="onSearchCancel"
    :showSearchWarn="true"
  >
    <!-- 导航栏左边 -->
    <template #barLeft>
      <span
        v-for="tab in tabs"
        :key="tab.key"
        class="category"
        :class="currentCategory === tab.key ? 'category-active' : ''"
        @click="onSwitchTab(tab.key)"
      >{{ tab.name }}</span
      >
    </template>

    <!-- 文件列表区域 -->
    <scroll-page
      ref="list-scroll-page"
      :loading="pageInfo.loading"
      :finished="pageInfo.finished"
      :list="scrollList"
      class="m-page"
      @scroll-bottom="handleBottom"
    >
      <template #list>
        <!-- 小组列表 -->
        <template v-if="['GROUP', 'ALL'].includes(currentCategory)">
          <SearchHeader
            :isALL="isALL"
            :total="isALL ? allData.groups.total : total"
            title="小组"
            @more="onSwitchTab('GROUP')"
          />
          <GroupCardList
            ref="GroupCardList"
            :groups="isALL ? allData.groups.data : data"
            @del="onDel"
            @update="onUpdate"
            :isSimple="isALL"
          />
        </template>

        <!-- 文件列表 -->
        <template v-if="['FILE', 'ALL'].includes(currentCategory)">
          <SearchHeader
            :isALL="isALL"
            :total="isALL ? allData.files.total : total"
            title="文件"
            @more="onSwitchTab('FILE')"
          />
          <GroupFileList
            ref="GroupFileList"
            :dataList="isALL ? allData.files.data : data"
            @del="onDel"
            @update="onUpdate"
            :isSimple="isALL"
          />
        </template>

        <!-- 人列表 -->
        <template v-if="['USER', 'ALL'].includes(currentCategory)">
          <SearchHeader
            :isALL="isALL"
            :total="isALL ? allData.users.total : total"
            title="人"
            @more="onSwitchTab('USER')"
          />
          <UserCard
            :user="user"
            v-for="user in isALL ? allData.users.data : data"
            :key="user.id"
          />
        </template>
      </template>
    </scroll-page>
  </CloudLayout>
</template>

<script>
import CloudLayout from '@/components/layouts/cloud-layout'
import GroupCardList from './components/group-card-list.vue'
import GroupFileList from './components/group-file-list.vue'
import UserCard from './components/user-card.vue'
import SearchHeader from './components/search-header.vue'

import { search } from '@/api'

export default {
  name: 'search',
  components: {
    CloudLayout,
    GroupCardList,
    GroupFileList,
    UserCard,
    SearchHeader
  },
  data() {
    return {
      // 单个Tab页数据
      data: [],
      total: 0,

      tabs: [
        {
          key: 'ALL',
          name: '全部'
        },
        {
          key: 'FILE',
          name: '文件'
        },
        {
          key: 'GROUP',
          name: '小组'
        },
        {
          key: 'USER',
          name: '人'
        }
      ],
      // 全部Tab的数据
      allData: {
        groups: {
          data: [],
          total: 0
        },
        users: {
          data: [],
          total: 0
        },
        files: {
          data: [],
          total: 0
        }
      },

      // 分页数据
      pageInfo: {
        loading: false,
        finished: false,
        pageNum: 0
      },

      // 当前显示的Tab
      currentCategory: 'ALL',
      // 关键字索引
      keyword: this.$route.params.kw
    }
  },
  computed: {
    scrollList: {
      get() {
        if (!this.isALL) {
          return this.data
        } else {
          if (
            this.allData.files.data.length === 0 &&
            this.allData.groups.data.length === 0 &&
            this.allData.users.data.length === 0
          ) {
            return []
          } else {
            return [this.allData]
          }
        }
      }
    },
    isALL() {
      return this.currentCategory === 'ALL'
    }
  },
  async created() {
    await this.getAllData()
  },
  methods: {
    async onCloudGSearch(keyword) {
      if (keyword && keyword !== this.$route.params.kw) {
        this.keyword = keyword
        await this.$router.replace({
          name: 'search',
          params: { kw: this.keyword }
        })
      }
      this.pageInfo.pageNum = 0
      this.getData()
    },
    async getAllData() {
      this.data = []
      this.total = 0
      this.pageInfo.loading = true
      this.pageInfo.finished = false
      try {
        let res = await search('GROUP', this.keyword, 1, 3)
        this.allData.groups = {
          total: res.total,
          data: res.list || []
        }
        res = await search('FILE', this.keyword, 1, 9)
        this.allData.files = {
          total: res.total,
          data: res.list || []
        }
        res = await search('USER', this.keyword, 1, 3)
        this.allData.users = {
          total: res.total,
          data: res.list || []
        }
      } catch (error) {
      }
      this.pageInfo.finished = true
      this.pageInfo.loading = false
    },
    async getData() {
      if (this.isALL) {
        this.getAllData()
        return
      }
      if (this.pageInfo.pageNum === 0) {
        this.pageInfo.finished = false
        this.data = []
        this.total = 0
      }

      this.pageInfo.loading = true
      this.pageInfo.pageNum++

      try {
        const res = await search(
          this.currentCategory,
          this.keyword,
          this.pageInfo.pageNum
        )
        const { list, total } = res || {}
        const newList = list.filter((item) => item)

        if (!newList.length) {
          this.pageInfo.finished = true
          return
        }
        this.data = [...this.data, ...newList]
        this.pageInfo.finished = this.data.length === res.total
        this.total = total
      } catch (error) {
      }
      this.pageInfo.loading = false
    },
    async onSwitchTab(category) {
      if (category === this.currentCategory) {
        return
      }
      this.currentCategory = category
      this.onCloudGSearch()
    },
    onSearchCancel() {
      this.$router.go(-1)
    },
    handleBottom() {
      if (this.isALL) {
        return
      }
      this.getData()
    },

    onDel() {
      if (!this.isALL) {
        this.onCloudGSearch()
      }
    },
    onUpdate(data) {
      const index = this.data.findIndex((item) => item.id === data.id)
      if (index > -1) {
        this.$set(this.data, index, {
          ...this.data[index],
          ...data
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.category {
  position: relative;
  height: 22px;
  margin-right: 28px;
  text-align: center;
  cursor: pointer;
}

.category-active::after {
  content: '';
  display: block;
  width: 100%;
  height: 2px;
  background-color: #fff;
  position: absolute;
  bottom: -8px; /* 将下划线位置往下移动5px */
  left: 0;
}

.m-page {
  flex: 1;
  background: #4d4d4d;
  border-top: 1px solid #383838;
  border-left: 1px solid #383838;
  border-top-left-radius: 4px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 8px 0;
  cursor: pointer;
}
</style>
