<template>
  <CloudLayout @search="onCloudGSearch">
    <!-- 导航栏左边 -->
    <template #barLeft>
      <span style="font-size: 12px">全部小组</span>
    </template>

    <!-- 导航栏右边 -->
    <template #barRight>
      <GButton
        size="small"
        @click="handleShowCreateGroupModal"
        class="upload-btn"
        style="color: #fff"
        ghost
      >
        <img :src="require('@/assets/img/svg/add_group.svg')" width="16" height="16" alt="" />
        <span style="margin-left: 4px; font-weight: normal">创建小组</span>
      </GButton>

      <multi-select
        class="multiSelect"
        v-model="category"
        :options="categoryOptions"
        :width="120"
        :hasValue="true"
        @change="handleCategoryChange"
      >
        <div slot="label" class="categorySelect">
          <img width="8" src="@/assets/img/arrow-down.png" alt="" />
          <span style="margin-left: 4px">{{ curCategoryName }}</span>
        </div>
      </multi-select>

      <SaveFileButton />
    </template>

    <!-- 组别列表区域 -->
    <scroll-page
      ref="list-scroll-page"
      :loading="pageInfo.loading"
      :finished="pageInfo.finished"
      :list="groups"
      class="m-page"
      @scroll="closeMenu"
      @scroll-bottom="handleBottom"
    >
      <template #list>
        <!-- 小组列表 -->
        <GroupCardList
          ref="GroupCardList"
          :groups="groups"
          @del="onDel"
          @update="onUpdate"
        />
      </template>
    </scroll-page>

    <NameFormModal
      v-model="showCreateGroupModal"
      title="创建小组"
      @ok="onGroupCreated"
    />
  </CloudLayout>
</template>

<script>
import { getGroupList, createGroup, getProjectGroupList } from '@/api'
import { getDriveFileList } from '@/api/cloud.js'
import CloudLayout from '@/components/layouts/cloud-layout'
import GroupCardList from './components/group-card-list.vue'
import NameFormModal from './components/name-form-modal'
import SaveFileButton from '@/pages/cloud/components/save-file/save-file-button.vue'

export default {
  components: {
    SaveFileButton,
    CloudLayout,
    GroupCardList,
    NameFormModal
  },
  data() {
    return {
      pageInfo: {
        loading: false,
        finished: false,
        total: 0,
        limit: 200,
        pageNum: 0
      },
      groups: [],
      isProjectGroupAdded: false,
      showCreateGroupModal: false,
      category: 'last_modified',
      groupUserChanged: false,
      currentType: 'GROUP',
      categoryOptions: [
        {
          name: '最近更新',
          id: 'last_modified'
        },
        {
          name: '星标小组',
          id: 'star_group'
        }
      ]
    }
  },
  computed: {
    refreshPage() {
      return this.$bus.refreshPage
    },
    curCategoryName() {
      return this.categoryOptions.find(item => item.id === this.category)?.name
    }
  },
  watch: {
    refreshPage(val) {
      // 文件上传完成页面刷新事件
      if (val) {
        const { gid } = this.$bus
        // 客户端的finished事件推送先于往Java端插记录，导致偶现数据从Java服务查询还是旧数据，用一个延时暂时解决
        setTimeout(() => {
          getDriveFileList({
            groupId: gid,
            pageNum: 1,
            pageSize: 5
          }).then((res) => {
            const { list: fileList } = res
            const index = this.groups.findIndex((item) => item.id === gid)
            if (index > -1) {
              this.$set(this.groups[index], 'files', fileList)
            }
          })
        }, 500)
      }
    }
  },
  async created() {
    await this.getData()

    this.groups.forEach((item) => {
      if (item.name === '与我共享') {
        this.$bus.shareWithMe = item.id
      }
    })

    this.$bus.setBreadcrumbList([])
  },
  async mounted() {
    this.$bus.$on('groupUserChanged', () => {
      this.groupUserChanged = true
    })

    this.$bus.$on('customDialogClosed', async () => {
      if (this.groupUserChanged) {
        this.groups = []
        this.pageInfo.pageNum = 0
        await this.getData()
        this.groupUserChanged = false
      }
    })

    this.$bus.$on('refreshGroupList', () => {
      // 清除缓存，确保获取最新数据
      this.$bus.clearCloudGroupsCache()
      this.groups = []
      this.pageInfo.pageNum = 0
      this.getData()
    })
  },
  methods: {
    handleShowCreateGroupModal() {
      this.$logMsg.info('[handleShowCreateGroupModal] 点击创建小组', '天枢网盘')
      this.showCreateGroupModal = true
    },
    async getData() {
      try {
        this.$logMsg.info('[获取列表][getData] 开始获取数据', '天枢网盘')
        this.pageInfo.loading = true
        const groupList = await getGroupList(this.pageInfo.pageNum + 1, this.category === 'star_group')

        this.groups = [...this.groups, ...groupList.list.map(i => ({ ...i, isProjectGroup: false }))] // 加入新数据
        this.pageInfo.finished = this.groups.length >= groupList.total
        if (groupList.list && groupList.list.length) this.pageInfo.pageNum += 1

        let projectGroupList = []
        if (!this.isProjectGroupAdded) {
          projectGroupList = await getProjectGroupList()
          this.isProjectGroupAdded = true
          this.groups.splice(1, 0, {
            id: new Date().getTime().toString(),
            isProjectGroup: true,
            list: projectGroupList
          })
        }

        // 缓存组列表数据到Bus中，供save-file-modal使用
        if (this.pageInfo.pageNum === 1) { // 只在第一页时缓存完整数据
          this.$bus.setCloudGroupListCache(groupList.list, projectGroupList)
        }

        this.$logMsg.info('[获取列表][getData] 获取数据成功', '天枢网盘')
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: `获取列表失败，${e.message}`
        })
      } finally {
        this.pageInfo.loading = false
      }
    },
    async handleBottom() {
      await this.getData()
    },
    handleCategoryChange(value) {
      this.pageInfo.pageNum = 0
      this.groups = []
      this.pageInfo.finished = false
      this.getData()
    },
    onDel(data) {
      this.groups.splice(this.groups.indexOf(data), 1)
    },
    onUpdate(data) {
      const index = this.groups.findIndex((item) => item.id === data.id)
      if (index > -1) {
        this.$set(this.groups, index, {
          ...this.groups[index],
          ...data
        })
      }
    },
    onGroupCreated({ name }) {
      createGroup({ name })
        .then((res) => {
          if (this.category === 'last_modified') {
            this.groups.splice(1, 0, res)
          }
          this.$CustomToast({
            type: 'success',
            duration: 2,
            content: `成功创建小组 “${name}”`
          })
        })
        .catch((e) => {
          this.$logMsg.error(
            '[onGroupCreated] 小组创建失败：' + e.message,
            'groups'
          )
        })
    },
    // 关闭菜单
    closeMenu() {
      this.$refs.GroupCardList.closeMenu()
    },
    // 搜索
    onCloudGSearch(keyword) {
      this.$router.push({
        name: 'search',
        params: { kw: keyword }
      })
    },
    //
    handleReqTimeChange() {
      console.log('handleReqTimeChange', this.reqTime)
    }
  }
}
</script>

<style lang="less" scoped>
.m-page {
  flex: 1;
  background: #4d4d4d;
  border-top: 1px solid #383838;
  border-left: 1px solid #383838;
  border-top-left-radius: 4px;
}

.container-header {
  display: flex;
  margin-bottom: 12px;
  font-size: 14px;
  align-items: center;

  .category-dropdown,
  .select-dropdown {
    width: 200px;
  }
}

.multiSelect {
  height: 24px;
  margin-left: 10px;
  padding: 2px 8px;
  line-height: 1em;
  border-radius: 4px;
  cursor: pointer;
  color: #fff;
  border: 1px solid #3e3e3e;
  background-color: transparent;

  &:hover {
    border: 1px solid #818181;
  }

  &:active,
  &:visited {
    border-color: #818181;
  }

  &.w-full {
    width: 100%;
  }

  &--circle {
    border-radius: 20px !important;
  }

  .categorySelect {
    display: flex;
    align-items: center;
    color: #fff;
    font-weight: normal !important;
    line-height: 1em;
  }
}
</style>
