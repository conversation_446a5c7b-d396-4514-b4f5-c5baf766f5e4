<template>
  <layout>
    <div class="p-upload">
      <div class="spectrum-Thumbnail">
        <template v-if="loading">
          图片加载中
        </template>

        <template v-else>
          <img
            v-if="formData.thumbnailUrl"
            :src="formData.thumbnailUrl"
            alt=""
            class="spectrum-Thumbnail-image"
          />
          <div v-else class="m-reload">
            图片解析失败，点击
            <span
              class="spectrum-Link spectrum-Link--sizeM a-link"
              @click="handleComObjUpload">
              重新上传
            </span>
          </div>
        </template>
      </div>

      <div class="spectrum-Form spectrum-Form--labelsAbove">
        <div class="spectrum-Form-item">
          <label
            class="spectrum-FieldLabel spectrum-FieldLabel--sizeM spectrum-Form-itemLabel"
            for="spectrum-textinput-instance"
          >
            所属项目
          </label>
          <div class="spectrum-Form-itemField">
            <multi-select
              v-model="formData.projectId"
              :options="projectOptions"
              label="所属项目"
              @change="handleProjectChange"
            />
          </div>
        </div>

        <div
          v-show="tagGroupList && tagGroupList.length > 0"
          class="spectrum-Form-item"
        >
          <label
            class="spectrum-FieldLabel spectrum-FieldLabel--sizeM spectrum-Form-itemLabel"
            for="fieldLabelExample-lifestory"
          >
            标签选择
          </label>
          <div class="spectrum-Form-itemField">
            <div class="spectrum-Textfield spectrum-Textfield--multiline display-flex display-inline">
              <div
                v-for="tagGroup in tagGroupList"
                :key="tagGroup.id"
                class="w-50"
              >
                <multi-select
                  v-model="tagsValue[tagGroup.id]"
                  :label="tagGroup.name"
                  :options="
                    tagGroup.children.map((item) => ({
                      name: item.name,
                      id: item.id,
                    }))
                  "
                  multi
                />
              </div>
            </div>
          </div>
        </div>

        <div class="spectrum-Form-item">
          <label
            class="spectrum-FieldLabel spectrum-FieldLabel--sizeM spectrum-Form-itemLabel"
            for="fieldLabelExample-emailaddress"
          >
            其他标签
          </label>
          <div class="spectrum-Form-itemField">
            <add-tag v-model="formData.tagNames" />
          </div>
        </div>

        <div class="spectrum-Form-item">
          <label
            class="spectrum-FieldLabel spectrum-FieldLabel--sizeM spectrum-Form-itemLabel"
            for="fieldLabelExample-stepper"
          >
            制作耗时
          </label>
          <div class="spectrum-Form-itemField">
            <div class="spectrum-Stepper">
              <div class="spectrum-Textfield display-flex">
                <div class="flex-1">
                  <time-auto-input v-model="timeNum" :units="timeUnit" />
                </div>
                <div class="flex-none ml-12">
                  <multi-select
                    v-model="timeUnit"
                    :options="unitOptions"
                    label="单位"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="u-btns">
        <GButton
          ghost
          shape="circle"
          style="border-width: 2px;"
          type="info"
          @click="handleCancel"
        >
          取消
        </GButton>

        <GButton
          :loading="submitLoading"
          shape="circle"
          style="margin-left: 16px;"
          type="primary"
          @click="handleSubmit"
        >
          确认上传
        </GButton>
      </div>
    </div>
  </layout>
</template>

<script>
import AddTag from './components/add-tag.vue'
import TimeAutoInput from './components/time-auto-input.vue'
import { uploadSmartObjectPsd, deleteFile } from '@/utils/fs/index.js'
import {
  getResourceTagList,
  postResourceUpload,
  postDriveFileSaveToResource, updateResourceWorkTime, updateResourceTag
} from '@/api/index.js'

export default {
  name: 'Upload',
  components: {
    AddTag,
    TimeAutoInput
  },
  data() {
    return {
      loading: true,
      submitLoading: false,
      formData: {
        thumbnailUrl: '',
        projectId: '',
        tagNames: []
      },
      fileName: '',
      tagsValue: {},
      tagGroupList: [],
      timeUnit: 'MINUTE',
      timeNum: 0,
      unitOptions: [
        {
          id: 'MINUTE',
          name: '分钟'
        },
        {
          id: 'HOUR',
          name: '小时'
        }
      ],
      oldTags: []
    }
  },
  computed: {
    mode() {
      if (this.$route.params.mode === 'update') return 'update'
      return 'create'
    },
    projectOptions() {
      return this.$bus.projectList.filter((item) => item.id !== '-1') // 过滤全部项目选项
    },
    tagNames() {
      if (Object.keys(this.tagsValue).length === 0) return []
      const values = Object.values(this.tagsValue)
      const flattenDeep = arr => arr.reduce((acc, val) => acc.concat(Array.isArray(val) ? flattenDeep(val) : val), [])
      return flattenDeep(values)
    }
  },
  watch: {
    'formData.projectId'(val) {
      this.handleProjectChange(val)
    }
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      // 新增
      if (this.mode === 'create') {
        this.$logMsg.info('[upload][initData] 初始化数据', '素材库')
        this.formData.projectId = this.$bus.searchParams.projectId
        const { isCloud, name, ...rest } = this.$route.query
        if (isCloud) {
          // 网盘转存到素材库
          this.formData = { ...this.formData, ...rest }
          this.fileName = name
          this.loading = false
        } else {
          this.handleComObjUpload()
        }
      }

      // 编辑
      if (this.mode === 'update') {
        this.loading = false
        const { tags, thumbnailUrl, workTime, workTimeUnit, projectId, id } = this.$route.params
        this.formData.thumbnailUrl = thumbnailUrl
        this.formData.projectId = projectId
        this.formData.id = id
        this.tagsValue = this.normalizeTags(tags)
        this.tagGroupList = tags
        this.timeNum = Number(workTime)
        this.formData.tagNames = tags.map(i => i.name)
        this.oldTags = tags.map(i => i.name)
        this.timeUnit = workTimeUnit
      }
    },
    async handleComObjUpload() {
      this.$logMsg.info('[upload][handleComObjUpload] 开始上传', '素材库')

      this.loading = true
      try {
        await this.$csCompose()

        const fileName = await this.$csSave()
        const compressData = await this.$compressConfirm(fileName)

        if (compressData) {
          await deleteFile(undefined, fileName)
          this.$logMsg.info('[upload][handleComObjUpload] 上传成功', '素材库')
          await this.$router.replace({
            name: 'list'
          })
          // await this.$csCompose()
          // const cfileName = await this.$csSave()
          // fileName = cfileName
        } else {
          this.$logMsg.info('[upload][handleComObjUpload] 开始合成', '素材库')

          this.fileName = fileName
          const imgdata = await uploadSmartObjectPsd(fileName)
          this.formData = { ...this.formData, ...imgdata }
          this.$logMsg.info('[upload][handleComObjUpload] 上传成功', '素材库')
        }
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: e.message
        })
        this.$logMsg.error(
          '[upload][handleComObjUpload] 上传失败：' + e.message,
          '素材库'
        )
      } finally {
        this.loading = false
      }
    },
    initTagsValue(val) {
      this.tagsValue = {}
      val.forEach((e) => {
        this.$set(this.tagsValue, e.id, [])
      })
    },
    normalizeTags(tags) {
      const tagsValue = {}
      tags.forEach(tag => {
        tagsValue[tag.id] = tag.children?.map(item => item.id) || []
      })
      return tagsValue
    },
    handleProjectChange(id) {
      this.$logMsg.info(
        '[upload][handleProjectChange] 切换项目ID:' + id,
        '素材库'
      )

      if (id) {
        getResourceTagList({
          projectId: id
        })
          .then((d) => {
            this.initTagsValue(d)
            this.tagGroupList = d
            this.$logMsg.info(
              '[upload][handleProjectChange] getResourceTagList查询成功',
              '素材库'
            )
          })
          .catch((e) => {
            this.$logMsg.error(
              '[upload][handleProjectChange] getResourceTagList查询失败',
              '素材库'
            )
          })
      } else {
        this.tagGroupList = []
      }
    },
    handleCancel() {
      this.$logMsg.info('[upload][handleCancel] 取消上传素材', '素材库')
      this.$router.go(-1)
    },
    handleSubmit() {
      if (this.mode === 'create') this.handleCreate()
      else if (this.mode === 'update') this.handleUpdate()
    },
    // 新增
    handleCreate() {
      this.$logMsg.info('[upload][handleSubmit] 开始上传', '素材库')
      const params = {
        ...this.formData,
        name: `${this.fileName}.psd`,
        tagNames: [
          ...this.tagNames.map((item) => item.name),
          ...this.formData.tagNames
        ],
        workTime: this.timeNum,
        workTimeUnit: this.timeUnit
      }

      // 分钟单位时长不能为小数
      if (
        params.workTimeUnit === 'MINUTE' &&
        params.workTime &&
        String(params.workTime).includes('.')
      ) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '时长不能为小数'
        })
        this.$logMsg.error(
          '[upload][handleSubmit] 上传失败：时长不能为小数',
          '素材库'
        )
      } else if (!params.url) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '图层加载失败，请点击重新上传'
        })
        this.$logMsg.error(
          '[upload][handleSubmit] 上传失败：图层加载失败，请点击重新上传',
          '素材库'
        )
      } else if (!params.projectId) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '请选择所属项目'
        })
        this.$logMsg.error(
          '[upload][handleSubmit] 上传失败：请选择所属项目',
          '素材库'
        )
      } else {
        if (this.submitLoading) return
        this.submitLoading = true
        const active = this.$route.query.isCloud
          ? postDriveFileSaveToResource
          : postResourceUpload
        active(params)
          .then(() => {
            this.$CustomToast({
              type: 'success',
              duration: 2,
              content: '素材上传成功'
            })
            // this.$bus.setSearchValue('projectId', params.projectId)
            // this.$bus.$emit('search')
            this.$router.replace({
              name: 'list',
              query: {
                refresh: true
              }
            })
            this.$logMsg.info('[upload][handleSubmit] 上传成功', '素材库')
          })
          .catch((e) => {
            this.$logMsg.error(
              '[upload][handleSubmit] 上传失败 ' + e.message,
              '素材库'
            )
          }).finally(() => {
          this.submitLoading = false
        })
      }
    },
    // 更新
    async handleUpdate() {
      try {
        // 时间
        await updateResourceWorkTime({
          resourceIds: [this.formData.id],
          workTime: this.timeNum,
          workTimeUnit: this.timeUnit
        })
        // 标签
        const oldTags = this.oldTags
        const newTags = this.formData.tagNames
        const addTagNames = newTags.filter(tag => !oldTags.includes(tag))
        const delTagNames = oldTags.filter(tag => !newTags.includes(tag))
        await updateResourceTag({
          resourceIds: [this.formData.id],
          addTagNames,
          delTagNames
        })
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: '更新成功'
        })
        await this.$router.push({
          name: 'list'
        })
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: `更新失败，${e.message}`
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.p-upload {
  width: 100%;
  height: 100%;
  padding: 0 12px;
  overflow-y: scroll;
  border-left: #383838 1px solid;

  .m-reload {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    text-align: center;
    padding-top: 90px;
    z-index: 2;

    .a-link {
      cursor: pointer;
    }
  }

  .spectrum-Thumbnail {
    position: relative;
    width: 100%;
    height: 195px;
    margin: 12px 0;
    background-color: #5a5a5a;
    background-image: none;
  }

  .spectrum-Textfield,
  .spectrum-Form-itemField {
    width: 100%;
  }

  .display-inline {
    margin-bottom: -8px;

    .w-50 {
      width: 50%;
      margin-bottom: 8px;

      &:nth-child(odd) {
        padding-right: 4px;
      }

      &:nth-child(even) {
        padding-left: 4px;
      }
    }
  }

  .ml-12 {
    margin-left: 12px;
  }

  .spectrum-Tag {
    padding: 0 14px;
  }

  .display-flex {
    display: flex;
    flex-wrap: wrap;

    .flex-1 {
      flex: 1;
      width: 0;
    }

    .flex-none {
      flex: none;
    }
  }

  .u-btns {
    margin: 40px 0;
    text-align: right;
  }
}

:deep(.spectrum-Menu-item) {
  padding-left: 8px;
}
</style>
