<template>
  <div class="m-waterfall" :style="waterFullClass">
    <div
      v-for="item in dataList"
      :key="item.id"
      class="img-item"
      :style="{
        width: itemWidth ? `${itemWidth}px` :`auto`,
        height: itemHeight ? `${itemHeight}px` :`${height}px`
      }"
    >
      <slot :item="item">
        <thumbnail
          :item="item"
          :scale="scale"
          @item-click="handleItemClick"
          @refresh="$emit('refresh')"
        />
      </slot>
    </div>
  </div>
</template>

<script>
import Thumbnail from './thumbnail'

export default {
  components: {
    Thumbnail
  },
  props: {
    dataList: {
      type: Array,
      default() {
        return []
      }
    },
    scale: {
      type: Number,
      default: 0
    }, // 比例 0-100
    gap: {
      type: Number,
      default: 4
    },
    itemWidth: {
      type: Number,
      default: 0
    },
    itemHeight: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      minW: 72,
      minH: 56,
      maxW: 464
    }
  },
  computed: {
    width() {
      return parseInt(String(this.minW + ((this.maxW - this.minW) * this.scale) / 100))
    },
    height() {
      return parseInt(String((this.width * this.minH) / this.minW))
    },
    waterFullClass() {
      return {
        gridTemplateColumns: `repeat(auto-fill, minmax(${this.width}px, 1fr))`
      }
    }
  },
  methods: {
    handleItemClick(item) {
      this.$emit('item-click', item)
    }
  }
}
</script>

<style lang="less" scoped>
.m-waterfall {
  display: grid;
  width: 100%;
  height: auto;
  grid-row-gap: 4px;
  grid-column-gap: 4px;

  .img-item {
    width: 100%;
    height: 96px;
  }
}
</style>
