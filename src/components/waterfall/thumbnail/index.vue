<template>
  <div class="thumbnail" @mouseover.capture="handleHover" ref="thumbnail">
    <div
      v-if="scale <= 40"
      class="preview-image"
      :class="{
        'preview-image--bottom': !overflowTop,
        'preview-image--top': overflowTop,
        'preview-image--right': overflowRight,
        'preview-image--left': !overflowRight,
      }"
    >
      <ImgViewer
        style="width: 100%; height: auto;"
        ref="previewImage"
        :alt="item.name"
        :src="item.thumbnailUrl"
      />
      <div class="detail-info">
        <div class="name">{{ item.name }}</div>
        <div class="info">
          <span>{{ (item.width && item.height) ? `${item.width} \&times; ${item.height} px` : '*' }}</span>
          <span>{{ item.updateTime?.split(' ')[0] || '-' }}</span>
        </div>
      </div>
    </div>

    <div class="spectrum-Thumbnail" @click="handleItemClick(item)" style="padding: 0">
      <ImgViewer
        :style="{
          width: isWidthAuto ? 'auto' : '100%',
          height: isWidthAuto ? '100%' : 'auto',
        }"
        :alt="item.name"
        :src="item.thumbnailUrl"
        class="spectrum-Thumbnail-image"
        thumbnail
      />
    </div>

    <img :src="getImg(item)" class="u-tag" height="14" alt />

    <IconFont
      @click="onStar"
      class="star-icon"
      :icon="isStar ? 'star' : 'unstar'"
      :class="isStar ? 'star-light' : ''"
      :size="20"
    />

    <div v-if="scale <= 40" class="u-options" @click.stop="handlePut(item)">下载</div>

    <div v-if="scale > 40" class="u-options mode">
      <div class="imgInfo">
        <div class="name">{{ item.name }}</div>
        <div class="size">
          <span>{{ (item.width && item.height) ? `${item.width} \&times; ${item.height} px` : '*' }}</span>
          <span style="margin-left: 10px;">{{ item.updateTime?.split(' ')[0] || '-' }}</span>
        </div>
      </div>
      <div class="downloadBtn" @click.stop="handlePut(item)">下载</div>
    </div>

    <div v-if="loadingView" class="u-options-loading">
      <div class="progress">
        <div class="u-options-progress" :style="{ width: `${processNum}%` }" />
        <div class="u-options-text">{{ processNum }}%</div>
      </div>
    </div>
  </div>
</template>

<script>
import FilePutMixin from '@/mixins/filePut'
import { starResource } from '@/api/cloud'
import { cancelRequest } from '@/utils/request'

export default {
  mixins: [FilePutMixin],
  props: {
    item: {
      type: Object,
      default() {
        return null
      }
    },
    scale: {
      type: Number
    }
  },
  data() {
    return {
      windowHeight: window.innerHeight,
      windowWidth: window.innerWidth,
      overflowRight: false,
      overflowTop: false,
      isStar: false,
      loading: false
    }
  },
  computed: {
    isWidthAuto() {
      const { width, height } = this.item
      return width / height < 1.38
    },
    processNum() {
      return this.$bus.downloadFiles[this.item.id] || 0
    },
    loadingView() {
      return Object.prototype.hasOwnProperty.call(
        this.$bus.downloadFiles,
        this.item.id
      )
    }
  },
  created() {
    window.addEventListener('resize', () => {
      this.windowWidth = window.innerWidth
      this.windowHeight = window.innerHeight
      this.overflowTop = false
      this.overflowRight = false
    })
    this.isStar = this.item.starSelected
  },
  methods: {
    handleHover() {
      const {
        width: prewW,
        height: prewH
      } = this.$refs.previewImage.$el.getBoundingClientRect()
      const {
        top: boxT,
        left: boxL
      } = this.$refs.thumbnail.getBoundingClientRect()
      this.overflowRight = boxL + prewW > this.windowWidth - 10 // 10为滚轮宽度
      this.overflowTop = boxT - 125 > prewH // 125为导航高度
    },
    handleItemClick(item) {
      this.$emit('item-click', item)
    },
    getImg(item) {
      const allExt = [
        'AI',
        'EPS',
        'GIF',
        'JPG',
        'MP4',
        'PNG',
        'PSB',
        'PSD',
        'SVG',
        'WEBP'
      ]
      const ext = item.ext.toUpperCase()
      if (!allExt.includes(ext)) return null
      return require(`@/assets/img/format/${ext}.png`)
    },
    handlePut(item) {
      let fileName
      const ext = item.ext || 'jpg'
      if (item.id) fileName = item.id
      else fileName = new Date().getTime().toString() + Math.random().toString(36).slice(2, 11)
      this.handelFilePut(item, `${fileName}.${ext}`) // mixin方法
    },
    async onStar(e) {
      if (this.loading) return
      try {
        this.loading = true
        window.getSelection().removeAllRanges()
        e.preventDefault()
        await starResource({
          resourceId: this.item.id,
          starType: this.isStar ? 'UN_STAR' : 'STAR'
        })
        this.$CustomToast({
          type: 'success',
          duration: 2,
          content: `${this.isStar ? '取消' : '添加'}星标成功`
        })
        this.isStar = !this.isStar
        if (!this.isStar) this.$emit('refresh')
      } catch (e) {
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: `星标成功，${e.message}`
        })
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.thumbnail {
  position: relative;
  width: 100%;
  height: 100%;

  &:hover {
    .preview-image {
      display: block;
    }

    .star-icon {
      display: block;
      cursor: pointer;
    }

    .u-options {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .cancel-text {
      background-color: red !important;
      color: #fff !important;
      z-index: 9;
    }
  }

  .u-tag {
    position: absolute;
    left: 4px;
    top: 4px;
    z-index: 1;
  }

  .star-icon {
    display: none;
    position: absolute;
    right: 4px;
    top: 4px;
    z-index: 10;

    &.star-light {
      color: #f2b713;
    }
  }

  .u-options {
    display: none;
    position: absolute;
    width: 100%;
    height: 20px;
    bottom: 0;
    color: #fff;
    text-align: center;
    line-height: 20px;
    background: #1473e6;
    cursor: pointer;
    z-index: 2;
    border-radius: 0 0 4px 4px;
  }

  .mode {
    display: none;
    justify-content: space-between !important;
    height: 45px;
    padding: 7px;
    cursor: default !important;
    background: rgba(0, 0, 0, 0.6);

    .imgInfo {
      display: flex;
      flex-direction: column;
      justify-content: space-evenly;
      align-items: flex-start;
      align-content: flex-start;

      .name {
        width: 200px;
        flex: 0.8;
        font-size: 12px;
        font-weight: 600;
        text-align: start;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        color: #FFFFFF;
      }

      .size {
        font-size: 11px;
        color: #868686;
      }
    }

    .downloadBtn {
      width: 40px;
      height: 24px;
      text-align: center;
      line-height: 24px;
      border-radius: 4px;
      cursor: pointer;
      border: 1px solid #0E56AD;
      background-color: #1473e6;
    }
  }

  .u-options-loading {
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 20px;
    padding: 0 4px;
    color: #333;
    line-height: 20px;
    z-index: 2;
    border-radius: 0 0 4px 4px;
    //overflow: hidden;

    .progress {
      position: absolute;
      left: 0;
      width: 100%;
      height: 100%;
      background: #ffffff !important;

      .u-options-progress {
        position: absolute;
        left: 0;
        bottom: 0;
        height: 20px;
        background: #1473e6;
      }

      .u-options-text {
        position: absolute;
        right: 4px;
        bottom: 0;
        height: 20px;
        color: #333;
      }
    }

    .cancel-text {
      position: absolute;
      right: 0;
      bottom: 0;
      width: 20%;
      height: 20px;
      text-align: center;
      cursor: pointer;
      z-index: 3;
      color: red;
      background-color: transparent;

      &:hover {
        font-weight: bold;
      }
    }
  }

  .spectrum-Thumbnail {
    width: 100%;
    height: 100%;
    background-color: #5a5a5a;
    background-image: none;
    border-radius: 4px;
    padding: 14px;

    &::before {
      box-shadow: none;
    }

    &-image {
      object-fit: cover;
      object-position: center;
      z-index: 0;
    }
  }

  .preview-image {
    display: none;
    position: absolute;
    z-index: 99999;
    width: 265px;
    background-color: red;
    border-radius: 5px;
    background-color: #5a5a5a;
    overflow: hidden;
    border: 1px solid #636363;
    box-shadow: 4px 4px 16px #00000060;

    &--left {
      left: 0;
    }

    &--right {
      right: 0;
    }

    &--top {
      bottom: 100%;
      margin-bottom: 4px;
    }

    &--bottom {
      top: 100%;
      margin-top: 4px;
    }

    &--overflow {
      height: auto;
    }

    .detail-info {
      background: #4d4d4d;
      width: 100%;
      padding: 7px;
      color: #868686;
      font-size: 12px;

      .name {
        font-weight: bold;
        word-break: break-all;
        color: #fff;
      }

      .info {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
    }
  }
}
</style>
