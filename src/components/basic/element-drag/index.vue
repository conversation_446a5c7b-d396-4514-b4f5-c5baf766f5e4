<template>
  <div
    class="drag-outer"
    ref="dragWrap"
    :style="outerOptions"
    @mouseenter="isHover = true"
    @mouseleave="isHover = isMousedown = false"
    @mousemove="dragMousemove"
  >
    <div
      class="drag-inner"
      ref="dragElement"
      :style="innerOptions"
      @mousedown="dragMousedown"
      @mouseup.stop="isMousedown = false"
    >
      <slot></slot>
    </div>
    <div class="operate-btns">
      <div class="operate-btn" @click.prevent="handleZoom('out')">
        <IconFont icon="zoom-out" :size="18"/>
      </div>
      <div class="operate-btn" @click.prevent="handleZoom('in')">
        <IconFont icon="zoom-in" :size="18"/>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ElementDrag',
  props: {
    outerOptions: {
      type: Object,
      default() {
        return {
          background: '#424242',
        }
      },
    },
    innerOptions: {
      type: Object,
      default() {
        return {
          background: 'transparent',
        }
      },
    },
    scaleZoom: {
      type: Object,
      default() {
        return {
          max: 5,
          min: 0.2,
        }
      },
    },
    radio: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      isMousedown: false,
      isHover: false,
      moveStart: {},
      translate: { x: 0, y: 0 },
      scale: 1,
    }
  },
  watch: {
    radio(val) {
      // console.log('radio', val)
      this.scale = val
      this.translate.x = 0
      this.translate.y = 0
      this.$refs.dragElement.style.transform = `scale(${this.scale}) translate(${this.translate.x}px, ${this.translate.y}px)`
    },
  },
  methods: {
    reset(radio) {
      this.scale = radio || 1
      this.translate.x = 0
      this.translate.y = 0
      this.$refs.dragElement.style.transform = `scale(${this.scale}) translate(${this.translate.x}px, ${this.translate.y}px)`
    },
    handleScroll(e) {
      if (this.isHover) {
        const speed = e.wheelDelta / 120
        const step =  0.04 * speed
        const {min,max} = this.scaleZoom
        this.scale =Math.max(Math.min(this.scale + step, max),min)  
        this.$refs.dragElement.style.transform = `scale(${this.scale}) translate(${this.translate.x}px, ${this.translate.y}px)`
      }
    },
    handleZoom(type) {
      const step = Math.max(this.scale*0.2,0.01)
      if (type === 'in' && this.scale < this.scaleZoom.max) {
        this.scale = parseFloat((this.scale+step).toFixed(2))
        this.$refs.dragElement.style.transform = `scale(${this.scale}) translate(${this.translate.x}px, ${this.translate.y}px)`
      } else if (type === 'out' && this.scale > this.scaleZoom.min) {
        this.scale = parseFloat((this.scale-step).toFixed(2))
        this.$refs.dragElement.style.transform = `scale(${this.scale}) translate(${this.translate.x}px, ${this.translate.y}px)`
      }
    },
    dragMousedown(event) {
      this.moveStart.x = event.clientX
      this.moveStart.y = event.clientY
      this.isMousedown = true
    },
    dragMousemove(event) {
      if (this.isMousedown) {
        this.translate.x += (event.clientX - this.moveStart.x) / this.scale
        this.translate.y += (event.clientY - this.moveStart.y) / this.scale
        this.$refs.dragElement.style.transform = `scale(${this.scale}) translate(${this.translate.x}px, ${this.translate.y}px)`
        this.moveStart.x = event.clientX
        this.moveStart.y = event.clientY
      }
    },
  },
  mounted() {
    window.addEventListener('mousewheel', this.handleScroll, false)
  },
}
</script>

<style lang="less" scoped>
.drag-outer {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  .drag-inner {
    transform-origin: center center;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: move;
    user-select: none;
    > * {
      -webkit-user-drag: none;
      user-drag: none;
    }
  }
  .operate-btns {
    position: absolute;
    display: flex;
    justify-content: space-between;
    width: 64px;
    bottom: 8px;
    left: 50%;
    transform: translateX(-50%);
    .operate-btn {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      background: rgba(66, 66, 66, 1);
      font-size: 18px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      color: #989898;

      &:hover {
        color: #ffffff;
      }
    }
  }
}
</style>
