<template>
  <div class="img-viewer">
    <img
      class="img"
      v-if="url"
      :src="url"
      @error="onError"
      alt=""
    />
    <div v-if="imgName" class="mock">{{ imgName }}</div>
  </div>
</template>

<script>
export default {
  name: 'ImgViewer',
  props: {
    src: {
      type: String,
      default: ''
    },
    imgName: {
      type: String,
      default: ''
    },
    defaultSrc: {
      type: String,
      required: false,
      default: require('@/assets/img/svg/default.svg')
    },
    thumbnail: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    url() {
      if (this.src && this.thumbnail) {
        const ext = this.src.split('.').pop()
        let paramsStr = '?imageView&thumbnail=500x0'
        if (ext === 'gif') {
          paramsStr = '?imageView&type=png&thumbnail=500x0'
        } else if (ext === 'svg') {
          paramsStr = ''
        }
        return `${this.src}${paramsStr}`
      }
      return this.src || this.defaultSrc
    }
  },
  methods: {
    onError(e) {
      e.target.src = this.defaultSrc
      e.target.style.width = '48px'
      e.target.style.height = '48px'
    }
  }
}
</script>

<style lang="less" scoped>
.img-viewer {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  cursor: pointer;

  .img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .mock {
    position: absolute;
    bottom: 0;
    left: 0;
    display: none;
    width: 100%;
    height: 32px;
    padding: 8px;
    font-size: 12px;
    font-weight: bold;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: #FFFFFF;
    background: rgba(0, 0, 0, 0.6);
  }

  &:hover .mock {
    display: block;
  }
}
</style>
