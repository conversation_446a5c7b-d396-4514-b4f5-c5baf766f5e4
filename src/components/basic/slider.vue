<template>
  <div class="slider-scroll" :class="{ drag: dragging }" @click="handleClick">
    <div class="value" v-if="showValue">{{ localV }}</div>
    <div class="slide-scrollbox">
      <div class="slider-rail"></div>
      <div class="slider-track" :style="{ width: ratio * 100 + '%' }"></div>
      <div
        class="slider-handle"
        :style="{ left: ratio * 100 + '%' }"
        @mousedown="startDrag"
        ref="handle"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    showValue: Boolean,
    value:Number
  },
  data() {
    return {
      localV: 0,
      dragging: false,
      ratio: 0,
    }
  },
  watch:{
    localV(v) {
      this.$emit('input', v)
    },
    value:{
      handler(v){
        this.localV = v || 0
        this.ratio = this.localV / 100
      },
      immediate: true
    }
  },
  methods: {
    startDrag(event) {
      event.preventDefault()
      this.dragging = true
      this.calculateValue(event.clientX)
    },
    stopDrag() {
      this.dragging = false
    },
    drag(event) {
      if (!this.dragging) return
      this.calculateValue(event.clientX)
    },
    handleClick(event) {
      this.calculateValue(event.clientX)
    },
    calculateValue(clientX) {
      const rect = this.$el.getBoundingClientRect()
      const ratio = (clientX - rect.left) / rect.width

      this.ratio = Math.min(Math.max(0, ratio), 1)
      this.localV = parseInt(100 * this.ratio)
    },
  },
  mounted() {
    window.addEventListener('mousemove', this.drag)
    window.addEventListener('mouseup', this.stopDrag)
  },
  beforeDestroy() {
    window.removeEventListener('mousemove', this.drag)
    window.removeEventListener('mouseup', this.stopDrag)
  },
}
</script>

<style scoped lang="less">
.slider-scroll {
  width: 100%;

  &.drag {
    cursor: pointer;
    .slide-scrollbox .slider-handle {
      transform: scale(1.2) translate(-7px, -40%);
    }
  }
  .value {
    color: #fff;
    margin-bottom: 3px;
    text-align: center;
  }
  .slide-scrollbox {
    width: 100%;
    position: relative;
    height: 4px;

    .slider-rail,
    .slider-track {
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      border-radius: 2px;
      background: #ccc;
      height: 100%;
    }
    .slider-rail {
      width: 100%;
    }
    .slider-handle {
      position: absolute;
      top: 50%;
      transform: translate(-7px, -50%);
      z-index: 9;

      cursor: pointer;
      width: 14px;
      height: 14px;
      background: #fff;
      border-radius: 50%;
      border: 2px solid #3d7af5;

      &:hover {
        transform: scale(1.2) translate(-7px, -40%);
      }
    }
  }
}
</style>
