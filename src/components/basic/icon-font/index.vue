<template>
  <svg class="iconfont" aria-hidden="true" @click="(e)=>$emit('click',e)" :style="sizeStyle">
    <use :xlink:href="href"></use>
  </svg>
</template>

<script >
import './iconfont'
export default {
  name: 'IconFont',
  components: {},
  props: {
    icon: String,
    size: [Number,String],
  },
  computed: {
    href () {
      return '#' + 'dubhe-icon-' + this.icon
    },
    sizeStyle () {
      if (this.size === 0) {
        return {}
      } else {
        return {
          width: this.size + 'px',
          height: this.size + 'px'
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.iconfont {
  display: inline;
  width: 1em;
  height: 1em;
  vertical-align: middle;
  fill: currentColor;
  overflow: hidden;
}
</style>
