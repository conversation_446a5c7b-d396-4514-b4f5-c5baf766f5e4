<template>
  <div
    :class="[prefixCls]"
    @mouseenter="handleShowPopper"
    @mouseleave="handleClosePopper"
  >
    <div :class="[prefixCls + '-rel']" ref="reference">
      <slot></slot>
    </div>
    <transition name="fade">
      <div
        :class="[prefixCls + '-popper', prefixCls + '-' + theme]"
        :style="dropStyles"
        ref="popper"
        v-show="!disabled && (visible || always)"
        @mouseenter="handleShowPopper"
        @mouseleave="handleClosePopper"
        :data-transfer="transfer"
        v-transfer-dom
      >
        <div :class="[prefixCls + '-content']" style="padding-left: 4px;">
          <div :class="[prefixCls + '-arrow']"></div>
          <div :class="innerClasses" :style="innerStyles">
            <slot name="content" >{{ content }}</slot>
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import Popper from '@/mixins/popper'
import TransferDom from '@/utils/transfer-dom'
import { oneOf } from '@/utils'
import { transferIndex, transferIncrease } from '@/utils/transfer-queue'

const prefixCls = 'tooltip'
export default {
  name: 'Tooltip',
  directives: { TransferDom },
  mixins: [Popper],
  props: {
    placement: {
      validator(value) {
        return oneOf(value, [
          'top',
          'top-start',
          'top-end',
          'bottom',
          'bottom-start',
          'bottom-end',
          'left',
          'left-start',
          'left-end',
          'right',
          'right-start',
          'right-end'
        ])
      },
      default: 'bottom'
    },
    content: {
      type: [String, Number],
      default: ''
    },
    delay: {
      type: Number,
      default: 100
    },
    disabled: {
      type: Boolean,
      default: false
    },
    controlled: {
      // under this prop,Tooltip will not close when mouseleave
      type: Boolean,
      default: false
    },
    always: {
      type: Boolean,
      default: false
    },
    transfer: {
      type: Boolean,
      default() {
        return !this.$IVIEW || this.$IVIEW.transfer === ''
          ? false
          : this.$IVIEW.transfer
      }
    },
    theme: {
      validator(value) {
        return oneOf(value, ['dark', 'light'])
      },
      default: 'light'
    },
    maxWidth: {
      type: [String, Number]
    }
  },
  data() {
    return {
      prefixCls: prefixCls,
      tIndex: this.handleGetIndex(),
      visible: false
    }
  },
  computed: {
    innerStyles() {
      const styles = {}
      if (this.maxWidth) styles['max-width'] = `${this.maxWidth}px`
      return styles
    },
    innerClasses() {
      return [
        `${prefixCls}-inner`,
        {
          [`${prefixCls}-inner-with-width`]: !!this.maxWidth
        }
      ]
    },
    dropStyles() {
      const styles = { 'z-index': 999 }
      if (this.transfer) styles['z-index'] = 1060 + this.tIndex
      return styles
    }
  },
  watch: {
    content() {
      this.updatePopper()
    }
  },
  methods: {
    handleShowPopper() {
      if (this.timeout) clearTimeout(this.timeout)
      this.timeout = setTimeout(() => {
        this.visible = true
      }, this.delay)
      this.tIndex = this.handleGetIndex()
    },
    handleClosePopper() {
      if (this.timeout) {
        clearTimeout(this.timeout)
        if (!this.controlled) {
          this.timeout = setTimeout(() => {
            this.visible = false
          }, 100)
        }
      }
    },
    handleGetIndex() {
      transferIncrease()
      return transferIndex
    }
  },
  mounted() {
    if (this.always) {
      this.updatePopper()
    }
  }
}
</script>

<style lang="less" scoped>
// @tooltip-prefix-cls: ~"@{css-prefix}tooltip";
// @tooltip-arrow: ~"@{tooltip-prefix-cls}-arrow";
// @tooltip-max-width: 250px;
// @tooltip-arrow-width: 5px;
// @tooltip-distance: @tooltip-arrow-width - 1 + 4;

// @tooltip-arrow-width-light: 7px;
// @tooltip-distance-light: @tooltip-arrow-width-light - 1 + 4;
// @tooltip-arrow-outer-width-light: (@tooltip-arrow-width-light + 1);
// @tooltip-arrow-color: hsla(0,0%,85%,.5);

.tooltip {
    display: inline-block;

    &-rel{
        display: inline-block;
        position: relative;
        width: inherit;
    }

    // &-popper{
    //     .popper(@tooltip-arrow, @tooltip-arrow-width, @tooltip-distance, @tooltip-bg);
    // }
    // &-light&-popper{
    //     .popper(@tooltip-arrow, @tooltip-arrow-width-light, @tooltip-distance-light, @tooltip-arrow-color);

    //     &[x-placement^="top"] .@{tooltip-arrow}:after {
    //         content: " ";
    //         bottom: 1px;
    //         margin-left: -@tooltip-arrow-width-light;
    //         border-bottom-width: 0;
    //         border-top-width: @tooltip-arrow-width-light;
    //         border-top-color: #fff;
    //     }

    //     &[x-placement^="right"] .@{tooltip-arrow}:after {
    //         content: " ";
    //         left: 1px;
    //         bottom: -@tooltip-arrow-width-light;
    //         border-left-width: 0;
    //         border-right-width: @tooltip-arrow-width-light;
    //         border-right-color: #fff;
    //     }

    //     &[x-placement^="bottom"] .@{tooltip-arrow}:after {
    //         content: " ";
    //         top: 1px;
    //         margin-left: -@tooltip-arrow-width-light;
    //         border-top-width: 0;
    //         border-bottom-width: @tooltip-arrow-width-light;
    //         border-bottom-color: #fff;
    //     }

    //     &[x-placement^="left"] .@{tooltip-arrow}:after {
    //         content: " ";
    //         right: 1px;
    //         border-right-width: 0;
    //         border-left-width: @tooltip-arrow-width-light;
    //         border-left-color: #fff;
    //         bottom: -@tooltip-arrow-width-light;
    //     }
    // }

    &-inner{
      max-width: 250px;
      padding: 8px 12px;
      color: #545454;
      text-align: left;
      text-decoration: none;
      background-color: #EEEEEE;
      border-radius: 2px;
      // box-shadow: @shadow-base;
      border: 1px solid #DEDEDE;
      white-space: nowrap;

      &-with-width{
          white-space: pre-wrap;
          text-align: justify;
      }
    }

    &-dark &-inner{
      background-color: #535353;
      border-color: #535353;
      box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.25);
      color: #989898;
    }

    // &-light &-inner{
    //     background-color: #fff;
    //     color: #545454;
    // }

    &-arrow{
        position: absolute;
        width: 0;
        height: 0;
        border-color: transparent;
        border-style: solid;
    }

    // &-light {
    //     .@{tooltip-arrow}{
    //         &:after{
    //             display: block;
    //             width: 0;
    //             height: 0;
    //             position: absolute;
    //             border-color: transparent;
    //             border-style: solid;
    //             content: "";
    //             border-width: @tooltip-arrow-width-light;
    //         }
    //         border-width: @tooltip-arrow-outer-width-light;
    //     }
    // }
}
</style>
