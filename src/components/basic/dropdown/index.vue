<template>
  <div
    class="dropdown"
    @mouseover="trigger === 'hover' && (show = true)"
    @mouseleave="trigger === 'hover' && (show = false)"
  >
    <div class="dropdown-rel" @click="trigger === 'click' &&  (show = !show)">
      <slot v-if="$slots.default"></slot>
      <template v-else>
        <IconFont icon="down-arrow2" :size="16" />
        {{ label }}
      </template>
    </div>

    <div
      v-if="trigger === 'click'"
      v-show="show"
      class="dropdown-transform-dom"
      @click="trigger === 'click' &&  (show = false)"
    />

    <div v-show="show" class="select-dropdown" :style="placementStyle" @click.stop>
      <slot v-if="$slots.list" name="list"></slot>
      <ul v-else>
        <li
          v-for="opt in options"
          :key="opt.value"
          :class="{ 'error': opt.error, 'selected': opt.label === label }"
          @click.prevent="handleClick(opt)"
        >
          {{ opt.label }}
          <IconFont
            v-show="opt.label === label"
            icon="tick"
            :size="14"
            style="vertical-align: -3px;"
          />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Dropdown',
  props: {
    options: {
      type: Array,
      default: () => []
    },
    value: {
      type: String,
      default: ''
    },
    placement: {
      type: String,
      default: 'bottom-start'
    },
    offset: {
      type: Number,
      default: 0
    },
    trigger: {
      type: String,
      default: 'click'
    }
  },
  data() {
    return {
      show: false,
      label: ''
    }
  },
  computed: {
    placementStyle() {
      const placementArr = this.placement.split('-')
      const styleArr = {}
      if (placementArr.includes('top')) {
        styleArr.bottom = '100%'
      }
      if (placementArr.includes('bottom')) {
        styleArr.top = '100%'
      }
      if (placementArr.includes('start')) {
        styleArr.left = '0'
      }
      if (placementArr.includes('end')) {
        styleArr.right = '0'
      }
      if (this.offset) {
        styleArr.marginRight = `-${this.offset}px`
      }
      return styleArr
    }
  },
  watch: {
    value() {
      this.initLabel()
      this.show = false
    }
  },
  created() {
    this.initLabel()
  },
  methods: {
    initLabel() {
      const { options, value } = this
      const item = options.find(item => item.value === value)
      this.label = item ? item.label : ''
    },
    handleClose() {
      console.log('clickoutside')
      this.show = false
    },
    handleClick(item) {
      this.$emit('input', item.value)
      this.$emit('change', item.value)
    }
  }
}
</script>

<style lang="less" scoped>
.dropdown {
  display: inline-block;
  position: relative;

  .dropdown-rel {
    position: relative;
    display: inline-flex;
    align-items: center;
    color: #ffffff;
    cursor: pointer;
  }

  .select-dropdown {
    width: auto;
    min-width: 86px;
    overflow: visible;
    margin: 2px 0;
    padding: 5px 6px;
    box-sizing: border-box;
    border-radius: 8px;
    // box-shadow: 0 1px 6px rgb(0, 0, 0, 0.2);
    position: absolute;
    z-index: 900;
    background: #C6C4C1;

    ul {
      list-style: none;
      margin: 0;
      padding: 0;

      li {
        padding: 2px 10px;
        color: #1A1A1A;
        border-radius: 4px;
        cursor: pointer;
        // &:hover {
        //   background: #296DCB;
        //   color: #ffffff;
        // }
        &.error {
          color: #C9252D;
        }

        &.selected {
          background: #296DCB;
          color: #ffffff;
        }
      }
    }
  }

  .dropdown-transform-dom {
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    z-index: 1;
  }
}
</style>
