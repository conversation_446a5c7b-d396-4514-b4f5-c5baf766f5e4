<template>
  <div
    v-if="show"
    class="modal-wrapper"
    @click.stop="handleClose"
  >
    <div
      class="container"
      :class="[ {'container__border-radius': borderRadius}, classname ]"
      @click.stop="handleStop"
      @mousedown="mousedown($event)"
      :style="{
        position: 'absolute',
        left: position.x + 'px',
        top: position.y + 'px',
        width
      }"
    >
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Modal',
  model: {
    prop: 'show',
    event: 'change'
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    borderRadius: {
      type: Boolean,
      default: true
    },
    canBlankClose: {
      type: Boolean,
      default: true
    },
    width: {
      type: String,
      default: '300px'
    },
    classname: {
      type: String,
      default: ''
    },
    draggable: {
      type: Boolean,
      default: false
    },
    originPosition: {
      type: Object,
      default: () => ({ x: 0, y: 0 })
    }
  },
  data() {
    return {
      dragging: false,
      position: this.originPosition,
      mouseStart: { x: 0, y: 0 },
      elementStart: { x: 0, y: 0 }
    }
  },
  mounted() {
    document.addEventListener('mousemove', this.mousemove)
    document.addEventListener('mouseup', this.mouseup)
  },
  beforeDestroy() {
    document.removeEventListener('mousemove', this.mousemove)
    document.removeEventListener('mouseup', this.mouseup)
  },
  methods: {
    handleClose() {
      if (this.canBlankClose) {
        this.$emit('change', false)
      }
    },
    handleStop() {
    },
    mousedown(e) {
      if (!this.draggable) {
        return
      }

      this.dragging = true
      this.mouseStart.x = e.clientX
      this.mouseStart.y = e.clientY
      this.elementStart.x = this.position.x
      this.elementStart.y = this.position.y
    },
    mousemove(e) {
      if (!this.dragging || !this.draggable) {
        return
      }

      this.position.x = this.elementStart.x + (e.clientX - this.mouseStart.x)
      this.position.y = this.elementStart.y + (e.clientY - this.mouseStart.y)
    },
    mouseup() {
      if (!this.draggable) {
        return
      }

      this.dragging = false
    }
  }
}
</script>

<style lang="less" scoped>
.modal-wrapper {
  z-index: 1000;

  .container {
    height: fit-content;
    background-color: #535353;

    &__border-radius {
      border-radius: 10px;
    }
  }
}
</style>
