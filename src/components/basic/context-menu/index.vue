<template>
  <div
    v-if="show"
    class="context-menu"
    :style="{
      left: `${position.x}px`,
      top: `${position.y}px`,
    }"
    v-clickoutside="closeContextMenu"
  >
    <slot />
    <!-- 按钮区域 -->
    <div
      v-for="item in menuBtns.filter((item) => !item.hidden)"
      :key="item.eventName"
      class="item hover"
      :class="item.className"
      :style="{cursor: item.disabled ? 'default' : 'pointer'}"
      :disabled="item.disabled"
      @click="handleMenuClick(item)"
    >
      {{ item.title }}
    </div>
  </div>
</template>

<script>
export default {
  props: {
    show: Boolean,
    position: {
      type: Object,
      default: () => ({
        x: 0,
        y: 0
      })
    },
    menuBtns: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {}
  },
  methods: {
    closeContextMenu() {
      this.$emit('close')
    },
    handleMenuClick(menuItem) {
      if (menuItem.disabled) return
      this.$emit('click', menuItem)
      this.$emit('close')
    }
  }
}
</script>
<style lang="less">
.context-menu {
  position: fixed;
  width: 205px;
  padding: 8px;
  font-size: 12px;
  line-height: 22px;
  border-radius: 6px;
  background: #c6c4c1;
  color: #888683;
  z-index: 1000;

  & > div {
    padding: 0 4px;
    border-radius: 4px;

    &.item {
      color: #1a1a1a;

      &.gray {
        color: #666666;
      }

      &.hover {
        &:hover {
          color: #ffffff;
          background: #1373e6;
          cursor: pointer;
        }
      }

      &.warning {
        color: #c9252d;

        &:hover {
          color: #ffffff;
          background: #c9252d;
        }
      }

      &[disabled='disabled'] {
        color: #888683;
        cursor: not-allowed;
      }
    }
  }

  .bar {
    height: 1px;
    margin: 6px 0;
    background: #acaaa7;
  }

  .popo-btn {
    cursor: pointer;
    display: inline-block;
    margin-left: 4px;
    vertical-align: -1px;

    &:hover {
      color: #1a1a1a;
    }
  }

  .popo-icon {
    display: inline-block;
    margin-left: 4px;
    vertical-align: -1px;
  }
}
</style>
