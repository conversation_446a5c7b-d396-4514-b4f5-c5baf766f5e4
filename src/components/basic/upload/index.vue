<template>
  <div>
    <input
      id="file-input"
      type="file"
      ref="fileInput"
      style="display: none"
      :accept="accept"
      :multiple="multiple"
      @change="uploadFile"
    />
    <div @click="addFile">
      <slot>
        <GButton style="color: #fff;">
          上传文件
        </GButton>
      </slot>
    </div>
  </div>
</template>

<script>

export default {
  props: {
    accept: {
      type: String,
      default: ''
    },
    multiple: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    addFile() {
      this.$refs.fileInput.click()
    },
    uploadFile(event) {
      const files = event.target.files
      if (this.multiple) {
        this.$emit('uploadFile', Array.from(files))
      } else {
        const file = files[0]
        this.$emit('uploadFile', file)
      }
      // 清空input值，允许重复选择同一文件
      event.target.value = ''
    }
  }
}
</script>
