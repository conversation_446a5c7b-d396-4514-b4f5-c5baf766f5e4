<template>
  <div class="checkbox" @click="handleChecked">
    <div class="checkbox__input">
      <IconFont v-show="value" icon="tick" :size="14"/>
    </div>
    <div class="checkbox__label">
      <slot />
    </div>
  </div>
</template>
<script>
export default {
  name: 'Checkbox',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleChecked (e) {
      const checkValue = !this.value
      this.$emit('input', checkValue)
    }
  }
}
</script>
<style lang="less" scoped>
.checkbox {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
  .checkbox__input {
    display: inline-flex;
    width: 16px;
    height: 16px;
    padding-left: 1px;
    border-radius: 3px;
    justify-content: center;
    align-content: center;
    margin-right: 8px;
    border: 1px solid #636363;
    background: #474747;
    color: #ffffff;
  }
  .checkbox__label {
    font-size: 12px;
    cursor: pointer;
  }
}
</style>
