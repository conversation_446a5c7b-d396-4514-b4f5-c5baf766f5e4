<template>
  <div class="g-input-style" :class="size">
    <div class="prefix">
      <slot name="prefix"></slot>
    </div>
    <slot name="value">
      <input
        ref="input"
        :maxlength="limit"
        :placeholder="placeholder"
        :value="value"
        @input="handleInput"
        :disabled="disabled"
        @paste="handlePaste"
        @keydown.enter="handleEnter"
        :type="type"
      />
    </slot>
    <div class="suffix" v-if="$slots.suffix">
      <slot name="suffix"></slot>
    </div>
    <div
      class="suffix"
      v-else-if="clearable"
      v-show="value"
      @click="handleClear"
    >
      <IconFont icon="empty" :size="16" class="icon" />
    </div>
    <span class="error-tips">
      <slot></slot>
    </span>
  </div>
</template>
<script>
export default {
  name: 'gInput',
  props: {
    value: {
      type: String,
      default: ''
    },
    placeholder: {
      type: String,
      default: ''
    },
    clearable: {
      type: <PERSON>olean,
      default: true
    },
    limit: {
      type: Number,
      default: 999999
    },
    disabled: {
      type: Boolean,
      default: false
    },
    size: String, // middel large
    type: String
  },
  methods: {
    handleInput() {
      this.$emit('change', this.$refs.input)
      this.$emit('input', this.$refs.input.value)
    },
    handleClear() {
      this.$emit('input', '')
      this.$emit('clear')
    },
    handlePaste(e) {
      this.$emit('paste', e)
    },
    handleEnter(e) {
      this.$emit('enter', e)
    }
  }
}
</script>
<style lang="less" scoped>
.g-input-style {
  position: relative;
  display: inline-flex;
  width: 100%;
  height: 32px;
  padding: 0 12px;
  background: #474747;
  border: 1px solid #636363;
  border-radius: 4px;

  &:hover {
    border-color: lighten(#636363, 10%);
  }

  input {
    flex: 1;
    // width: 100%;
    height: 100%;
    background: transparent;
    color: #FFFFFF;
    border: none;
    outline: none !important;
    font-size: 14px;
  }

  .prefix,
  .suffix {
    display: flex;
    width: auto;
    justify-items: center;
    align-items: center;
  }

  .suffix {
    cursor: pointer;

    .icon {
      color: #E1E1E1;

      &:hover {
        color: #FFFFFF;
      }
    }
  }

  .error-tips {
    position: absolute;
    left: 0;
    bottom: -18px;
    width: 100%;
    color: #d31510;
  }

  &.middel {
    height: 40px;
  }

  &.large {
    height: 48px;
  }
}
</style>
