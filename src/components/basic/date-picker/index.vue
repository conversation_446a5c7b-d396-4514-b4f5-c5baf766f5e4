<template>
  <div class="datePicker">
    <el-date-picker
      v-model="dateValue"
      size="mini"
      :type="type"
      unlink-panels
      popper-class="datePickerPopper"
      :pickerOptions="pickerOptions"
    />
  </div>
</template>

<script>
import './index.less'

export default {
  props: {
    value: [String, Array],
    type: String,
    showPickerOptions: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'value',
    event: 'changeTime'
  },
  computed: {
    dateValue: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('changeTime', value)
      }
    },
    pickerOptions() {
      if (!this.showPickerOptions) return {}
      return {
        shortcuts: [
          {
            text: '最近一周',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      }
    }
  },
  mounted() {
    const elDateEditorEl = document.querySelector('.el-date-editor')
    elDateEditorEl.innerText = '筛选日期'
  }
}
</script>

<style scoped lang="less">
:deep(.el-date-editor) {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  width: auto !important;
  height: 24px !important;
  margin-left: 5px;
  font-size: 12px;
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid #3E3E3F;
  color: #C7C7C7;
  background-color: transparent;

  .el-range-input, i, span {
    display: none;
  }
}

:deep(.el-picker-panel) {
  background-color: red;
}

:deep(.el-picker-panel__body-wrapper) {
  background-color: red !important;

  .el-picker-panel__sidebar,
  .el-picker-panel__body {
    background-color: red !important;
  }
}
</style>
