.datePickerPopper {
  border: none !important;
  transform: scale(0.9) translateX(0px);
  transition: transform 0.3s ease;

  .el-picker-panel__body-wrapper {
    border-radius: 3px;
    opacity: 1;
    box-sizing: border-box;
    border: 1px solid #383838 !important;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.25);
    color: #fff !important;
    background: #535353 !important;

    .el-picker-panel__sidebar {
      background: #535353 !important;
      border-right: 1px solid #383838 !important;

      .el-picker-panel__shortcut {
        color: #fff !important;
      }

    }

    .el-picker-panel__body {
      color: #fff;

      .el-date-range-picker__content.is-left {
        border-right: 1px solid #383838 !important;
      }

      .el-picker-panel__icon-btn {
        color: #fff;
      }

      .el-picker-panel__icon-btn.is-disabled {
        color: #bbb;
      }

      .el-date-table th {
        color: #fff;
      }

      .in-range {
        div {
          background-color: rgba(61, 122, 245, 0.2);
        }
      }
    }
  }
}
