<template>
  <div
    v-clickoutside="handleClose"
    :class="{
      'is-search-with': isSearchWith
    }"
    class="u-multi-select"
  >
    <div v-if="$slots.label" class="label" @click.stop="handleOpen">
      <slot :label="showname || label" name="label"></slot>
    </div>
    <button
      v-else
      :class="{
        'is-focus': !isSearchWith && open,
        label: hasValue,
        'spectrum-SearchWithin-picker': isSearchWith,
      }"
      aria-haspopup="listbox"
      class="g-select spectrum-Picker spectrum-Picker--sizeM is-close selectBtn"
      style="border-radius: 4px"
      @click.stop="handleOpen"
    >
      <span class="spectrum-Picker-label current-label">{{ showname || label }}</span>
      <IconFont :size="22" icon="down-arrow" />
    </button>
    <div
      :class="open ? 'is-open' : 'is-close'"
      :style="{
        'max-height': isSearchWith ? '560px' : '140px',
        'overflow-y': 'auto',
        'z-index': !!$slots.label ? 3 : 1,
        right: !!$slots.label ? 0 : 'initial',
        width: width ? `${width}px` : '100%'
      }"
      class="spectrum-Popover spectrum-Popover--bottom spectrum-Picker-popover"
      style="margin-top: 8px; padding-left: 5px"
    >
      <ul class="spectrum-Menu" role="listbox">
        <template v-if="options.length > 0">
          <li
            v-for="opt in options"
            :key="opt.id"
            :aria-selected="
              multi
                ? value.map(item => item.id).includes(opt.id)
                : opt.id === value
            "
            :class="
              multi
                ? value.map(item => item.id).includes(opt.id)
                : opt.id === value
                  ? 'is-selected'
                  : null
            "
            :tabindex="opt.id"
            class="spectrum-Menu-item optionItem"
            role="option"
            @click="
              multi
                ? handleSelected({ id: opt.id, name: opt.name }, $event)
                : handleSelected(opt.id, $event)
            "
          >
            <span class="spectrum-Menu-itemLabel">{{ opt.name }}</span>
            <IconFont
              v-if="
                multi
                  ? value.map(item => item.id).includes(opt.id)
                  : opt.id === value
              "
              :size="16"
              icon="tick" />
          </li>
        </template>
        <li
          v-else
          aria-disabled="true"
          class="spectrum-Menu-item is-disabled"
          role="option"
        >
          <span class="spectrum-Menu-itemLabel" style="padding-left: 5px">暂无数据</span>
          <!-- <IconFont icon=""/> -->
          <svg
            aria-hidden="true"
            class="spectrum-Icon spectrum-UIIcon-Checkmark100 spectrum-Menu-checkmark spectrum-Menu-itemIcon"
            focusable="false"
          >
            <use xlink:href="#spectrum-css-icon-Checkmark100" />
          </svg>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  name: 'MultiSelect',
  props: {
    isSearchWith: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    hasValue: {
      type: Boolean,
      default: false
    },
    value: {
      type: [String, Array, Number],
      require: true
    },
    options: {
      type: Array,
      default() {
        return []
      }
    },
    multi: {
      type: Boolean,
      default: false
    },
    width: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      open: false,
      showname: ''
    }
  },
  watch: {
    value: {
      deep: true,
      handler(val) {
        if (this.multi) {
          this.showname = this.options
            .filter(item => val.map(item => item.id).includes(item.id))
            .map(item => item.name)
            .join(',')
        } else {
          this.showname = this.options.filter(item => item.id === val)[0]?.name
        }
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      // console.log(111, this.$slots)
      if (this.multi) {
        const value = this.value.map(item => item.id)
        this.showname = this.options
          .filter(item => value.includes(item.id))
          .map(item => item.name)
          .join(',')
      } else {
        this.showname = this.options.filter(
          item => item.id === this.value
        )[0]?.name
      }
    },
    handleOpen() {
      this.open = !this.open
    },
    handleClose() {
      this.open = false
    },
    handleSelected(item) {
      if (this.multi) {
        let seletedIdArr = JSON.parse(JSON.stringify(this.value))
        if (seletedIdArr.map(item => item.id).includes(item.id)) {
          seletedIdArr = seletedIdArr.filter(o => o.id !== item.id)
        } else {
          seletedIdArr.push(item)
        }
        this.$emit('input', seletedIdArr)
        this.$emit('change', seletedIdArr)
      } else {
        if (item === this.value && !this.hasValue) {
          this.$emit('input', '')
          this.$emit('change', '')
        } else {
          this.$emit('input', item)
          this.$emit('change', item)
        }
        setTimeout(() => {
          this.handleClose()
        }, 0)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.u-multi-select {
  display: flex;
  flex-direction: column;
  justify-content: center;
  position: relative;
  width: auto;

  .current-label {
    color: #fff;
  }

  .spectrum-Picker {
    width: 100%;
  }

  .spectrum-Popover {
    width: 100%;
    z-index: 1;
    top: 20px;
  }

  &.is-search-with {
    .spectrum-Popover {
      width: 218px;
    }

    .g-select.label {
      background: #404040;
      border-color: #636363;
      border-right: none;
    }
  }

  .g-select.label {
    background: #3c3c3c;
    border-color: transparent;
    color: #e3e3e3;

    &:hover {
      border-color: #636363;
      color: #ffffff;
    }

    &.is-focus {
      background: #595959;
      border-color: #818181;
      color: #ffffff;
    }
  }
}

.optionItem {
  padding: 0 6px !important;
}

.selectBtn {
  border: 1px solid rgb(144, 144, 144);

  &:hover {
    border-color: #fff;
  }
}
</style>
