/* 通用弹窗组建 */
<template>
  <div class="g-dialog" v-if="value">
    <div class="dialog-wrapper" :style="{ width: `${width}px` }">
      <slot v-if="$slots.default" class="dialog-cont"></slot>

      <div v-else class="dialog-cont dialog-confirm-text">
        <img v-if="!hideTopImg" src="./img/icon.png" width="64" height="64" alt="" />

        <template v-if="type === 'dialog'">
          <div class="dialog-confirm-text-title">{{ title }}</div>
          <div class="dialog-confirm-text-subtitle">{{ subtitle }}</div>
        </template>

        <template v-else>
          <div class="dialog-confirm-text-subtitle">{{ subtitle }}</div>
          <div class="dialog-confirm-text-title" v-html="title"></div>
        </template>
      </div>

      <slot name="body"></slot>

      <template v-if="!hideFooter">
        <slot v-if="$slots.footer" name="footer" class="dialog-footer"></slot>

        <div v-else class="dialog-footer">
          <GButton
            @click="handleOk"
            :disabled="okDisabled"
            :type="type === 'warning' ? 'error' : 'primary'"
            style="width:100%;margin-bottom: 8px;"
          >
            {{ okText }}
          </GButton>

          <GButton @click="handleCancel" style="width:100%;">{{ cancelText }}</GButton>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  name: 'GDialog',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'dialog' // dialog, warning, info
    },
    okText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    title: {
      type: String,
      default: '提示'
    },
    subtitle: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 260
    },
    okDisabled: {
      type: Boolean,
      default: false
    },
    hideTopImg: {
      type: Boolean,
      default: false
    },
    hideFooter: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleOk() {
      if (this.okDisabled) return
      this.$emit('ok')
      this.$emit('input', false)
    },
    handleCancel() {
      this.$emit('cancel')
      this.$emit('input', false)
    }
  }
}
</script>

<style lang="less" scoped>
.g-dialog {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;

  .dialog-wrapper {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    min-width: 260px;
    height: auto;
    padding: 16px;
    background: #535353;
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.32);
    border-radius: 10px;

    .dialog-cont {
      height: auto;
    }

    .dialog-footer {
      height: auto;
      margin-top: 24px;

      .dialog-footer-btn {
        height: 28px;
        line-height: 28px;
        margin-bottom: 8px;
        text-align: center;
        background: #868686;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;

        &:last-child {
          margin-bottom: 0;
        }

        &.error {
          background: #c9252d;
        }

        &.info {
          background: #1373e6;

          &:hover {
            background: #197efa;
          }

          &:active,
          &:focus {
            background: #1065cc;
          }
        }

        &.cancel {
          font-weight: 400;
          color: #201f1e;
        }

        &[disabled='disabled'] {
          cursor: not-allowed;
          background: #696969;

          &:hover {
            background: #696969;
          }
        }
      }
    }

    .dialog-confirm-text {
      text-align: center;
      text-overflow: clip;

      &-title {
        margin: 16px 0;
        font-size: 14px;
        font-weight: 500;
        color: #ffffff;
      }

      &-subtitle {
        font-size: 12px !important;
        font-weight: normal;
        color: #ececec;
        margin-bottom: 16px;
      }
    }
  }
}
</style>
