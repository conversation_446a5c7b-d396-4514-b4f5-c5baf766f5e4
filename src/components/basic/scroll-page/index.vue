<template>
  <div
    ref="m-scroll-page"
    class="m-scroll-page"
    @scroll="handleScrollTriggle"
  >
    <slot name="header"></slot>

    <slot :list="list" name="list" class="u-scroll-body" />

    <template v-if="showFooter">
      <div v-if="!finished && loading" class="u-block">—— 加载中 ——</div>
      <div v-if="!finished && !loading && list.length === 0" class="u-block">
        <br>
        <img src="@/assets/img/network.png" width="56" alt />
        <br>
        网络出错，请检查网络链接
      </div>
      <div v-if="finished && list.length !== 0" class="u-block">—— 没有更多了 ——</div>
      <div v-if="finished && list.length === 0" class="u-block empty">
        <slot name="empty">
          <img src="@/assets/img/empty.png" :width="56" alt />
          <span>没有找到相关内容</span>
        </slot>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: true
    },
    finished: {
      type: Boolean,
      default: false
    },
    list: {
      type: Array,
      default() {
        return []
      }
    },
    showFooter: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    handleScrollTriggle(e) {
      this.$emit('scroll', e)
      const clientHeight = e.target.clientHeight
      const scrollTop = e.target.scrollTop
      const scrollHeight = e.target.scrollHeight
      const distance = scrollHeight - scrollTop - clientHeight
      if (distance < 100 && !this.finished && !this.loading) {
        this.$emit('scroll-bottom')
      }
    },
    setScrollTop(clientTop) {
      this.$refs['m-scroll-page'].scrollTop = clientTop
    },
    getScrollTop() {
      return this.$refs['m-scroll-page'].scrollTop
    }
  }
}
</script>

<style lang="less" scoped>
.m-scroll-page {
  width: 100%;
  height: 100%;
  padding: 12px 12px 50px 12px;
  overflow-y: scroll;

  .u-block {
    text-align: center;
    line-height: 24px;
    color: #9E9E9E;
  }

  .empty {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100%;

    span {
      margin-top: 10px;
    }
  }
}
</style>
