<template>
  <button :class="classStyle" @click="handleClick">
    <div class="btn-process" v-if="process" :style="{width:process+'%'}"></div>
    <span class="com-button-label">
      <slot />
      <span v-if="loading">...</span>
    </span>
  </button>
</template>

<script>
const SizeClassConfig = {
  large: 'spectrum-ActionButton--sizeL',
  small: 'spectrum-ActionButton--sizeS',
  defalut: 'spectrum-ActionButton--sizeM'
}
const TypeClassConfig = {
  primary: 'com-button--primary',
  text: 'com-button--text',
  error: 'com-button--error',
  default: 'com-button--default',
  info: 'com-button--info'
}
const ShapeClassConfig = {
  circle: 'com-button--circle'
}

export default {
  props: {
    type: {
      type: String,
      default: 'default' // primary default text error info
    },
    size: {
      type: String,
      default: 'defalut' // small defalut large
    },
    ghost: <PERSON><PERSON><PERSON>, // 幽灵
    shape: {
      type: String,
      default: '' // circle
    },
    disabled: Boolean,
    loading: Bo<PERSON><PERSON>,
    process: Number
  },
  computed: {
    classStyle() {
      let defaultClass = ['spectrum-ActionButton', 'com-button']
      defaultClass.push(SizeClassConfig[this.size])
      defaultClass.push(TypeClassConfig[this.type])
      defaultClass.push(ShapeClassConfig[this.shape])

      if (this.type === 'info') {
        defaultClass = [
          ...defaultClass,
          'spectrum-Button',
          'spectrum-Button--primary'
        ]
      }

      if (this.ghost) {
        defaultClass.push('ghost')
      }
      if (this.disabled) {
        defaultClass.push('com-button--disabled')
      }

      return defaultClass.join(' ')
    }
  },
  methods: {
    handleClick(e) {
      if (this.disabled || this.loading) return
      this.$emit('click', e)
    }
  }
}
</script>

<style lang="less" scoped>
.com-button {
  border-radius: 4px;
  border-width: 1px;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
  overflow: hidden;

  &.w-full {
    width: 100%;
  }

  &--circle {
    border-radius: 20px !important;
  }

  &--disabled {
    cursor: not-allowed;
    background-color: #696969 !important;
    color: #C2C2C2 !important;
    border-color: #696969 !important;
  }

  .com-button-label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    line-height: 1em;
  }

  .btn-process {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    background: #1373e6;
    z-index: 0;
  }
}

.spectrum-ActionButton--sizeS {
  padding: 0 8px;
}

.spectrum-ActionButton--sizeM {
  padding: 0 12px;
  font-weight: bold;
  //min-width: 70px;
}

// 文字型按钮
.com-button--text {
  background-color: transparent;
  border-color: transparent;
  color: #ffffff;

  &:hover {
    border-color: #818181;
  }
}

@status-colors: {
  primary: #1373e6, #fff; //背景色、字体色
  default: #868686, #201f1e;
  error: #c9252d, #fff;
};

@status-ghost-colors: {
  info: #e3e3e3, #201f1e, #e3e3e3; // 线框主色、hover字体色、hover背景色
};

//实体按钮
each(@status-colors, {
  .com-button--@{key} {
    @colors: @value;
    @bg-color: extract(@colors, 1);
    @text-color: extract(@colors, 2);
    @border-color: @bg-color;

    background-color: @bg-color;
    color: @text-color;
    border-color: @border-color;

    &:hover {
      background-color: lighten(@bg-color, 4%);
      border-color: lighten(@border-color, 4%);
    }

    &:active, &:visited {
      background-color: lighten(@bg-color, 1%);
      border-color: lighten(@border-color, 1%);
    }
  }
})

// 线框按钮
each(@status-ghost-colors, {
  .com-button--@{key}.ghost {
    @colors: @value;
    @border-color: extract(@colors, 1);
    @text-color: @border-color;
    @text-color-hover: extract(@colors, 2);
    @bg-color-hover: extract(@colors, 3);

    background-color: transparent;
    border-color: @border-color;
    color: @text-color;

    &:hover {
      background-color: @bg-color-hover;
      color: @text-color-hover;
    }

    &:active, &:visited {
      background-color: @bg-color-hover;
      color: @text-color-hover;
    }
  }
})


// 默认的线框按钮比较特殊，hove是线框颜色变化
.com-button--default.ghost {
  @border-color: #3e3e3f;
  @text-color: #fff;
  @border-color-hover: #818181;

  background-color: transparent;
  border-color: @border-color;
  color: @text-color;

  &:hover {
    border-color: @border-color-hover;
  }

  &:active,
  &:visited {
    border-color: @border-color-hover;
    background-color: @border-color;
  }
}
</style>
