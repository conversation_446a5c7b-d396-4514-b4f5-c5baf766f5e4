<template>
  <div
    class="select"
    @click.capture="handleShowOptions"
    v-clickoutside="handleClose"
    :style="{backgroundColor: disabled ? '#696969': '#474747'}"
  >
    <div class="mask-bg" @click="handleClose"></div>

    <div v-if="!currentOption.value" class="placeholder value-content">
      {{ placeholder }}
    </div>
    <div v-else class="value-content">{{ currentOption.label }}</div>
    <IconFont icon="down-arrow" class="icon" />
    <div v-show="showOptions" class="options">
      <div v-if="!options.length" class="empty-text">
        <slot name="empty">
          还没有数据哦
        </slot>
      </div>
      <ul v-else>
        <li
          class="option"
          :class="{
            'option--active': currentOption.value === option.value,
            'option--disabled': option.disabled,
          }"
          :key="option.value"
          v-for="option in options"
          @click="handleSelect(option)"
        >
          {{ option.label }}
          <IconFont v-if="currentOption.value === option.value" icon="tick" class="icon" />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    options: {
      type: Array,
      default: () => []
    },
    placeholder: String,
    value: {
      type: String,
      default: ''
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  data() {
    return {
      showOptions: false
    }
  },
  methods: {
    handleShowOptions() {
      if (!this.disabled) {
        this.showOptions = !this.showOptions
      }
    },
    handleSelect(option) {
      if (!option.disabled) {
        this.$emit('change', option.value)
        this.showOptions = false
      } else {
        this.showOptions = true
      }
    },
    handleClose() {
      this.showOptions = false
    }
  },
  computed: {
    currentOption() {
      return this.options?.find((v) => this.value === v.value) ?? {}
    }
  }
}
</script>

<style lang="less" scoped>
.select {
  box-sizing: border-box;
  height: 32px;
  width: 100%;
  color: #fff;
  border: solid 1px #5a5a5a;
  border-radius: 4px;
  background-color: #474747;
  cursor: pointer;

  .icon {
    position: relative;
    margin-right: 10px;
    float: right;
    font-size: 22px;
    transform: translateY(-60%);
    cursor: pointer;
    color: #E1E1E1 !important;

    &:hover {
      color: #fff;
    }
  }

  &:hover {
    border-color: #6e6e6e;
  }

  .value-content {
    margin: 0px 24px 0 12px;
    position: relative;
    font-size: 14px;
    transform: translateY(25%);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .placeholder {
    color: #909090;
  }

  .options {
    position: relative;
    margin-top: 14px;
    box-sizing: border-box;
    width: 100%;
    border-radius: 4px;
    font-size: 14px;
    background-color: #535353;
    border: solid 1px #424242;
    z-index: 1000;
    max-height: 200px;
    overflow: auto;
    box-shadow: 0px 2px 5px 2px #0005;

    .empty-text {
      text-align: center;
      color: #c2c2c2;
      min-height: 56px;
      width: 100%;
      padding: 4px 0;
    }

    ul,
    li {
      margin: 0;
      padding: 0;
      list-style: none;
    }

    .option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 24px;
      font-size: 12px;
      padding: 4px 12px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;

      &:hover,
      &--active {
        background: #474747;
      }

      &--disabled {
        color: #919191 !important;
        cursor: not-allowed;
      }

      .icon {
        position: relative;
        width: 14px;
        height: 14px;
        margin-right: 0px;
        float: right;
        font-size: 18px;
        transform: translateY(0%);
        color: #fff;
      }
    }
  }
}

.mask-bg {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100%;
}
</style>
