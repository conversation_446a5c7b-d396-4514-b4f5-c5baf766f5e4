<template>
  <div v-if="show" class="modal-wrapper" @click.stop="handleClose">
    <div
      class="container"
      :style="{ width }"
      :class="{ 'container__border-radius': borderRadius }"
      @click.stop="handleStop"
    >
      <slot name="content"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Modal',
  model: {
    prop: 'show',
    event: 'change',
  },
  props: {
    show: {
      type: Boolean,
      default: false,
    },
    borderRadius: {
      type: Boolean,
      default: true,
    },
    canBlankClose: {
      type: Boolean,
      default: true,
    },
    width: {
      type: String,
      default: '300px',
    },
  },
  methods: {
    handleClose() {
      if (this.canBlankClose) {
        this.$emit('change', false)
      }
    },
    handleStop() {},
  },
}
</script>

<style lang="less" scoped>
.modal-wrapper {
  display: flex;
  position: fixed;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  background-color: #0008;
  z-index: 1000;
  justify-content: center;
  align-items: center;

  .container {
    height: fit-content;
    background-color: #535353;

    &__border-radius {
      border-radius: 10px;
    }
  }
}
</style>
