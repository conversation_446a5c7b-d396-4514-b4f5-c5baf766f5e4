<template>
  <div
    class="avatar"
    :style="{ width: `${width}px`, height: `${height}px` }"
    @mouseover="onMouseover"
    @mouseleave="onMouseleave"
    @click="avatarClick"
  >
    <div class="avatar-container">
      <img
        v-if="type === 'img'"
        :src="src ? src : require('@/assets/img/avatar.png')"
        :style="{ width: width + 'px', height: height + 'px' }"
        alt
      />
      <div class="text" v-if="type === 'text'">{{ text }}</div>
    </div>

    <div
      v-if="hoverText && hoverText.length > 0"
      class="hover-text"
      :style="{ visibility: `${HoverInfo.show ? 'visible' : 'hidden'}` }"
    >
      <div style="margin: 0 10px;">{{ hoverText }}</div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    type: {
      type: String,
      default: 'img'
    },
    text: {
      type: String,
      default: ''
    },
    src: {
      type: String,
      default: require('@/assets/img/svg/default.svg')
    },
    height: {
      type: Number,
      default: 25
    },
    width: {
      type: Number,
      default: 25
    },
    hoverText: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      HoverInfo: {
        show: false,
        x: 0,
        y: 0
      }
    }
  },
  methods: {
    onMouseover(e) {
      this.HoverInfo.x = e.clientX
      this.HoverInfo.y = e.clientY + 10
      this.HoverInfo.show = true
    },
    onMouseleave() {
      this.HoverInfo.show = false
    },
    avatarClick() {
      this.$emit('avatarClick')
    }
  }
}
</script>

<style lang="less" scoped>
.avatar {
  position: relative;
  cursor: pointer;

  .text {
    font-size: 14px;
    font-weight: 500;
  }

  .avatar-container {
    display: flex;
    border-radius: 50%;
    background-color: #777777;
    overflow: hidden;
    justify-content: center;
    align-items: center;
  }

  .hover-text {
    position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    width: fit-content;
    width: -webkit-fit-content;
    width: -moz-fit-content;
    min-width: 68px;
    height: 34px;
    margin-top: 4px;
    font-size: 12px;
    text-align: center;
    z-index: 10;
    border-radius: 4px;
    color: #545454;
    //background-color: #c4c4c4;
    background-color: #eeeeee;
  }
}
</style>
