<template>
  <div style="display: flex;align-items: center;">
    <div class="container" style="width: 100%; height: 100%;">
      <gInput
        v-model="searchKeyword"
        :placeholder="placeholder"
        class="searchInput"
        autocomplete="off"
        @enter="onEnter"
        tabindex="-1"
        :clearable="clearable"
        @clear="onClear"
      >
        <template #prefix>
          <IconFont icon="search" :size="14" class="prefixIcon" />
        </template>
      </gInput>
    </div>

    <div v-if="hasCancelBtn" class="search-cancel" @click="onCancel">取消</div>
  </div>
</template>

<script>
export default {
  name: 'Search',
  props: {
    placeholder: {
      type: String,
      default: ''
    },
    keyword: {
      type: String,
      default: ''
    },
    hasCancelBtn: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchKeyword: this.keyword
    }
  },
  watch: {
    keydown(val) {
      this.searchKeyword = val
    },
    keyword(val) {
      this.searchKeyword = val
    }
  },
  methods: {
    onCancel() {
      this.onClear()
      this.$emit('cancel')
    },
    onEnter() {
      this.$emit('enter', this.searchKeyword.trim())
    },
    onClear() {
      this.searchKeyword = ''
      this.$emit('clear')
    }
  }
}
</script>

<style lang="less" scoped>
.container {
  display: flex;
  align-items: center;

  .searchInput {
    height: 34px;
    font-size: 14px;
    z-index: 9;
    border: 1px solid #636363;
    border-radius: 4px;
    background-color: #474747;

    &::placeholder,
    .spectrum-Textfield-input::placeholder {
      font-size: 14px;
      color: #777777;
    }

    .prefixIcon {
      margin-right: 8px;
      color: white;
    }
  }
}

.clear-button {
  z-index: 100;
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
}

.search-cancel {
  width: 60px;
  line-height: 36px;
  text-align: center;
  cursor: pointer;
}
</style>
