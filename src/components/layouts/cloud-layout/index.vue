<template>
  <layout>
    <div class="p-group">
      <!-- 搜索框 -->
      <div class="g-search">
        <Search
          @enter="onCloudGSearch"
          @cancel="onCloudGCancel"
          :hasCancelBtn="hasCancelBtn"
          :keyword="keyword"
        />
      </div>

      <!-- 头部bar -->
      <div class="m-info">
        <div class="m-info-l">
          <slot name="barLeft"></slot>
        </div>
        <div class="m-info-r">
          <slot name="barRight"></slot>
        </div>
      </div>
      <slot />
    </div>
  </layout>
</template>
<script>
import Search from '../search.vue'

export default {
  props: {
    hasCancelBtn: Boolean,
    keyword: {
      type: String,
      default: '',
    },
    showSearchWarn:Boolean
  },
  components: {
    Search,
  },
  data() {
    return {}
  },
  methods: {
    onCloudGSearch(keyword) {
      if (!keyword) {
       this.showSearchWarn &&  this.$CustomToast({
          type: 'error',
          duration: 2,
          content: '请先输入关键字再搜索',
        })
        return
      }
      this.$emit('search', keyword)
    },
    onCloudGCancel() {
      this.$emit('cancel')
    },
  },
}
</script>
<style lang="less" scoped>
.p-group {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}
.g-search {
  margin: 8px 16px 8px 0;
}
.m-info {
  height: auto;
  margin-bottom: 8px;
  display: flex;
  padding-right: 16px;
  .m-info-l,
  .m-info-r {
    display: flex;
    align-items: center;
  }
  .m-info-l {
    flex: 1;
    font-size: 14px;
  }
  .m-info-r {
    // margin-right: 8px;
    width: auto;
    display: flex;
    .upload-btn {
      border-radius: 4px;
      border-color: #3e3e3e;

      &__primary {
        background-color: #3688ea;
        border-color: #145cb2;
      }
    }
  }
}
.m-page {
  flex: 1;
  background: #4d4d4d;
  border-top: 1px solid #383838;
  border-left: 1px solid #383838;
  border-top-left-radius: 4px;
}

.line {
  background-color: #ffffff46;
  border-radius: 10px;
  margin: 0 10px;
  width: 1px;
  height: 60%;
}

.container-header {
  display: flex;
  // margin-left: 6px;
  margin-bottom: 12px;
  font-size: 14px;
  align-items: center;
  .category-dropdown,
  .select-dropdown {
    width: 200px;
  }
}
</style>
