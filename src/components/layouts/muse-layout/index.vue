<template>
  <layout>
    <div class="p-group">
      <div v-if="$slots.sider" class="sider">
        <slot name="sider" />
      </div>

      <div class="right">
        <!-- 搜索框 -->
        <div class="g-search">
          <slot name="searchBar"></slot>
        </div>

        <div v-if="$slots.header" class="header">
          <slot name="header"></slot>
        </div>

        <div class="container">
          <!-- 头部bar -->
          <div class="m-info">
            <div class="m-info-l">
              <slot name="barLeft"></slot>
            </div>

            <div class="m-info-r">
              <slot name="barRight"></slot>
            </div>
          </div>

          <slot />
        </div>
      </div>
    </div>
  </layout>
</template>

<script>

export default {
  data() {
    return {}
  }
}
</script>

<style lang="less" scoped>
.p-group {
  display: flex;
  width: 100%;
  height: 100%;
}

.sider {
  padding: 8px 2px;
  border-left: 1px solid #383838;
}

.right {
  width: 100%;
  padding: 8px;
  border: 1px solid #383838;
  border-left: none;

  .g-search {
    display: flex;
    align-items: center;
  }

  .header {
    padding: 0 8px 8px 0;
  }

  .container {
    height: 100%;
    padding: 9px 12px;
    margin-top: 8px;
    border-radius: 4px 0 0 0;
    overflow: hidden;
    background-color: #4D4D4D;
    box-shadow: inset 0.5px 0 0 0 #383838, inset 0 0.5px 0 0 #383838;

    .m-info {
      height: auto;
      margin-bottom: 8px;
      display: flex;
      padding-right: 16px;

      .m-info-l,
      .m-info-r {
        display: flex;
        align-items: center;
      }

      .m-info-l {
        flex: 1;
        font-size: 14px;
      }

      .m-info-r {
        width: auto;
        display: flex;

        .upload-btn {
          border-radius: 4px;
          border-color: #3e3e3e;

          &__primary {
            background-color: #3688ea;
            border-color: #145cb2;
          }
        }
      }
    }
  }

  .m-page {
    flex: 1;
    background: #4d4d4d;
    border-top: 1px solid #383838;
    border-left: 1px solid #383838;
    border-top-left-radius: 4px;
  }

  .line {
    background-color: #ffffff46;
    border-radius: 10px;
    margin: 0 10px;
    width: 1px;
    height: 60%;
  }

  .container-header {
    display: flex;
    // margin-left: 6px;
    margin-bottom: 12px;
    font-size: 14px;
    align-items: center;

    .category-dropdown,
    .select-dropdown {
      width: 200px;
    }
  }
}
</style>
