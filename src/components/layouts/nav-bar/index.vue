<!--
 * @Author: 李菁颖 <EMAIL>
 * @Date: 2023-01-03 10:30:29
 * @LastEditors: 李菁颖 <EMAIL>
 * @LastEditTime: 2023-02-17 11:43:56
 * @FilePath: /com.netease.library.local/src/components/nav-bar/index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="m-nav-bar">
    <div class="u-bar-icon" @click="handleReturn">
      <IconFont icon="return" :size="22"/>
    </div>
    <span style="cursor: pointer;" @click="handleReturn">
      {{ title }}
    </span>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: '返回'
    }
  },
  methods: {
    handleReturn () {
      this.$router.replace({
        name: 'list'
      })
    }
  }
}
</script>
<style lang="less" scoped>
.m-nav-bar {
  width: 100%;
  height: 36px;
  padding: 10px 12px 10px 0;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  .u-bar-icon {
    display: inline-block;
    cursor: pointer;
  }
}
</style>
