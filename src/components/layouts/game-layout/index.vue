<template>
  <layout>
    <div class="p-group">
      <div>
        <!-- 搜索框 -->
        <div class="g-search">
          <div class="search-type">
            <div
              class="game"
              :class="{active: $bus.gameSearchType === 'game'}"
              @click="handleSearchType('game')"
            >
              游戏
            </div>
            <div
              class="material"
              :class="{active: $bus.gameSearchType === 'material'}"
              @click="handleSearchType('material')"
            >
              截图
            </div>
          </div>

          <Search
            class="search"
            :keyword="gameSearchKey"
            placeholder="搜索全网爆款游戏截图（支持搜索游戏名字、标签等）"
            @enter="onGameSearch"
            @clear="onGameClear"
          />
        </div>

        <FilterBar
          :dropList="dropList"
          :searchParams="searchParams"
          @change="handleTagsClick"
          @toggle="handleFilter"
        />

        <div v-if="$slots.header" class="header">
          <slot name="header"></slot>
        </div>
      </div>

      <div class="container">
        <!-- 头部bar -->
        <div class="m-info">
          <div class="m-info-l">
            <slot name="barLeft"></slot>
          </div>

          <div class="m-info-r">
            <slot name="barRight"></slot>
          </div>
        </div>

        <slot />
      </div>
    </div>
  </layout>
</template>

<script>
import Search from '../search.vue'
import FilterBar from '@/pages/game/components/filter-bar.vue'

export default {
  props: {
    showSearchWarn: Boolean
  },
  components: {
    FilterBar,
    Search
  },
  computed: {
    gameSearchKey() {
      return this.$bus.gameSearchKey
    }
  },
  data() {
    return {
      searchKey: '',
      dropList: [],
      searchParams: {}
    }
  },
  methods: {
    onGameSearch(keyword) {
      this.$bus.setGameSearchKey(keyword)
      this.$emit('search', keyword)
    },
    onGameClear() {
      this.$bus.setGameSearchKey('')
      this.$emit('search', '')
    },
    handleTagsClick(key, val) {
      this.searchParams[key] = val
    },
    handleFilter() {
      const filterTagIds = Object.values(this.searchParams)
      this.$emit('filter', filterTagIds)
    },
    handleSearchType(type) {
      this.$bus.setFilterTagIds([])
      this.$bus.setGameSearchType(type)
      if (type === 'material') {
        this.$router.push({
          name: 'gameSearch'
        })
      } else {
        this.$router.push({
          name: 'games',
          query: { clear: true }
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.p-group {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
}

.g-search {
  display: flex;
  align-items: center;
  margin: 8px 16px 8px 0;

  .search-type {
    display: flex;
    align-items: center;
    width: 136px;
    height: 37px;
    padding: 4px;
    border-radius: 20px;
    background-color: #474747;
    border: 1px solid #636363;

    .game,
    .material {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 64px;
      height: 29px;
      cursor: pointer;
      border-radius: 20px;
      color: #777;

      &:hover {
        color: #fff;
        box-shadow: 4px 4px 16px 0 rgba(0, 0, 0, 0.08);
      }
    }

    .active {
      color: #fff;
      border: 1px solid #535353;
      background-color: #535353;
      box-shadow: 4px 4px 16px 0 rgba(0, 0, 0, 0.08);
    }
  }

  .search {
    flex: 1;
    height: 100%;
    margin-left: 10px;
  }
}

.header {
  padding: 0 8px 8px 0;
}

.container {
  height: 100%;
  padding-top: 9px;
  border-radius: 4px 0 0 0;
  opacity: 1;
  overflow: hidden;
  background: #4D4D4D;
  box-shadow: inset 0.5px 0 0 0 #383838, inset 0 0.5px 0 0 #383838;

  .m-info {
    height: auto;
    margin-bottom: 8px;
    display: flex;
    padding-right: 16px;

    .m-info-l,
    .m-info-r {
      display: flex;
      align-items: center;
    }

    .m-info-l {
      flex: 1;
      font-size: 14px;
    }

    .m-info-r {
      width: auto;
      display: flex;

      .upload-btn {
        border-radius: 4px;
        border-color: #3e3e3e;

        &__primary {
          background-color: #3688ea;
          border-color: #145cb2;
        }
      }
    }
  }
}

.m-page {
  flex: 1;
  background: #4d4d4d;
  border-top: 1px solid #383838;
  border-left: 1px solid #383838;
  border-top-left-radius: 4px;
}

.line {
  background-color: #ffffff46;
  border-radius: 10px;
  margin: 0 10px;
  width: 1px;
  height: 60%;
}

.container-header {
  display: flex;
  // margin-left: 6px;
  margin-bottom: 12px;
  font-size: 14px;
  align-items: center;

  .category-dropdown,
  .select-dropdown {
    width: 200px;
  }
}
</style>
