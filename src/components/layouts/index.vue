<template>
  <div class="m-layout">
    <div class="slider">
      <div
        class="item"
        :class="{ active: webType === 'm' }"
        @click="handleRouter('list')"
      >
        素材库
      </div>

      <!--  v-if="APP_ENV !== 'prod' && !isOutEpl" -->
      <div
        class="item"
        v-if="APP_ENV !== 'prod'"
        :class="{ active: webType === 's' }"
        @click="handleRouter('groups')"
      >
        天枢网盘
      </div>

      <div
        class="item"
        v-if="APP_ENV !== 'prod'"
        :class="{ active: webType === 'g' }"
        @click="handleRouter('games')"
      >
        <span>外部素材库</span>
        <img
          v-if="showGameNews"
          class="news"
          :width="20"
          :height="10"
          src="@/assets/img/news.png" alt=""
        />
      </div>

      <div
        class="item"
        v-if="APP_ENV !== 'prod'"
        :class="{ active: webType === 'muse' }"
        @click="handleRouter('muse')"
      >
        <span>MUSE仓库</span>
        <img
          v-if="showMuseNews"
          class="news"
          :width="20"
          :height="10"
          src="@/assets/img/news.png" alt=""
        />
      </div>

      <div class="tools">
        <div class="icon" @click="handleRefresh">
          <Tooltip content="刷新页面" placement="right">
            <IconFont icon="refresh" :size="16" />
          </Tooltip>
        </div>

        <div class="icon" @click="handleHelp">
          <Tooltip content="帮助" placement="right">
            <IconFont icon="help" :size="16" />
          </Tooltip>
        </div>
      </div>
    </div>

    <div class="cont">
      <div class="wrapper">
        <slot />
      </div>
    </div>
  </div>
</template>

<script>
import { APP_ENV } from '@/config'
import './index.less'

export default {
  data() {
    return {
      APP_ENV,
      webType: this.$route.meta.type || 's'
    }
  },
  computed: {
    isOutEpl() {
      return this.$bus.profile.type === 'EXTERNAL'
    },
    showGameNews() {
      return this.$bus.showGameNews
    },
    showMuseNews() {
      return this.$bus.showMuseNews
    }
  },
  mounted() {
    const isPageRefreshed = window.sessionStorage.getItem('pageRefreshed') === 'true'
    if (isPageRefreshed) {
      this.$CustomToast({
        type: 'success',
        duration: 2,
        content: '已刷新'
      })
      window.sessionStorage.removeItem('pageRefreshed')
    }
  },
  methods: {
    handleRefresh() {
      window.sessionStorage.setItem('pageRefreshed', 'true')
      this.backup()
      window.location.reload()
    },
    handleHelp() {
      this.$csInterface.openURLInDefaultBrowser(
        'https://docs.popo.netease.com/lingxi/675cd93fb03d4873a9ec9aba33e3e9fe'
      )
    },
    handleRouter(name) {
      if (name === 'groups') {
        // 埋点
        this.$et('DRIVE_ENTER')
      }
      this.$router.replace({
        name
      })
    },
    backup() {
      window.sessionStorage.setItem('breadcrumbList', JSON.stringify(this.$bus.breadcrumbList))
    }
  }
}
</script>
