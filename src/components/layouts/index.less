.m-layout {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;

  .slider {
    position: relative;
    padding-top: 4px;
    width: 45px;
    height: 100%;

    .item {
      position: relative;
      writing-mode: vertical-lr;
      width: 32px;
      min-height: 64px;
      background: #444444;
      border-radius: 0 4px 4px 0;
      text-align: center;
      margin: 6px 0;
      padding: 4px 8px;
      cursor: pointer;
      letter-spacing: 2px;

      &.active {
        font-weight: bold;
        background: #383838;
        cursor: default;
      }

      img {
        position: absolute;
        right: -8px;
        top: -2px;
      }
    }

    .tools {
      position: absolute;
      width: 32px;
      bottom: 0;
      z-index: 3;

      .icon {
        display: flex;
        margin: 18px 0;
        justify-content: center;
        cursor: pointer;

        i {
          padding: 0px 4px;
        }
      }
    }
  }

  .cont {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 0;
    height: 100%;
  }

  .wrapper {
    flex: 1;
    height: 0;
    background-color: var(--spectrum-popover-background-color, var(--spectrum-global-color-gray-300));
  }
}
