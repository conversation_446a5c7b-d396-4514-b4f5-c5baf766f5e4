<template>
  <div class="m-footer">
    1111
    <!-- <div v-clickoutside="handleClose" class="u-setting" >
      <div
        :class="{'is-open': open}"
        class="option-list spectrum-Popover"
      >
        <ul class="spectrum-Menu" role="listbox">
          <li
            class="spectrum-Menu-item"
            role="option"
            @click="handleClearData"
          >
            <span class="spectrum-Menu-itemLabel">清除缓存</span>
          </li>
          <li
            class="spectrum-Menu-item"
            role="option"
            @click="handleLogout"
          >
            <span class="spectrum-Menu-itemLabel">退出登录</span>
          </li>
        </ul>
      </div>
      <div class="u-btn" @click="open = !open">
        <svg style="fill:#ffffff;" xmlns="http://www.w3.org/2000/svg" height="12" viewBox="0 0 18 18" width="12">
          <rect id="Canvas" fill="#ffffff" opacity="0" width="18" height="18" /><path class="a" d="M8.5965,12.893H7.534a3.0709,3.0709,0,0,0-.45-1.0895l.7565-.7565a.3035.3035,0,0,0,0-.429l-.46-.46a.3035.3035,0,0,0-.429,0l-.7555.757a3.07263,3.07263,0,0,0-1.089-.45V9.4035A.3035.3035,0,0,0,4.8035,9.1h-.607a.3035.3035,0,0,0-.3035.3035V10.466a3.07263,3.07263,0,0,0-1.089.45l-.7565-.758a.3035.3035,0,0,0-.429,0l-.46.46a.3035.3035,0,0,0,0,.429l.7565.757a3.0709,3.0709,0,0,0-.45,1.0895H.4035A.3035.3035,0,0,0,.1,13.197h0v.607a.3035.3035,0,0,0,.3035.3035H1.466a3.0709,3.0709,0,0,0,.45,1.0895l-.758.756a.3035.3035,0,0,0,0,.429l.46.46a.3035.3035,0,0,0,.429,0l.757-.757a3.07263,3.07263,0,0,0,1.089.45v1.0625a.3035.3035,0,0,0,.3035.3035h.607a.3035.3035,0,0,0,.3035-.3035V16.534a3.07263,3.07263,0,0,0,1.089-.45l.7565.7565a.3035.3035,0,0,0,.429,0l.46-.46a.3035.3035,0,0,0,0-.429L7.085,15.196a3.0709,3.0709,0,0,0,.45-1.0895H8.5975a.3035.3035,0,0,0,.3035-.3035v-.6065a.3035.3035,0,0,0-.3035-.3035ZM4.5,15.082A1.582,1.582,0,1,1,6.082,13.5h0A1.582,1.582,0,0,1,4.5,15.082Z" />
          <path class="a" d="M17.681,7.453l-1.4-.5715a4.37836,4.37836,0,0,0-.006-1.6785l1.405-.591a.4325.4325,0,0,0,.231-.566l-.361-.8545a.432.432,0,0,0-.5655-.23121l-.0005.00021L15.5785,3.55A4.38056,4.38056,0,0,0,14.383,2.372l.5715-1.4a.4325.4325,0,0,0-.237-.5635l-.8-.3265a.4325.4325,0,0,0-.5635.237l-.5715,1.4a4.38055,4.38055,0,0,0-1.6785.006L10.512.322A.432.432,0,0,0,9.9465.09079L9.946.091,9.0915.45a.4325.4325,0,0,0-.231.566L9.45,2.4215a4.3765,4.3765,0,0,0-1.178,1.196l-1.4-.5715a.4325.4325,0,0,0-.5635.237l-.3265.8a.4325.4325,0,0,0,.237.5635l1.4.5715a4.37836,4.37836,0,0,0,.006,1.6785l-1.405.591a.4325.4325,0,0,0-.231.566l.3595.854a.432.432,0,0,0,.5655.23121l.0005-.00021,1.405-.591a4.38043,4.38043,0,0,0,1.196,1.178l-.5715,1.4a.4325.4325,0,0,0,.237.5635l.8.3265a.4325.4325,0,0,0,.5635-.237l.5715-1.4a4.37757,4.37757,0,0,0,1.6785-.006l.591,1.405a.432.432,0,0,0,.5655.23121l.0005-.00021.8545-.3595a.4325.4325,0,0,0,.231-.566L14.45,9.6785A4.37607,4.37607,0,0,0,15.628,8.483l1.4.5715a.432.432,0,0,0,.5633-.23652l.0002-.00048.3265-.8a.4325.4325,0,0,0-.23624-.56419Zm-5.731.691A2.094,2.094,0,1,1,14.044,6.05,2.094,2.094,0,0,1,11.95,8.144Z" />
        </svg>
        设置
      </div>
    </div> -->
    <div class="u-setting">
      <div class="u-btn" @click="handleHelp">
        <svg style="fill:#ffffff;" xmlns="http://www.w3.org/2000/svg" height="16" viewBox="0 0 18 18" width="16">
          <rect id="Canvas" fill="#ff13dc" opacity="0" width="16" height="16" />
          <path class="fill" d="M10.09064,12.966a.9167.9167,0,0,1-.97722,1.0076.93092.93092,0,0,1-.97769-1.0076.97756.97756,0,0,1,1.95491-.02931Q10.09085,12.95135,10.09064,12.966ZM8.97658,4a4.61617,4.61617,0,0,0-2.2591.5362c-.05924.03139-.05924.09215-.05924.15342V6.17521a.07459.07459,0,0,0,.11854.06076,3.69224,3.69224,0,0,1,1.87246-.50481c.90632,0,1.26328.38278,1.26328.93417,0,.47493-.28253.79645-.77259,1.30176C8.42665,8.70278,7.99526,9.1615,7.99526,9.8815a1.70875,1.70875,0,0,0,.357,1.05721A.244.244,0,0,0,8.54519,11h1.2929a.06531.06531,0,0,0,.05931-.10734,1.65129,1.65129,0,0,1-.23779-.843c0-.45874.54994-.96405,1.12955-1.53113a2.73714,2.73714,0,0,0,.95107-2.1129C11.74024,5.05774,10.75955,4,8.97658,4ZM17.5,9A8.5,8.5,0,1,1,9,.50005H9A8.5,8.5,0,0,1,17.5,9ZM15.6748,9A6.67481,6.67481,0,1,0,9,15.67476H9A6.67476,6.67476,0,0,0,15.6748,9Z" />
        </svg>
        <span style="vertical-align: 3px;"> 帮助</span>
      </div>
    </div>
    <div v-if="projectList.length > 0 && routerName !== 'upload'" class="u-upload">
      <material-upload/>
    </div>
  </div>
</template>
<script>
// import { SOURCE_PATHNAME } from '@/config/index.js'
// import { logout } from '@/api/index.js'
// import { deleteFolder } from '@/utils/fs/index.js'
import MaterialUpload from './material-upload/index.vue'
// import { myStorage } from '@/utils/index.js'
export default {
  name: 'Footer',
  components: {
    MaterialUpload
  },
  computed: {
    projectList () {
      return this.$bus.projectList
    },
    routerName () {
      return this.$route.name || ''
    }
  },
  data () {
    return {
      open: false,
      uploadTip: false
    }
  },
  methods: {
    // handleLogout () {
    //   logout().then(d => {
    //     myStorage.remove('token')
    //     this.$router.push({
    //       name: 'index'
    //     })
    //   })
    // },
    // handleOpen () {
    //   this.open = true
    // },
    // handleClose () {
    //   this.open = false
    // },
    // handleClearData () {
    //   this.open = false
    //   // eslint-disable-next-line no-undef
    //   const srcPath = `${this.$csInterface.getSystemPath(SystemPath.USER_DATA)}/${SOURCE_PATHNAME}`
    //   deleteFolder(srcPath).then(d => {
    //     this.$CustomToast({
    //       type: 'success',
    //       duration: 2,
    //       content: d.message
    //     })
    //   }).catch(e => {
    //     this.$CustomToast({
    //       type: 'error',
    //       duration: 2,
    //       content: '操作失败'
    //     })
    //   })
    // },
    handleHelp () {
      this.$csInterface.openURLInDefaultBrowser('https://docs.popo.netease.com/lingxi/675cd93fb03d4873a9ec9aba33e3e9fe')
    }
    // handleDeepClear () {
    //   this.$csInterface.evalScript('PsDeepCleaner()')
    // }
  }
}
</script>
<style lang="less" scoped>
.m-footer{
  position: relative;
  height: 30px;
  padding: 6px 8px;
  border: 1px solid #383838;
  border-width: 1px 0;
  background: #535353;
  .u-setting {
    display: table-row;
    position: relative;
    .option-list {
      position: absolute;
      bottom: 30px;
      z-index: 5;
    }
    .u-btn {
      display: inline-block;
      cursor: pointer;
      height: 16px;
      .icon-user {
        vertical-align: -1px;
      }
    }
  }
  .u-upload {
    position: absolute;
    right: 20px;
    top: 6px;
    cursor: pointer;
  }
}
</style>
