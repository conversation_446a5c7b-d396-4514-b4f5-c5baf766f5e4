<template>
  <div>
    <span
      :class="uploadTip ? 'is-open' : 'is-close'"
      class="spectrum-Tooltip spectrum-Tooltip--left"
      style="margin-top: -4px;"
    >
      <span class="spectrum-Tooltip-label">上传素材</span>
      <span class="spectrum-Tooltip-tip"></span>
    </span>
    <span
      @mouseenter="uploadTip = true"
      @mouseleave="uploadTip = false"
      @click="handleUpload"
    >
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path fill-rule="evenodd" clip-rule="evenodd" d="M8 3C6.25664 3 4.73863 4.4163 4.54622 6.32521L4.4782 7H3.8C2.7159 7 1.75 7.9583 1.75 9.25C1.75 10.5417 2.7159 11.5 3.8 11.5H5V13H3.8C1.79152 13 0.25 11.2708 0.25 9.25C0.25 7.45304 1.46894 5.88666 3.15123 5.5617C3.65441 3.25694 5.6192 1.5 8 1.5C10.0267 1.5 11.7352 2.78184 12.5061 4.57358C14.3879 4.95907 15.75 6.72037 15.75 8.75C15.75 9.82271 15.3291 10.8599 14.71 11.6315C14.1011 12.3903 13.2068 13 12.2 13H11V11.5H12.2C12.5932 11.5 13.0989 11.2425 13.54 10.6927C13.9709 10.1558 14.25 9.44291 14.25 8.75C14.25 7.24828 13.1709 6.09496 11.9075 6.00188L11.4069 5.965L11.2501 5.48815C10.7639 4.00938 9.46962 3 8 3Z" fill="white"/>
        <path fill-rule="evenodd" clip-rule="evenodd" d="M7.25008 9.55972L6.03076 10.779L4.9701 9.71838L8.00005 6.68843L11.03 9.71838L9.96934 10.779L8.75008 9.55977L8.75015 14.2492L7.25015 14.2492L7.25008 9.55972Z" fill="white"/>
      </svg>
      <span style="vertical-align: 3px;"> 上传</span>
    </span>
  </div>
</template>
<script>
export default {
  name: 'MaterialUpload',
  data () {
    return {
      uploadTip: false
    }
  },
  methods: {
    handleUpload () {
      this.$router.push({
        name: 'upload'
      })
    }
  }
}
</script>
