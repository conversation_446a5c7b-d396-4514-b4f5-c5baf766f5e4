<template>
  <div class="m-search-box">
    <template v-if="isIndex">
      <!-- <div class="u-select-item" style="padding: 4px 0;">
        <div class="spectrum-ActionGroup spectrum-ActionGroup--quiet">
          <color-picker :value="searchParams.colorRange" @change="handleColorChange"/>
          <multi-select
            v-for="item in dropList"
            :key="`${item.key}_${searchParams.projectId}`"
            :value="searchParams[item.key] || []"
            :options="item.items"
            :label="item.name"
            all-checked
            @change="(val) => handleMultiChange(item.key, val)"
          />
        </div>
      </div> -->
      <div class="u-select-item index-search-box spectrum-SearchWithin" style="margin-top: 8px; margin-bottom: 4px;">
        <dept-select
          :isSearchWith="true"
          :value="searchParams.projectId"
          class="dept-select"
          @change="handleDeptChange"
        />
        <auto-input
          :isSearchWith="true"
          :left-div="true"
          :value="searchParams.tags"
          class="keyword-input"
          @change="handleInputChange"
        />
      </div>
      <div class="tags-select">
        <div class="selects">
          <tags-select
            v-bind="{
            colorRange: searchParams.colorRange,
            tags: searchParams.tags,
            dropList: dropList
          }"
            @change="handleTagsClick"
            @toggle="handleSearch"
          />
          <!-- 筛选日期 -->
          <div class="pickDate">
            <img
              v-if="time.every(i => i)"
              :width="16"
              alt=""
              src="@/assets/img/closeWithBorder.png"
              @click.stop="clearSearchTime"
            >
            <date-picker
              v-model="time"
              showPickerOptions
              type="daterange"
            />
          </div>

          <!-- 时间顺序 -->
          <multi-select
            :hasValue="true"
            :options="reqTimeOptions"
            :value="reqTime"
            :width="150"
            style="position: relative; z-index: 200;"
            @change="handleReqTimeChange"
          >
            <div slot="label" class="reqTimeSelect">
              <span>
                {{ reqTimeLabelName }}
                <IconFont :size="16" icon="sort" />
              </span>
            </div>
          </multi-select>
        </div>
        <upload-btn class="upload-btn" />
      </div>
    </template>
    <template v-else>
      <div class="u-select-item">
        <auto-input :value="searchParams.tags" @change="handleInputChange" />
      </div>
      <div v-if="project" class="u-select-item">
        <dept-select :value="searchParams.projectId" style="margin-top: 8px;" @change="handleDeptChange" />
      </div>
      <div class="u-select-item" style="padding: 4px 0;">
        <div class="spectrum-ActionGroup spectrum-ActionGroup--quiet">
          <color-picker :value="searchParams.colorRange" @change="handleColorChange" />
          <DropMultiSelect
            v-for="item in dropList"
            :key="`${item.key}_${searchParams.projectId}`"
            :label="item.name"
            :options="item.items"
            :value="searchParams[item.key] || []"
            all-checked
            @change="(val) => handleMultiChange(item.key, val)"
          />
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import AutoInput from './auto-input.vue'
import ColorPicker from './color-picker.vue'
import DropMultiSelect from './multi-select.vue'
import DeptSelect from './dept-select.vue'
import TagsSelect from './tags-select.vue'
import UploadBtn from './uploader/index.vue'
import { getDropList } from '@/api/index.js'
import { judgeResultFun } from '@/utils/index.js'

let sourceParams = null
export default {
  name: 'SearchBox',
  props: {
    project: {
      type: Boolean,
      default: true
    },
    isIndex: {
      type: Boolean,
      default: false
    },
    reqTime: {
      type: String,
      default: 'DEFAULT'
    },
    reqTimeOptions: {
      type: Array,
      default: () => ([])
    },
    reqTimeLabelName: {
      type: String,
      default: '综合排序'
    },
    searchTime: {
      type: Array,
      default: () => ['', '']
    }
  },
  model: {
    prop: 'searchTime',
    event: 'changeSearchTime'
  },
  components: {
    AutoInput,
    ColorPicker,
    DropMultiSelect,
    DeptSelect,
    TagsSelect,
    UploadBtn
  },
  data() {
    return {
      dropList: [],
      isFirst: true // 用于第一次部门初始化的时候不埋点
    }
  },
  computed: {
    searchParams() {
      return this.$bus.searchParams
    },
    time: {
      get() {
        return this.searchTime
      },
      set(value) {
        this.$emit('changeSearchTime', value)
      }
    }
  },
  created() {
    this.initData()
  },
  methods: {
    initData() {
      sourceParams = JSON.parse(JSON.stringify(this.$bus.searchParams))
    },
    getDropListData(val) {
      getDropList({
        projectId: val
      }).then(d => {
        this.dropList = d ? d.map(item => {
          return {
            name: item.name,
            key: item.key,
            items: item.items ? item.items.map(v => {
              return {
                label: v.name,
                value: v.id
              }
            }) : []
          }
        }) : []
        // 把颜色的筛选项过虑
        this.dropList = this.dropList.filter(item => item.name !== '颜色')
      }).catch(err => {
        this.$logMsg.error(`[搜索框]获取颜色下拉数据失败：${err.message}`, 'search-box')
        this.$CustomToast({
          type: 'error',
          duration: 2,
          content: err.message
        })
      })
    },
    handleDeptChange(val) {
      this.$bus.updateProjectSelectedId(val)
      this.getDropListData(val)
      this.$bus.clearSearchData('inTagIds')
      if (val !== sourceParams.dept) {
        this.dataChange('projectId', val)
        // 除了页面加载的第一次以外，所有的部门切换都要埋点
        if (!this.isFirst) {
          this.$et('PLUGIN_SELECT_PROJECT')
        } else {
          this.isFirst = false
        }
      }
    },
    handleInputChange(val) {
      if (!judgeResultFun(val, sourceParams.tags)) {
        this.dataChange('tags', val)
      }
    },
    handleColorChange(id) {
      if (id !== sourceParams.colorRange) {
        this.dataChange('colorRange', id)
      }
    },
    handleMultiChange(key, val) {
      this.dataChange(key, val)
    },
    handleTagsClick(key, val) {
      this.$bus.setSearchValue(key, val)
      sourceParams[key] = val
    },
    handleSearch() {
      this.$emit('change', this.$bus.searchParams)
      this.$et('PLUGIN_SELECT_TAG')
    },
    dataChange(key, value) {
      this.$bus.setSearchValue(key, value)
      sourceParams[key] = value
      this.$emit('change', this.$bus.searchParams)
    },
    handleReqTimeChange(id) {
      this.$emit('handleReqTimeChange', id)
    },
    clearSearchTime() {
      this.$emit('changeSearchTime', ['', ''])
    }
  }
}
</script>
<style lang="less" scoped>
.reqTimeSelect {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: 4px 8px;
  width: auto !important;
  height: 24px !important;
  margin-left: 5px;
  font-size: 12px;
  cursor: pointer;
  border-radius: 4px;
  border: 1px solid #3E3E3F;
  color: #C7C7C7;
  background-color: transparent;
  position: relative;
  z-index: 200; /* 增加 z-index */

  &:hover {
    border: 1px solid #C7C7C7;
  }
}

/* 添加新的样式规则确保下拉菜单在最上层 */
:deep(.spectrum-Popover) {
  z-index: 1000 !important;
}

.m-search-box {
  width: 100%;
  padding: 0 12px 0 0;

  .u-select-item {
    flex: 1;
    display: flex;
    align-items: center;
  }

  .index-search-box {
    display: flex;
    width: 100%;
    margin: 8px 0;

    .dept-select {
      width: 120px;
    }

    .keyword-input {
      flex: 1;
    }
  }

  .tags-select {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin: 1px 0 3px;

    .selects {
      display: flex;
      align-items: center;

      .pickDate {
        position: relative;

        &:hover img {
          display: inline-block;
        }

        img {
          position: absolute;
          top: -5px;
          left: 0;
          display: none;
          cursor: pointer;
          z-index: 99;
        }
      }
    }

    .tags-select {
      flex: 1;
    }

    .upload-btn {
      width: auto;
    }
  }
}
</style>
