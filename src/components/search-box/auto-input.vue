<template>
  <div :class="{'spectrum-Search spectrum-Search--quiet': !isSearchWith}" class="u-search">
    <div :class="{'spectrum-Textfield--quiet': !isSearchWith}" class="u-search spectrum-Textfield"
         style="position: relative">
      <span v-if="leftDiv" class="dividing-line">
        <div class="dividing-line__inner">&nbsp;</div>
      </span>
      <span
        aria-hidden="true"
        class="spectrum-Icon spectrum-Icon--sizeM spectrum-Textfield-icon"
        focusable="false"
        style="z-index: 10; cursor: pointer;"
        @click="handlechange"
      >
        <IconFont :size="14" icon="search" style="color: white" />
      </span>
      <input
        v-model="values"
        :class="{
          'spectrum-SearchWithin-input': isSearchWith,
        }"
        autocomplete="off"
        class="spectrum-Textfield-input spectrum-Search-input"
        name="search"
        placeholder="搜索你想要的素材"
        style="z-index: 9; height: 32px; border: 1px solid #636363; border-left: none"
        type="search"
        @blur="handleBlur"
        @focus="opened=true"
        @input="handleInput"
        @keyup.enter.prevent="handlechange"
      >
      <button
        v-show="!values"
        class="searchImgBtn spectrum-ClearButton spectrum-Search-clearButton"
        style="z-index: 31;margin-right: 6px"
        type="reset"
        @click="searchImgModalVisible = true"
      >
        <img
          alt=""
          src="@/assets/img/searchImg.png"
        />
      </button>
    </div>
    <button
      v-show="values"
      class="spectrum-ClearButton spectrum-Search-clearButton"
      style="z-index: 31;margin-right: 6px"
      type="reset"
      @click="handleClear"
    >
      <IconFont :size="16" icon="empty" style="color: white" />
    </button>
    <div
      v-if="suggestOptions.length > 0"
      :class="{'is-open': opened}"
      :style="{
        width: popWidth
      }"
      class="spectrum-Popover"
      style="position: absolute; top: 38px; left: 0; z-index:500;"
    >
      <ul class="spectrum-Menu" role="listbox">
        <li
          v-for="item in suggestOptions"
          :key="`${searchCount}_${item.tag}`"
          :tabindex="`${searchCount}_${item.tag}`"
          class="spectrum-Menu-item is-selected"
          role="option"
          @click.stop="handleSelected(item.tag)"
        >
          <span class="spectrum-Menu-itemLabel">
            <div class="tag">{{ item.tag }}</div>
            <div class="count">{{ item.count }}素材</div>
          </span>
        </li>
      </ul>
    </div>

    <Modal v-model="searchImgModalVisible" width="260px">
      <template #content>
        <div
          style="display: flex; flex-direction: column; justify-content: center; align-items: center; padding: 20px 0">
          <div>选择图片，以图搜图</div>
          <div style="margin: 5px 0 15px 0">不会上传至素材库哦</div>
          <el-upload :auto-upload="false" :file-list="fileList" :multiple="false" :on-change="handleFileChange"
                     :show-file-list="false">
            <GButton slot="trigger" shape="circle" size="small" type="primary">上传图片</GButton>
          </el-upload>
        </div>
      </template>
    </Modal>
  </div>
</template>
<script>
import { getSuggestList, getUploadToken } from '@/api/index.js'
import Uploader from '@/assets/js/up-js-sdk/nos-js-sdk'
// import Upload from '@/components/basic/upload'
// import { _throttle } from '@/utils/index.js'
export default {
  // components: { Upload },
  props: {
    isSearchWith: {
      type: Boolean,
      default: false
    },
    leftDiv: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default() {
        return []
      }
    }
  },
  data() {
    return {
      values: '',
      opened: false,
      suggestOptions: [],
      searchCount: 0,
      popWidth: '300px',
      searchImgModalVisible: false,
      fileList: []
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    window.onresize = () => {
      this.popWidth = document.body.clientWidth <= 300 ? '100%' : '300px'
    }
  },
  activated() {
    this.initData()
  },
  methods: {
    initData() {
      this.values = this.value.join(' ')
    },
    handleClear() {
      this.values = ''
      this.handlechange()
    },
    handlechange() {
      const val = this.values.split(' ').filter(item => item)
      this.opened = false
      this.$emit('change', val)
    },
    handleInput() {
      this.handleInputChange()
      // console.log(123)
      // _throttle(() => {
      //   this.handleInputChange()
      // }, 200)
    },
    handleInputChange() {
      // console.log(0)
      this.searchCount = this.searchCount + 1
      const valueArr = this.values.split(' ').filter(item => item !== ' ')
      const keyword = valueArr.length > 0 ? valueArr[valueArr.length - 1] : ''
      if (keyword === '') {
        this.suggestOptions = []
        this.$emit('change', [])
      } else {
        getSuggestList({
          keyword,
          projectId: this.$bus.searchParams.projectId
        }).then(d => {
          // console.log(123)
          this.suggestOptions = d
        })
      }
    },
    handleSelected(item) {
      const valueArr = this.values.split(' ').filter(item => item !== ' ')
      valueArr.pop()
      valueArr.push(item)
      this.values = valueArr.join(' ')
      this.opened = false
      this.suggestOptions = []
      this.handlechange()
    },
    handleBlur() {
      setTimeout(() => {
        this.opened = false
      }, 600)
    },
    async handleFileChange({ raw: file }) {
      const result = await getUploadToken({ fileName: file.name, isPrivate: false })
      if (!result) return
      const params = {
        bucketName: result.bucket,
        objectName: result.key,
        token: result.token,
        trunkSize: 16 * 1024 * 1024
      }
      const uploader = Uploader({})
      uploader.addFile(file)
      uploader.upload(params)
      this.searchImgModalVisible = false
      await this.$router.push({
        name: 'searchImg',
        query: {
          bucket: result.bucket,
          key: result.key
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.u-search {
  width: 100%;
  padding: 0;
  z-index: 30;

  .spectrum-SearchWithin-input {
    background-color: #404040 !important;
  }

  .spectrum-Textfield-input {
    border-left: none;
  }

  .spectrum-Textfield:hover .spectrum-Textfield-input {
    color: #fff;
    border-color: #636363;
  }

  .spectrum-Menu-itemLabel {
    display: inline-flex;

    .tag {
      flex: 1;
      width: 0;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
    }

    .count {
      width: auto;
      color: rgba(255, 255, 255, 0.5);
      text-align: right;
    }
  }

  input:focus::placeholder {
    color: #909090;
  }

  .dividing-line {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 1px;
    border: 1px solid #636363;
    border-right: none;
    border-left: none;
    background-color: #404040;

    &__inner {
      width: 2px;
      border-radius: 50px;
      background-color: #2E2E2E;
      height: 24px;
    }
  }

  .searchImgBtn {
    position: absolute;
    right: 5px;
    top: 0;
    z-index: 9;
    width: 20px;
    cursor: pointer;

    img {
      width: 100%;
    }

    &:hover img {
      background-color: #ffffff44;
    }
  }
}
</style>
