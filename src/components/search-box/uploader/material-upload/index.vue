<template>
  <GButton @click="handleUpload" type="primary" size="small" style="width: 80px; margin-left: 6px">
    上传素材
  </GButton>
</template>
<script>
export default {
  name: 'MaterialUpload',
  data () {
    return {
      uploadTip: false
    }
  },
  methods: {
    handleUpload () {
      this.$router.push({
        name: 'upload'
      })
    }
  }
}
</script>
<style lang="less" scoped>
.spectrum-ActionButton--emphasized.is-selected {
  background-color: #1373E6;
  border-color: #1373E6;
  color: #ffffff;
  border-radius: 4px;
  &:hover {
    background-color: #197EFA;
    border-color: #197EFA;
  }
  &:active, &:focus {
    background-color: #1065CC;
    border-color: #1065CC;
  }
}
</style>
