<template>
  <div class="uploader-btn">
    <div v-if="projectList.length > 0 && routerName !== 'upload'" class="u-upload spectrum-Examples-itemGroup tags-groups">
      <material-upload/>
    </div>
  </div>
</template>
<script>
import MaterialUpload from './material-upload/index.vue'
export default {
  name: 'uploader-btn',
  components: {
    MaterialUpload
  },
  computed: {
    projectList () {
      return this.$bus.projectList
    },
    routerName () {
      return this.$route.name || ''
    }
  },
  data () {
    return {
      open: false,
      uploadTip: false
    }
  }
}
</script>
<style lang="less" scoped>
.uploader-btn {
  display: inline-block;
  .u-upload {
    // position: absolute;
    // right: 20px;
    // top: 6px;
    // cursor: pointer;
  }
}
</style>
