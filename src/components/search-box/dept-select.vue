<template>
<div class="u-dept-select">
  <multi-select v-model="project" :hasValue="true" :isSearchWith="isSearchWith" :width="isSearchWith ?  218 : 'auto'" label="所属项目" :options="projectList"/>
</div>
</template>
<script>
export default {
  name: 'DeptSelect',
  props: {
    isSearchWith: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      project: ''
    }
  },
  computed: {
    projectList () {
      return this.$bus.projectList
    },
    projectId () {
      return this.$bus.searchParams.projectId
    }
  },
  watch: {
    projectId (val) {
      this.project = val
    },
    project (val) {
      this.$emit('change', val)
    }
  }
}
</script>
<style lang="less" scoped>
.u-dept-select {
  width: 100%;
  z-index: 20;
}
</style>
