<template>
  <div v-clickoutside="handleClose" class="u-multi-select">
    <button
      :class="{'is-open': opened}"
      class="spectrum-Picker spectrum-Picker--sizeS spectrum-Picker--quiet"
      aria-haspopup="listbox"
      @click="handleOpen"
    >
      <span class="spectrum-Picker-label">{{ label }}</span>
      <svg
        :class="{
          'spectrum-UIIcon-ChevronDown100': !opened,
          'spectrum-UIIcon-ChevronUp100': opened
        }"
        class="spectrum-Icon spectrum-Picker-menuIcon"
        focusable="false"
        aria-hidden="true"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="10"
          height="10"
          viewBox="0 0 10 10"
        >
          <g id="ChevronSize75">
            <rect
              id="Frame"
              width="10"
              height="10"
              fill="red"
              opacity="0"
            />
            <path d="M7.4834,4.40625,3.85986.7832a.83969.83969,0,0,0-1.1875,1.1875L5.70166,5,2.67236,8.0293a.83969.83969,0,1,0,1.1875,1.1875l3.62354-3.623A.83933.83933,0,0,0,7.4834,4.40625Z"/>
          </g>
        </svg>
      </svg>
    </button>
    <div
      :class="{'is-open': opened}"
      class="spectrum-Popover spectrum-Popover--bottom spectrum-Picker-popover spectrum-Picker-popover--quiet"
      style="z-index: 100; max-height: 240px;"
    >
      <ul class="spectrum-Menu" role="listbox">
        <template v-if="options.length > 0">
          <li
            v-if="allChecked"
            :key="-1"
            class="u-select-li spectrum-Menu-item is-selected"
            role="option"
            aria-selected="true"
          >
            <label
              :class="{'is-indeterminate': selectValue.length !== options.length}"
              class="spectrum-Checkbox spectrum-Checkbox--sizeS u-select-item"
            >
              <span class="spectrum-Checkbox-label u-select-label">全部</span>
              <input
                :checked="selectValue.length === options.length"
                type="checkbox"
                class="spectrum-Checkbox-input"
                @change="e => handleAllChecked(e)"
              >
              <span class="spectrum-Checkbox-box u-select-checkbox">
                <svg class="spectrum-Icon spectrum-UIIcon-Checkmark100 spectrum-Checkbox-checkmark" focusable="false" aria-hidden="true">
                  <!-- <use xlink:href="#spectrum-css-icon-Checkmark100" /> -->
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="10"
                    height="10"
                    viewBox="0 0 10 10"
                  >
                    <g id="CheckmarkSize100">
                      <rect
                        id="Frame"
                        width="10"
                        height="10"
                        fill="red"
                        opacity="0"
                      />
                      <path d="M9.56445.71094a.9982.9982,0,0,0-1.40332.1748L3.4812,6.89648l-1.657-2.0288A.99977.99977,0,0,0,.27539,6.13232l2.4502,3c.02154.02637.05328.0362.07678.05976a.93683.93683,0,0,0,.08337.097.95478.95478,0,0,0,.13367.072.95912.95912,0,0,0,.10351.05579A.99454.99454,0,0,0,3.49951,9.5L3.5,9.49988,3.50049,9.5a.99309.99309,0,0,0,.38635-.08765.962.962,0,0,0,.10724-.06024.94719.94719,0,0,0,.13824-.0777.94481.94481,0,0,0,.082-.09991c.02264-.02387.05407-.03375.0747-.06024l5.4502-7A1.00056,1.00056,0,0,0,9.56445.71094Z"/>
                    </g>
                  </svg>
                </svg>
                <svg class="spectrum-Icon spectrum-UIIcon-Dash100 spectrum-Checkbox-partialCheckmark" focusable="false" aria-hidden="true">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="10"
                    height="10"
                    viewBox="0 0 10 10"
                  >
                    <g id="DashSize100">
                      <rect
                        id="Frame"
                        width="10"
                        height="10"
                        fill="red"
                        opacity="0"
                      />
                      <path d="M8.5,6h-7a1,1,0,0,1,0-2h7a1,1,0,0,1,0,2Z"/>
                    </g>
                  </svg>
                  <!-- <use xlink:href="#spectrum-css-icon-Dash100" /> -->
                </svg>
              </span>
            </label>
          </li>
          <li
            v-for="(item, index) in options"
            :key="index"
            :tabindex="index"
            class="u-select-li spectrum-Menu-item is-selected"
            role="option"
            aria-selected="true"
          >
            <label class="spectrum-Checkbox spectrum-Checkbox--sizeS u-select-item">
              <span class="spectrum-Checkbox-label u-select-label">{{ item.label }}</span>
              <input
                :value="item.value"
                :id="`checkbox-${index}`"
                :checked="selectValue.includes(item.value)"
                type="checkbox"
                class="spectrum-Checkbox-input"
                @change="e => handleCheck(e, item)"
              >
              <span class="spectrum-Checkbox-box u-select-checkbox">
                <svg class="spectrum-Icon spectrum-UIIcon-Checkmark100 spectrum-Checkbox-checkmark" focusable="false" aria-hidden="true">
                  <!-- <use xlink:href="#spectrum-css-icon-Checkmark100" /> -->
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="10"
                    height="10"
                    viewBox="0 0 10 10"
                  >
                    <g id="CheckmarkSize100">
                      <rect
                        id="Frame"
                        width="10"
                        height="10"
                        fill="red"
                        opacity="0"
                      />
                      <path d="M9.56445.71094a.9982.9982,0,0,0-1.40332.1748L3.4812,6.89648l-1.657-2.0288A.99977.99977,0,0,0,.27539,6.13232l2.4502,3c.02154.02637.05328.0362.07678.05976a.93683.93683,0,0,0,.08337.097.95478.95478,0,0,0,.13367.072.95912.95912,0,0,0,.10351.05579A.99454.99454,0,0,0,3.49951,9.5L3.5,9.49988,3.50049,9.5a.99309.99309,0,0,0,.38635-.08765.962.962,0,0,0,.10724-.06024.94719.94719,0,0,0,.13824-.0777.94481.94481,0,0,0,.082-.09991c.02264-.02387.05407-.03375.0747-.06024l5.4502-7A1.00056,1.00056,0,0,0,9.56445.71094Z"/>
                    </g>
                  </svg>
                </svg>
                <svg class="spectrum-Icon spectrum-UIIcon-Dash100 spectrum-Checkbox-partialCheckmark" focusable="false" aria-hidden="true">
                  <!-- <use xlink:href="#spectrum-css-icon-Dash100" /> -->
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="10"
                    height="10"
                    viewBox="0 0 10 10"
                  >
                    <g id="DashSize100">
                      <rect
                        id="Frame"
                        width="10"
                        height="10"
                        fill="red"
                        opacity="0"
                      />
                      <path d="M8.5,6h-7a1,1,0,0,1,0-2h7a1,1,0,0,1,0,2Z"/>
                    </g>
                  </svg>
                </svg>
              </span>
            </label>
          </li>
        </template>
        <li
          v-else
          class="spectrum-Menu-item is-disabled"
          role="option"
          aria-disabled="true"
        >
          <span class="spectrum-Menu-itemLabel">暂无数据</span>
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    value: {
      type: Array,
      default () {
        return []
      }
    },
    allChecked: {
      type: Boolean,
      default: false
    },
    label: {
      type: String,
      default: ''
    },
    options: {
      type: Array,
      default () {
        return []
      }
    }
  },
  data () {
    return {
      selectValue: [],
      opened: false
    }
  },
  // watch: {
  //   opened (val) {
  //     if (!val) {
  //       this.$emit('change', this.selectValue)
  //     }
  //   },
  // },
  created () {
    this.initData()
  },
  methods: {
    initData () {
      this.selectValue = this.value
    },
    handleOpen () {
      this.opened = !this.opened
    },
    handleClose () {
      this.opened = false
    },
    handleAllChecked (e) {
      if (e.target.checked) {
        this.selectValue = this.options.map(item => {
          return item.value
        })
      } else {
        this.selectValue = []
      }
      this.$emit('change', this.selectValue)
    },
    handleCheck (e, item) {
      if (e.target.checked) {
        this.selectValue.push(item.value)
      } else {
        this.selectValue = this.selectValue.filter(value => value !== item.value)
      }
      this.$emit('change', this.selectValue)
    }
  }
}
</script>
<style lang="less" scoped>
.u-multi-select {
  display: inline-block;
  margin-right: 20px;
  .u-select-li {
    padding: 3px 12px;
    .u-select-item {
      min-height: auto;
    }
    .u-select-label {
      margin: 0;
      width: 72px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .u-select-checkbox {
      margin: 2px 0;
    }
  }
}
</style>
