<template>
  <div v-clickoutside="handleClose" class="c-color-select">
    <button
      :class="{'is-open': opened}"
      class="spectrum-Picker spectrum-Picker--sizeS spectrum-Picker--quiet"
      aria-haspopup="listbox"
      @click="handleOpen"
    >
      <span class="spectrum-Picker-label">
        颜色
        <span
          v-show="checkedItem.id && checkedItem.id !== null"
          :class="checkedColorClass"
          :style="{backgroundColor: checkedItem.color}"
          class="checked-color"></span>
      </span>
      <IconFont
        icon="down-arrow"
        :size="22"
        :style="{ marginLeft: '3px' }"
        :class="{ rotate180: opened }"
      />
     
    </button>
    <div
      :class="{'is-open': opened}"
      class="spectrum-Popover spectrum-Popover--bottom spectrum-Picker-popover spectrum-Picker-popover--quiet"
      style="z-index: 100;"
    >
      <div class="color-index">
        <span
          v-for="item in colorArray"
          :class="colorClass(item)"
          :key="item.id"
          :style="{ backgroundColor: item.color }"
          class="color-item"
          @click="onChecked(item)"
        >
          <!-- <i class="iconfont icon-check checked" v-if="checkedItem.id === item.id"></i> -->
        </span>
      </div>
    </div>
  </div>
</template>
<script>
// eslint-disable-next-line camelcase
const grayId = 'GREY'
export default {
  props: {
    value: {
      // eslint-disable-next-line vue/require-prop-type-constructor
      type: String | null,
      default () {
        return null
      }
    }
  },
  data () {
    return {
      opened: false,
      colorArray: [
        { id: null, color: '#ffffff', checked: false },
        { id: 'RED', color: '#F20000', checked: false },
        { id: 'ORANGE', color: '#F27900', checked: false },
        { id: 'YELLOW', color: '#F2F200', checked: false },
        { id: 'GREEN', color: '#00F200', checked: false },
        { id: 'BLUE_GREEN', color: '#00F2F2', checked: false },
        { id: grayId, color: '#ffffff', checked: false },
        { id: 'PINK', color: '#F20079', checked: false },
        { id: 'FUCHSIA', color: '#F200F2', checked: false },
        { id: 'PURPLE', color: '#7900F2', checked: false },
        { id: 'BLUE', color: '#0000F2', checked: false },
        { id: 'CYAN', color: '#0079F2', checked: false }
      ],
      checkedItem: {
        color: '', id: ''
      }
    }
  },
  computed: {
    checkedColorClass () {
      const id = this.checkedItem.id
      let className
      if (id === null) {
        className = 'c-line'
      } else if (id === grayId) {
        className = 'c-triangle'
      }
      return className
    }
  },
  watch: {
    value (newVal, oldVal) {
      this.initData(newVal)
    }
  },
  created () {
    this.initData()
  },
  methods: {
    initData (val = undefined) {
      const colorArray = JSON.parse(JSON.stringify(this.colorArray))
      const findIndex = colorArray.findIndex(item => item.id === (val === undefined ? this.value : val))
      if (findIndex > -1) {
        this.checkedItem.id = colorArray[findIndex].id
        this.checkedItem.color = colorArray[findIndex].color
      }
      this.colorArray = colorArray
    },
    // 获取颜色的类值
    colorClass (item) {
      if (item.id === null) {
        return 'c-line'
      } else if (item.id === grayId) {
        return 'c-triangle'
      }
    },
    handleOpen () {
      this.opened = !this.opened
    },
    handleClose () {
      this.opened = false
    },
    onChecked (item) {
      this.checkedItem = item
      this.colorArray.forEach(color => {
        if (color.id === item.id) {
          color.checked = true
        } else {
          color.checked = false
        }
      })
      this.$emit('change', item.id)
      this.opened = false
    }
  }
}
</script>
<style lang="less" scoped>
.c-color-select {
  display: inline-block;
  margin-right: 12px;
  .color-index {
    width: 185px;
  }
  .checked-color{
    border: 1px solid #cccccc94;
    position: relative;
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin: 0px 5px;
    vertical-align: -1px;
    overflow: hidden;
  }
  .color-item {
    float: left;
    width: 20px;
    height: 20px;
    margin: 5px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .iconfont {
      font-size: 12px;
    }
    .checked{
      color: #ffffff;
    }
    &.c-line, &.c-triangle{
      overflow: hidden;
      border: 1px solid #cccccc94;
    }
  }
  .c-triangle:before{
    position: absolute;
    left: -2px;
    right: 0px;
    content: '';
    height: 0px;
    width: 0px;
    border-top: 25px solid #000000;
    border-left: 23px solid transparent;
  }
  .c-line:before{
    content: '';
    width: 1px;
    height: 100%;
    position: absolute;
    left: 8px;
    top: 0px;
    background: #E1E3E6;
    transform: rotate(-43deg) scale(1.33);
  }
}
</style>
