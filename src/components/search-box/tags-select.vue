<template>
  <div class="tags-select" >
    <div v-if="opened" class="model"></div>
    <div  v-clickoutside="handleClose">
      <div class="spectrum-Examples-itemGroup" >
        <GButton size="small" @click="handelVisible" ghost>
          颜色
          <span
            v-show="checkedItem.id && checkedItem.id !== null"
            :class="checkedColorClass"
            :style="{ backgroundColor: checkedItem.color }"
            class="checked-color"
          ></span>
          <IconFont
            icon="down-arrow"
            :size="22"
            :style="{ marginLeft: '3px' }"
            :class="{ rotate180: opened }"
          />
        </GButton>

        <GButton
          :key="item.key"
          v-for="item in dropList"
          size="small"
          @click="handelVisible"
          ghost
        >
          {{ item.name }}
          {{
            searchParams[item.key] && searchParams[item.key].length
              ? `(${searchParams[item.key].length})`
              : ''
          }}
          <IconFont
            icon="down-arrow"
            :size="22"
            :style="{ marginLeft: '3px' }"
            :class="{ rotate180: opened }"
          />
        </GButton>
      </div>
      <div
        v-if="opened"
        class="spectrum-Popover spectrum-Popover--bottom spectrum-Picker-popover is-open"
        :style="{ 'z-index': 6, width: popWidth }"
      >
        <div class="tags-group-item">
          <div class="tags-group-label" @click="handleToggle('colorRange')">
            颜色
            <IconFont
              class="arrow-icon"
              icon="down-arrow2"
              :size="22"
              :class="{ 'is-close': !tagToggle['colorRange'] }"
            />
          </div>
          <div
            class="tags-groups"
            :class="{ 'is-close': !tagToggle['colorRange'] }"
          >
            <div class="color-index-wraper">
              <div class="color-index">
                <span
                  v-for="item in colorArray"
                  :class="colorClass(item)"
                  :key="item.id"
                  :style="{ backgroundColor: item.color }"
                  class="color-item"
                  @click="onChecked(item)"
                >
                  <IconFont
                    icon="tick"
                    :size="18"
                    v-if="checkedItem.id === item.id"
                    :style="{
                      color:
                        checkedItem.color === '#ffffff' ? '#000' : '#ffffff',
                    }"
                  />
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="tags-group-item" v-for="item in dropList" :key="item.key">
          <div class="tags-group-label" @click="handleToggle(item.key)">
            {{ item.name
            }}{{ item.items.length > 0 ? `(${item.items.length})` : '' }}
            <IconFont
              class="arrow-icon"
              icon="down-arrow2"
              :size="22"
              :class="{ 'is-close': !tagToggle[item.key] }"
            />
          </div>
          <div
            class="spectrum-Examples-itemGroup tags-groups"
            :class="{ 'is-close': !tagToggle[item.key] }"
          >
            <button
              key="0"
              :class="{
                'is-selected':
                  !searchParams[item.key] || !searchParams[item.key].length,
              }"
              class="spectrum-ActionButton spectrum-ActionButton--sizeS spectrum-ActionButton--emphasized"
              @click="handleMultiCancel(item.key)"
            >
              <span class="spectrum-ActionButton-label">不限</span>
            </button>
            <button
              v-for="tag in item.items"
              :key="`${item.key}_${tag.value}`"
              :class="{
                'is-selected':
                  searchParams[item.key] &&
                  searchParams[item.key].includes(tag.value),
              }"
              class="spectrum-ActionButton spectrum-ActionButton--sizeS spectrum-ActionButton--emphasized"
              @click="handleMultiChange(item.key, tag.value)"
            >
              <span class="spectrum-ActionButton-label">{{ tag.label }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
// eslint-disable-next-line camelcase
const grayId = 'GREY'
export default {
  name: 'TagsSelect',
  props: {
    colorRange: {
      type: String || null,
      default() {
        return null
      },
    },
    dropList: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      colorArray: [
        { id: null, color: '#ffffff', checked: false },
        { id: grayId, color: '#ffffff', checked: false },
        { id: 'RED', color: '#F20000', checked: false },
        { id: 'ORANGE', color: '#F27900', checked: false },
        { id: 'YELLOW', color: '#F2F200', checked: false },
        { id: 'GREEN', color: '#00F200', checked: false },
        { id: 'BLUE_GREEN', color: '#00F2F2', checked: false },
        { id: 'PINK', color: '#F20079', checked: false },
        { id: 'FUCHSIA', color: '#F200F2', checked: false },
        { id: 'PURPLE', color: '#7900F2', checked: false },
        { id: 'BLUE', color: '#0000F2', checked: false },
        { id: 'CYAN', color: '#0079F2', checked: false },
      ],
      checkedItem: {
        color: '',
        id: '',
      },
      tagToggle: {
        colorRange: true,
      },
      opened: false,
      popWidth: '0px',
    }
  },
  computed: {
    checkedColorClass() {
      const id = this.checkedItem.id
      let className
      if (id === null) {
        className = 'c-line'
      } else if (id === grayId) {
        className = 'c-triangle'
      }
      return className
    },
    searchParams() {
      return this.$bus.searchParams
    },
  },
  watch: {
    dropList: {
      deep: true,
      handler(val) {
        val.forEach((item) => {
          this.$set(this.tagToggle, item.key, true)
        })
      },
    },
    colorRange(newVal) {
      this.initData(newVal)
    },
    opened(val) {
      if (val) {
        this.oSearchParams = JSON.stringify(this.searchParams)
        this.$set(this.tagToggle, 'colorRange', true)
        this.dropList.forEach((item) => {
          this.$set(this.tagToggle, item.key, true)
        })
      }
    },
  },
  created() {
    this.initData()
  },
  methods: {
    initData(val = undefined) {
      const colorArray = JSON.parse(JSON.stringify(this.colorArray))
      const findIndex = colorArray.findIndex(
        (item) => item.id === (val === undefined ? this.colorRange : val)
      )
      if (findIndex > -1) {
        this.checkedItem.id = colorArray[findIndex].id
        this.checkedItem.color = colorArray[findIndex].color
      }
      this.colorArray = colorArray
    },
    // 获取颜色的类值
    colorClass(item) {
      if (item.id === null) {
        return 'c-line'
      } else if (item.id === grayId) {
        return 'c-triangle'
      }
    },
    onChecked(item) {
      this.checkedItem = item
      this.colorArray.forEach((color) => {
        if (color.id === item.id) {
          color.checked = true
        } else {
          color.checked = false
        }
      })
      this.$emit('change', 'colorRange', item.id)
      this.$emit('toggle')
      this.$forceUpdate()
    },
    handleMultiChange(key, val) {
      const searchParams = JSON.parse(JSON.stringify(this.searchParams))
      if (searchParams[key] && searchParams[key].includes(val)) {
        searchParams[key].splice(searchParams[key].indexOf(val), 1)
      } else {
        if (!searchParams[key]) {
          searchParams[key] = []
        }
        searchParams[key].push(val)
      }
      this.$emit('change', key, searchParams[key])
      this.$emit('toggle')
      this.$forceUpdate()
      // this.$nextTick(() => {
      //   this.$forceUpdate()
      //   console.log(this.$bus.searchParams)
      // })
    },
    handleMultiCancel(key) {
      this.$emit('change', key, [])
      this.$emit('toggle')
      this.$forceUpdate()
    },
    handleToggle(key) {
      this.$set(this.tagToggle, key, !this.tagToggle[key])
    },
    handelVisible(e){
      e.stopPropagation()
      this.opened = !this.opened
    },
    handleClose() {
      if (this.opened) {
        this.opened = false
      }
    },
  },
  mounted() {
    this.popWidth = `${document.body.clientWidth - 24}px`
    window.onresize = () => {
      this.popWidth = `${document.body.clientWidth - 24}px`
    }
  },
}
</script>
<style lang="less" scoped>
.tags-select {
  position: relative;
  .model {
    position: fixed;
    content: '';
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: transparent;
    z-index: 5;
  }
  .checked-color {
    border: 1px solid #cccccc94;
    position: relative;
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin: 0px 0px 0px 4px;
    overflow: hidden;
  }
  .color-item {
    float: left;
    width: 20px;
    height: 20px;
    margin: 5px;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    .iconfont {
      font-size: 12px;
    }
    .checked {
      color: #ffffff;
    }
    &.c-line,
    &.c-triangle {
      overflow: hidden;
      border: 1px solid #cccccc94;
    }
  }
  .c-triangle:before {
    position: absolute;
    left: -2px;
    right: 0px;
    content: '';
    height: 0px;
    width: 0px;
    border-top: 25px solid #000000;
    border-left: 23px solid transparent;
  }
  .c-line:before {
    content: '';
    width: 1px;
    height: 100%;
    position: absolute;
    left: 8px;
    top: 0px;
    background: #e1e3e6;
    transform: rotate(-43deg) scale(1.33);
  }
  .spectrum-Popover {
    position: absolute;
    top: 25px;
    padding: 4px 12px;
    max-height: 400px;
    overflow-y: auto;
    .tags-group-item {
      margin: 4px 0;
      .tags-group-label {
        font-size: 12px;
        color: #ffffff;
        margin-bottom: 8px;
        cursor: pointer;
        .arrow-icon {
          display: inline-block;
          &.is-close {
            transform: rotate(90deg);
          }
        }
      }
      .tags-groups {
        margin: 0 -2px;
        height: auto;
        overflow: hidden;
        .color-index-wraper {
          margin: -3px;
        }
        .color-index {
          display: table-cell;
        }
        &.is-close {
          height: 0px;
          display: none;
        }
        // .spectrum-ActionButton {
        //   margin: 0px 5px 5px 0px;
        //   padding: 0px 10px;
        //   height: 24px;
        //   line-height: 24px;
        //   font-size: 12px;
        //   border-radius: 4px;
        //   border: 1px solid #cccccc94;
        //   background: #ffffff;
        //   color: #666666;
        //   &:hover {
        //     border-color: #cccccc;
        //   }
        // }
      }
    }
  }
  .spectrum-ActionButton {
    margin: 2px;
    border-color: #3e3e3e;
    border-radius: 4px;
    background-color: transparent;
    &.spectrum-ActionButton--emphasized.is-selected {
      background-color: #2c75de;
      border-color: #2c75de;
      color: #ffffff;
    }
    &.unlimited {
      border-color: #383838;
      background-color: #474747;
      color: #c7c7c7;
      &:hover {
        border-color: #383838;
        background-color: #535353;
        color: #ffffff;
      }
      &:active {
        border-color: #383838;
        background-color: #383838;
        color: #c7c7c7;
      }
    }
  }
  .color-picker {
    transform: translateX(-2px);
  }
}
</style>
