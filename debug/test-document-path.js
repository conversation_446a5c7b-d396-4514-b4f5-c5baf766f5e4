/**
 * 测试获取当前文档路径的脚本
 * 在CEP环境中运行此脚本来调试文档路径获取问题
 */

/* eslint-disable no-undef */

function testDocumentPath() {
  console.log('=== 开始测试文档路径获取 ===')
  
  const cs = new CSInterface()
  
  // 1. 检查文档数量
  cs.evalScript('app.documents.length', (docCount) => {
    console.log('1. 文档数量:', docCount)
    
    if (!docCount || docCount === '0') {
      console.error('❌ 没有打开的文档')
      return
    }
    
    // 2. 获取活动文档名称
    cs.evalScript('app.activeDocument.name', (docName) => {
      console.log('2. 活动文档名称:', docName)
      
      // 3. 检查文档是否已保存
      cs.evalScript('app.activeDocument.saved', (isSaved) => {
        console.log('3. 文档是否已保存:', isSaved)
        
        // 4. 获取文档完整路径
        cs.evalScript('app.activeDocument.fullName.fsName', (fullPath) => {
          console.log('4. 文档完整路径:', fullPath)
          
          if (fullPath && fullPath !== 'undefined' && fullPath !== 'null') {
            const cleanPath = fullPath.replace(/^["']|["']$/g, '')
            console.log('5. 清理后的路径:', cleanPath)
            
            // 6. 检查文件是否存在
            const fs = custom_node.fs
            if (fs.existsSync(cleanPath)) {
              console.log('✅ 文件存在')
              
              // 7. 获取文件信息
              const stats = fs.statSync(cleanPath)
              console.log('7. 文件大小:', stats.size, 'bytes')
              console.log('7. 文件修改时间:', stats.mtime)
            } else {
              console.error('❌ 文件不存在:', cleanPath)
              
              // 尝试列出父目录内容
              const path = require('path')
              const dir = path.dirname(cleanPath)
              console.log('父目录:', dir)
              
              if (fs.existsSync(dir)) {
                const files = fs.readdirSync(dir)
                console.log('父目录内容:', files)
              } else {
                console.error('❌ 父目录也不存在')
              }
            }
          } else {
            console.error('❌ 无法获取文档路径，可能文档未保存')
          }
        })
      })
    })
  })
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { testDocumentPath }
} else {
  // 在浏览器环境中直接运行
  window.testDocumentPath = testDocumentPath
}

console.log('测试脚本已加载，请调用 testDocumentPath() 来运行测试')
