{"name": "com.netease.library.local", "version": "5.6.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode game", "start": "npm run serve", "build:local": "vue-cli-service build --mode local", "build:qa": "vue-cli-service build --mode qa", "build:game": "vue-cli-service build --mode game", "build": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint", "update-exe": "node scripts/update-exe.js", "package": "node scripts/pack.js", "package-qa": "node scripts/pack.js qa", "upload-zxp": "node scripts/upload-zxp.js"}, "dependencies": {"@adobe/spectrum-css-workflow-icons": "^1.2.1", "@spectrum-css/actionbutton": "^1.0.3", "@spectrum-css/badge": "^1.0.1", "@spectrum-css/button": "^3.0.3", "@spectrum-css/checkbox": "^3.0.3", "@spectrum-css/divider": "^1.0.3", "@spectrum-css/fieldlabel": "^3.0.3", "@spectrum-css/icon": "^3.0.3", "@spectrum-css/link": "^3.1.4", "@spectrum-css/menu": "^3.0.3", "@spectrum-css/page": "^3.0.2", "@spectrum-css/picker": "^1.0.3", "@spectrum-css/popover": "^3.0.3", "@spectrum-css/search": "^3.0.3", "@spectrum-css/searchwithin": "^3.4.22", "@spectrum-css/tag": "^2.0.1", "@spectrum-css/textfield": "^3.0.2", "@spectrum-css/thumbnail": "^1.0.2", "@spectrum-css/toast": "^3.0.3", "@spectrum-css/tooltip": "^3.0.4", "@spectrum-css/typography": "^3.0.2", "@spectrum-css/vars": "^3.0.2", "@spectrum-css/well": "^3.0.2", "axios": "^0.21.1", "blueimp-md5": "^2.19.0", "copy-to-clipboard": "^3.3.3", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "dayjs": "^1.11.7", "element-ui": "^2.15.14", "jsencrypt": "^3.2.1", "jszip": "^3.10.1", "minio": "7.0.20", "moment": "^2.29.4", "popper.js": "1.14.6", "psd": "^3.4.0", "request": "^2.88.2", "store": "^2.0.12", "string-random": "^0.1.3", "uuid": "^9.0.1", "vue": "^2.6.11", "vue-router": "^3.2.0", "watermark-dom": "^2.3.0", "yauzl": "^2.10.0", "zip-folder": "^1.0.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "less": "^4.1.3", "less-loader": "5.0.0", "lint-staged": "^9.5.0", "vue-template-compiler": "^2.6.11", "webpack-iconfont-plugin-nodejs": "^1.0.29"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint", "git add"]}}