# Adobe插件-素材库

## 1. 项目描述

- 项目名：Adobe插件-素材库
- git仓库地址：`ssh://********************:22222/uedc-utils/com.adobe.library.git`
- 分支：`master`
- 本项目第一负责人：李菁颖

## 2. 如何运行

  ```
  node版本[>=12]
  yarn
  ```

### 2.1 开发环境配置

1. 项目环境划分

   测试环境：http://*************:8999/
   正式环境：http://dubhe.netease.com/plugin/

2. 环境切换规范

   正式环境分支 `master`，开发从这条分支拉取

3. 开发流程

   把项目拉取到PS插件目录下 `/Library/Application Support/Adobe/CEP/extensions/com.netease.library.local`

4. 环境配置

   不同环境HOST配置入口在根目录的`.env.*` 文件

5. 依赖环境

   开发环境下尽量使用 PhotoShop 2021版本。
   目前最新2023版本，在开发环境是iframe的形式下会有点击选择等问题，线上由于打包成静态文件暂无此问题。
   若插件放入PS插件目录后，报错"未签名"
   ，请参考[文档](href="http://blog.nullice.com/%E6%8A%80%E6%9C%AF/CEP-%E5%BC%80%E5%8F%91%E6%95%99%E7%A8%8B/%E6%8A%80%E6%9C%AF-CEP-%E5%BC%80%E5%8F%91%E6%95%99%E7%A8%8B-Adobe-CEP-%E6%89%A9%E5%B1%95%E5%BC%80%E5%8F%91%E6%95%99%E7%A8%8B-%E3%80%8C-1-%E3%80%8DHello-World/#mac-os")
   打开开发者模式

6. 浏览器模拟器 devtools

    1. 确认项目已在PS插件目录下。
    2. Chrome进入[chrome://inspect](chrome://inspect) （Edeg进入[edge://inspect](edge://inspect)）。
    3. 勾选`Discover network targets`，让devtools可以捕获网络。
    4. 点击configure，新增网络端口，输入`localhost:9012`，下方的`Enable port forwarding`可勾可不勾，勾了之后可通过
       `localhost:9012`
       直接打开devtools，但80版本之后的chrome均不兼容，老版chromium[下载地址](href="https://devproducts.oss-cn-shanghai.aliyuncs.com/devtools/Mac_706915_chrome-mac.zip")。
    5. 点击下方的`inspect`可进入devtools。

### 2.2 开发过程

1. 命令

  ```bash
  yarn install  // 依赖安装
  yarn update-exe // 下载客户端最新的服务包,本地开发需将exe包复制到项目中
  yarn serve    // 服务启动

  yarn package// 打包（测试、正式、游戏部门独立部署打包）
  ```

2. 不同环境接口配置

  ```
  .env.local    // 本地环境
  .env.qa       // 测试环境
  .env.prod     // 正式环境
  .env.game     // 游戏独立部署环境
  ```

3. 配置信息

  ```
  VUE_APP_HOST              // 请求服务器的base地址
  VUE_APP_SOURCE_PATHNAME   // 插件包名称，主要取决于文件下载到哪个文件夹下
  VUE_APP_GA_ID             // Google Analyze的ID号
  ```

### 2.3 发布

NDP：uedc-static集群 library-plugin-static-online

#### Mac apple公证：

1. 准备apple开发者账号，个人/团队均可
2. 创建应用专用密码
    1. 访问 https://account.apple.com 并登录
    2. 在【安全】栏中找到【应用专用密码】
    3. 生成密码
        1. 名称固定，是【Notarization】，代码中已经写死了名称，所以在生成的时候要固定
        2. 密码要记住，在后续需要用到
3. 在 terminal 使用 notarytool 存储凭证
   `notarytool store-credentials <credential_name> --apple-id <your_apple_id> --password <your_app_specific_password>`
    1. credential_name 为名称，已固定为【Notarization】
    2. your_apple_id 为apple开发者账号
    3. your_app_specific_password 为刚才2-C生成的应用专用密码
    4. developer team id 为开发者团队id，可在 https://account.apple.com 查看
4. 提交公正即可完成公正（这一步在实际中不需要操作，打包过程中已经在脚本里操作了，所以只需要完成步骤1~3即可）

### 2.4 错误告警及监控

1. 数据监控：Google Analytics

### 2.5 相关人员

| 角色   | 人员  |
|------|-----|
| 产品经理 | 王宇琪 |
| 视觉设计 | 徐剑杰 |
| 后台开发 | 狄鑫  |
| 前端开发 | 庄小云 |

### 2.6 其他

交互稿：https://www.figma.com/file/0jOhyS4oSfZN62OfIG8muK/%E5%95%86%E4%B8%9A%E5%8C%96%E6%B5%81%E7%A8%8B?node-id=1037%3A0

视觉稿：https://www.figma.com/file/0jOhyS4oSfZN62OfIG8muK/%E5%95%86%E4%B8%9A%E5%8C%96%E6%B5%81%E7%A8%8B?node-id=1568%3A0

## 3. 业务介绍

adobe素材插件

## 4. 项目备注

### 4.1 插件安装包

1. 安装包存放地址：`/zxp
2. 对应插件安装包：

  ```
    com.netease.library       // 正式包
    com.netease.library.qa    // 测试包
    com.netease.library.game  // 游戏部门独立部署包
  ```

3. 插件包打包脚本
    1. 修改 `package.json` 文件下的 `version`，同时同步webview的版本号
    2. `yarn package`
    3. 插件包结果在zxp上，在ndp上面直接部署的时候走zxp目录下的所有文件

### 4.2 Adobe插件开发参考文档

- [CEP 10 HTML Extension Cookbook](https://github.com/Adobe-CEP/CEP-Resources/blob/master/CEP_10.x/Documentation/CEP%2010.0%20HTML%20Extension%20Cookbook.md)

- [ADOBE PHOTOSHOP SCRIPTING](https://www.adobe.com/devnet/photoshop/scripting.html)

- [PS脚本文档](https://gitee.com/code_yu/photoshop-javascript)

- [Adobe CEP 扩展开发教程](http://nullice.com/archives/category/note/%e8%bd%af%e4%bb%b6%e6%95%99%e7%a8%8b/adobe-cep)
  Cookbook中文翻译系列文章

- [CEP 扩展开发 Bebug](https://juejin.cn/post/7131585039740960798)

# 历史版本日志

## 4.0.4

1. 增加前端日志打点
2. 增加日志上报功能
3. 优化代码、删除冗余文件
4. 增加update-exe 更新exe的指令，不用手动替换包

## 4.0.5

1. 项目默认用户上一次选择；
2. 登陆提示语细化、增加明文显示；
3. 用户点击PS中编辑判断如果与文件文件不符，确认弹窗提示；
4. 上传中、下载中文件不让用户去操作下载、删除等动作
5. 上传文件的逻辑代码优化
6. 修复保存上传文件时，文件名为空
7. 修复用户刷新之后无法看到上传中、下载中的文件
8. 图片组件封装

## 4.0.6

1. 基础组件封装统一注册、优化
2. 天枢盘功能组件优化、提升重用率、维护性
3. 账密登陆改为OpenID网页跳转
4. 优化代码、删除 余文件,group列表更名bug修复
5. 外部账号隐藏天枢盘Tab
6. 素材库下载按钮增加埋点
7. 天枢盘涉及POPO用户分享文件、添加组员、退出小组选择转移成员等重新封装

## 5.0.0

1. 素材库预览图增加名称跟尺寸信息，并且预览图悬浮位置重新计算优化
2. 素材库列表增加放大缩小功能
3. 素材库条件筛选框打开关闭优化
4. 素材库上传素材花费时间优化
5. 素材库增加最近下载、最常下载功能；
6. 网盘组别列表的图片缩略图压缩展示
7. 网盘创建名称、保存文件弹窗、与我共享组别显示等交互文案优化，部分样式优化;
8. 网盘增加文件置入功能；置入通用方法优化，防止调用死循环;
9. 网盘保存文件时，没有小组数据支持立即创建；
10. 网盘搜索取消返回上一个层级
11. 网盘客户端下载文件方法统一封装；上传文件统一方法优化；
12. 网盘增加点击埋点；
13. 通用能力：
    1. Icon组件封装，放到IconFont库管理;
    2. 日志信息完善，写入按照顺序;
    3. 增加打包版本时间戳；
    4. 通用按钮组件增加进度条功能，loading功能；

